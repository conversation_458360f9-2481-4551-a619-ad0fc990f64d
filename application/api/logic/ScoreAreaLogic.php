<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------
namespace app\api\logic;

use app\api\model\Goods;
use think\Db;


class ScoreAreaLogic
{
    public static function scoreGoodsList($id, $page, $size)
    {
        $where[] = ['AG.del', '=', 0];
        $where[] = ['G.del', '=', 0];
        $where[] = ['G.status', '=', 1];
        $where[] = ['score_id', '=', $id];

        $goods = new Goods();
        $count = $goods->alias('G')
            ->join('score_goods AG', 'G.id = AG.goods_id')
            ->where($where)
            ->count();


        $list = $goods->alias('G')
            ->join('score_goods AG', 'G.id = AG.goods_id')
            ->where($where)
            ->page($page, $size)
            ->field('G.id,G.name,G.image,AG.price as price,sales_sum+virtual_sales_sum as sales_sum,G.market_price,AG.score_id')
            ->select();

        $more = is_more($count, $page, $size);  //是否有下一页

        //获得前一天的贡献值兑换比例
        $day = date('Y-m-d',strtotime('-1 day'));
        $ratio_data = Db::name('model2_ratio')->where(['date'=>$day])->find();
        //如果没有记录，比例就是1:1000
        if(empty($ratio_data)){
            $day_ratio = 1 / 1000;
        }else{
            $day_ratio = $ratio_data['ratio'];
        }
        $onescore = bcdiv(1,$day_ratio,6);
        foreach($list as &$item){
            $item['score'] = bcmul($item['price'],$onescore,0);
        }

        $data = [
            'list'          => $list,
            'page_no'       => $page,
            'page_size'     => $size,
            'count'         => $count,
            'more'          => $more
        ];

        return $data;
    }

    public static function updateScore($order_id){
        $order = Db::name('order')->where(['id'=>$order_id])->find();
    }
}