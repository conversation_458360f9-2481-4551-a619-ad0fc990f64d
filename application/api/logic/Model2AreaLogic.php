<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------
namespace app\api\logic;

use app\api\model\Goods;
use think\Db;


class Model2AreaLogic
{
    public static function model2GoodsList($id, $page, $size)
    {
        $where[] = ['AG.del', '=', 0];
        $where[] = ['G.del', '=', 0];
        $where[] = ['G.status', '=', 1];
        $where[] = ['model2_id', '=', $id];

        $goods = new Goods();
        $count = $goods->alias('G')
            ->join('model2_goods AG', 'G.id = AG.goods_id')
            ->where($where)
            ->count();


        $list = $goods->alias('G')
            ->join('model2_goods AG', 'G.id = AG.goods_id')
            ->where($where)
            ->page($page, $size)
            ->field('G.id,G.name,G.image,G.min_price as price,sales_sum+virtual_sales_sum as sales_sum,G.market_price,AG.model2_id')
            ->select();

        $more = is_more($count, $page, $size);  //是否有下一页
        $data = [
            'list'          => $list,
            'page_no'       => $page,
            'page_size'     => $size,
            'count'         => $count,
            'more'          => $more
        ];

        return $data;
    }

    public static function updateModel2($order_id){
        //开启事务
        Db::startTrans();
        try{
            //获得订单详情
            $order = Db::name('order')
                    ->alias('O')
                    ->join('user u','O.user_id = u.id')
                    ->where(['O.id'=>$order_id])
                    ->field('O.*,u.user_money,u.first_leader,u.score')
                    ->find();
            //计算商品单价
            $signle_price = bcdiv($order['total_amount'],$order['total_num'],2);
            //获得昨天比例
            $yesterday = date('Y-m-d',strtotime('-1 day'));
            $ratio_data = Db::name('model2_ratio')->where(['date'=>$yesterday])->find();
            //如果没有记录，比例就是1:1000

            if(empty($ratio_data)){
                $yesterday_ratio = 1 / 1000;
            }else{
                $yesterday_ratio = $ratio_data['ratio'];
            }

            $score_log = [];
            //获得购买人的上级
            $parent = Db::name('user')->where(['id'=>$order['first_leader']])->find();
            //如果有上级
            if(!empty($parent)){
                //获得上级总共有多少个下级
                $child = Db::name('user')->where(['first_leader'=>$parent['id']])->column('id');
                //获得所有下级模式二有效订单数量
                $where = [];
                $where[] = ['order_type','=',5];
                $where[] = ['order_status','=',3];
                $where[] = ['id','<>',$order_id];
                $child_order = Db::name('order')->whereIn('user_id',$child)->where($where)->sum('total_num');
                $account_log = [];
                $parentmoney = $parent['user_money'];
                for($i = $child_order+1;$i<=($child_order+$order['total_num']);$i++){
                    $money = 0;
                    //如果次数是3的倍数,2的倍数，1的倍数，发放奖励
                    if($i % 3 == 0){
                        $ratio = 100;
                        $money = $signle_price * 1;
                        $remark = '新用户下单分享奖励3';
                    }else if(($i-1) % 3 == 0){
                        $ratio = 20;
                        $money = $signle_price * 0.2;
                        $remark = '新用户下单分享奖励2';
                    }else if(($i-2) % 1 == 0){
                        $ratio = 30;
                        $money = $signle_price * 0.3;
                        $remark = '新用户下单分享奖励1';
                    }
                    $parentmoney = bcadd($parentmoney,$money,2);
                    $account_log[] = [
                        'user_id' => $parent['id'],
                        'log_sn' => createSn('account_log','log_sn','',4),
                        'source_type' => 503,
                        'source_id' => $order_id,
                        'source_sn'=>$order['order_sn'],
                        'change_amount'=>$money,
                        'left_amount'=>$parentmoney,
                        'remark' => $remark,
                        'change_type'=>1,
                        'create_time' => time()
                    ];
                }
                Db::name('user')->where(['id'=>$parent['id']])->update(['user_money'=>$parentmoney]);
                //获得10块的贡献值奖励
                $scoremoney = 10;
                $integral = bcdiv($scoremoney,$yesterday_ratio,0);
                $parentscore = $parent['score'];
                for($i=0;$i<$order['total_num'];$i++){
                    $parentscore = bcadd($parentscore,$integral,2);
                    //记录贡献值的获取
                    $score_log[] = [
                        'user_id' => $parent['id'],
                        'log_sn' => createSn('score_log','log_sn','',4),
                        'source_type' => 102,
                        'source_id' => $order_id,
                        'source_sn'=>$order['order_sn'],
                        'change_amount'=>$integral,
                        'left_amount'=>$parentscore,
                        'remark' => '恭喜获得余额'.$integral,
                        'change_type'=>1,
                        'create_time' => time()
                    ];
                }
                Db::name('user')->where(['id'=>$parent['id']])->update(['score'=>$parentscore]);
                //记录一下模式二的奖励
                if(!empty($account_log)){
                    Db::name('account_log')->insertAll($account_log);
                }
            }
            //本人获得30元的等额贡献值奖励
            $usermoney = 30;
            $score = bcdiv($usermoney,$yesterday_ratio,0);
            $myscore = $order['score'];
            for($i=0;$i<$order['total_num'];$i++){
                $myscore = bcadd($myscore,$score,2);
                //记录贡献值的获取
                $score_log[] = [
                    'user_id' => $order['user_id'],
                    'log_sn' => createSn('score_log','log_sn','',4),
                    'source_type' => 101,
                    'source_id' => $order_id,
                    'source_sn'=>$order['order_sn'],
                    'change_amount'=>$score,
                    'left_amount'=>$myscore,
                    'remark' => '模式二下单，获得30元等额贡献值',
                    'change_type'=>1,
                    'create_time' => time()
                ];
            }
            Db::name('user')->where(['id'=>$order['user_id']])->update(['score'=>$myscore]);
            //将数据插入数据库
            if(!empty($score_log)){
                Db::name('score_log')->insertAll($score_log);
            }
            Db::commit();
        }catch(\Exception $e){
            Db::rollback();
            dump($e);die;
            throw new \Exception($e);
        }
    }

}