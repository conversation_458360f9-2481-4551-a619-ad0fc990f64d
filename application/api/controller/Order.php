<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\api\controller;

use app\api\logic\OrderLogic;
use app\common\model\Client_;
use app\common\server\ConfigServer;
use app\common\server\WechatMiniExpressSendSyncServer;
use think\Db;

/**
 * 订单
 * Class Order
 * @package app\api\controller
 */
class Order extends ApiBase
{

    public $like_not_need_login = ['confirm'];
    //订单列表
    public function lists()
    {
        $type = $this->request->get('type', 'all');
        $order_list = OrderLogic::getOrderList($this->user_id, $type, $this->page_no, $this->page_size);
        $this->_success('获取成功', $order_list);
    }

    //下单接口
    public function buy()
    {
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $post['client'] = $this->client;
        $check = $this->validate($post, 'app\api\validate\Order.buy');
        if (true !== $check) {
            $this->_error($check);
        }

        $action = $post['action'];
        $info = OrderLogic::info($post, $this->user_id);

        if ($info['code'] == 0) {
            $this->_error($info['msg']);
        }

        if ($action == 'info') {
            $this->_success('', $info['data']);
        }
        
        $order = OrderLogic::add($this->user_id, $info['data'], $post);
        return $order;
    }


    //订单详情
    public function detail()
    {
        $order_id = $this->request->get('id');
        $follow_id = $this->request->get('follow_id');
        if (!$order_id){
            $this->_error('请选择订单');
        }
        //print_r($follow_id);
        $order_detail = OrderLogic::getOrderDetail($order_id, $this->user_id,$follow_id);
        //print_r($order_detail);
        if (!$order_detail) {
            $this->_error('订单不存在了!', '');
        }
        $this->_success('获取成功', $order_detail);
    }
    
    /**
     * @notes 微信确认收货 获取详情
     * @return \think\response\Json
     * <AUTHOR>
     * @datetime 2023-09-05 09:51:41
     */
    function wxReceiveDetail()
    {
        return $this->_success('获取成功', OrderLogic::wxReceiveDetail(input('order_id/d'), $this->user_id));
    }

    //取消订单
    public function cancel()
    {
        $order_id = $this->request->post('id');
        if (empty($order_id)) {
            $this->_error('参数错误');
        }
        return OrderLogic::cancel($order_id, $this->user_id);
    }


    //删除订单
    public function del()
    {
        $order_id = $this->request->post('id');
        if (empty($order_id)) {
            $this->_error('参数错误');
        }
        return OrderLogic::del($order_id, $this->user_id);
    }


    //确认订单
    public function confirm()
    {
        $order_id = $this->request->post('id');
        if (empty($order_id)) {
            $this->_error('参数错误');
        }
        // 检测上级最早订单是否确认收货
        $parentOrder = OrderLogic::checkConfirm($this->user_id);
        // 如果上级订单是false，则提示
        if ($parentOrder === false) {
            $this->_error('上级订单未确认收货，无法确认收货');
        }
        return OrderLogic::confirm($order_id, $this->user_id);
    }


    public function orderTraces()
    {
        $order_id = $this->request->get('id');
        $tips = '参数错误';
        if ($order_id) {
            $traces = OrderLogic::orderTraces($order_id, $this->user_id);
            if ($traces) {
                $this->_success('获取成功', $traces);
            }
            $tips = '暂无物流信息';
        }
        $this->_error($tips);
    }


    /**
     * @notes 核销订单列表
     * <AUTHOR>
     * @date 2021/8/18 3:58 下午
     */
    public function verificationLists()
    {
        $type = $this->request->get('type',\app\common\model\Order::NOT_WRITTEN_OFF);
        $lists = OrderLogic::verificationLists($type, $this->user_id, $this->page_no, $this->page_size);
        $this->_success('获取成功', $lists);
    }


    /**
     * @notes 提货核销
     * <AUTHOR>
     * @date 2021/8/18 4:36 下午
     */
    public function verification()
    {
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $check = $this->validate($post, 'app\api\validate\Order.verification');
        if (true !== $check) {
            $this->_error($check);
        }
        $result = OrderLogic::verification($post);
        $this->_success('获取成功',$result);
    }

    /**
     * @notes 确认提货
     * <AUTHOR>
     * @date 2021/8/18 7:02 下午
     */
    public function verificationConfirm()
    {
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $result = OrderLogic::verificationConfirm($post);
        if (true !== $result) {
            $this->_error($result);
        }
        $this->_success('提货成功');
    }

    /**
     * @notes 获取配送方式
     * <AUTHOR>
     * @date 2021/8/19 7:17 下午
     */
    public function getDeliveryType()
    {
        $data = OrderLogic::getDeliveryType();
        $this->_success('获取成功',$data);
    }
    
    /**
     * @notes 微信同步发货 查询
     * @return \think\response\Json
     * <AUTHOR>
     * @datetime 2023-09-07 15:27:17
     */
    function wechatSyncCheck()
    {
        $id     = $this->request->get('id');
        
        $order  = \app\common\model\Order::where('id', $id)->where('user_id', $this->user_id)->findOrEmpty();
        
        $result = WechatMiniExpressSendSyncServer::wechatSyncCheck($order);
        
        if (! $result) {
            $this->_error('获取失败');
        }
    
        $this->_success('成功', $result);
    }
    /**
     * User: 意象信息科技 mjf
     * Desc: 更新拼团参与状态
     * @param $found_id
     */
    // public static function updateModel1Status($found_id){
    //     $found = Model1Found::get($found_id);
    //     //人数凑齐,拼团成功
    //     if ($found['join'] == $found['need']){
    //         $found->status = Model1::STATUS_SUCCESS;
    //         $found->model1_end_time = time();
    //         $found->save();
    //         //重新开团
    //         try{
    //             //查找最新的贡献度满的人
    //             $followlist = Db::name('model1_follow')
    //                         ->alias('mf')
    //                         ->join('user u','u.id = mf.follow_user_id','left')
    //                         ->where(['found_id' => $found_id])
    //                         ->order('mf.contribution_point desc,mf.follow_time asc')
    //                         ->field('mf.*,u.first_leader')
    //                         ->select();
                
    //             $single_fly_commission = ConfigServer::get('model1', 'single_fly_commission', 0);
    //             $single_fly_commission = floatval($single_fly_commission) > 0 ? bcdiv($single_fly_commission,100,2) : 2;
    //             $winner = $followlist[0];
    //             $followuserids = [];
    //             foreach ($followlist as $item){
    //                 $followuserids[] = $item['follow_user_id'];
    //             }
    //             $found = Db::name('model1_found')->where(['id' => $found_id])->find();
    //             //获得商品的价格
    //             $order_goods = Db::name('order_goods')
    //                         ->alias('og')
    //                         ->join('order o','o.id = og.order_id','left')
    //                         ->where(['og.order_id' => $winner['order_id']])
    //                         ->field('og.*,o.order_sn,o.total_amount')
    //                         ->find();
    //             $user = Db::name('user')->where('id',$winner['follow_user_id'])->find();
    //             $getmoney = bcmul($order_goods['total_amount'],$single_fly_commission,2);
    //             $winnermoney = bcadd($user['user_money'],$getmoney,2);//获得200%的返佣
                
    //             //记录一下分钱的情况
    //             Db::name('money_log')->insert([
    //                 'user_id' => 0,
    //                 'receiver' => $winner['follow_user_id'],
    //                 'order_id' => 0,
    //                 'found_id' => 0,
    //                 'price' => $getmoney,
    //                 'create_time' => time(),
    //                 'status' => 1
    //             ]);
                
    //             $winnerordermoney = bcsub($winnermoney,$order_goods['total_amount'],2);//在扣除一单的金额
    //             Db::name('user')->where('id',$winner['follow_user_id'])->update(['user_money'=>$winnerordermoney]);

    //             Hook::listen('notice', [
    //                 'user_id'  => $winner['follow_user_id'],
    //                 'earnings' => $getmoney,
    //                 'scene'    => NoticeSetting::GET_EARNINGS_NOTICE,
    //             ]);
    //             //将user的贡献度清零
    //             Db::name('user')->whereIn('id',$followuserids)->update(['contribution_point' => 0]);
    //             //发送成团通知
    //             $noticelist = [];
    //             foreach($followlist as $item){
    //                 $noticelist[] = [
    //                     'user_id' => $item['follow_user_id'] ?? 0,
    //                     'title'   => '拼团成功',
    //                     'content' => $item['follow_user_nickname'].'，您好，恭喜您完成拼团',
    //                     'scene'   => 113,
    //                     'receive_type' => 2,
    //                     'send_type'    => 1,
    //                     'extra'        => '',
    //                     'create_time'  => time()
    //                 ];
    //             }
    //             Db::name('notice')->insertAll($noticelist);
    //             //单飞人成团
    //             //判断是否有上级的未完成的团
    //             $pfound = Db::name('model1_found')
    //                 ->where(['user_id' => $user['first_leader'],'status' => 0,'model1_id' => $winner['model1_id']])
    //                 ->order('found_time desc')
    //                 ->find();
    //             if(empty($pfound)){
    //                 $result = Db::name('model1_found')->insertGetId([
    //                     'sn'=>createSn('model1_found','sn', '', 4),
    //                     'found_time' => time(),
    //                     'user_id' => $winner['follow_user_id'],
    //                     'model1_id'=>$winner['model1_id'],
    //                     'nickname'=>$winner['follow_user_nickname'],
    //                     'order_id'=>$winner['order_id'],
    //                     'need' => $found['need'],
    //                     'join'=>0,
    //                     'status'=>0
    //                 ]);
    //                 $pfound = Db::name('model1_found')->where(['id' => $result])->find();
    //             }
    //             //重新生成一单订单
    //             $order = Db::name('order')->where('id','=',$winner['order_id'])->find();
    //             $orderGoods = Db::name('order_goods')->where(['order_id' => $winner['order_id']])->select();
    //             unset($order['id']);
    //             //修改参数
    //             $order['order_sn'] = createSn('order','order_sn','',4);
    //             $order['pay_time'] = time();
    //             $order['pay_way'] = 3;
    //             $order['model1_id'] = $winner['model1_id'];
    //             $order['model1_found_id'] = $pfound['id'];
    //             $order['create_time'] = time();
    //             //增加新数据
    //             $order_id = Db::name('order')->insertGetId($order);
    //             foreach($orderGoods as &$orderinfo){
    //                 $orderinfo['order_id'] = $order_id;
    //                 $orderinfo['create_time'] = time();
    //                 unset($orderinfo['id']);
    //             }
    //             Db::name('order_goods')->insertAll($orderGoods);
    //             Db::name('account_log')->insertAll([[
    //                 'user_id' => $winner['follow_user_id'],
    //                 'log_sn' => createSn('account_log','log_sn','',4),
    //                 'source_type' => 502,
    //                 'source_id' => $winner['order_id'],
    //                 'source_sn'=>$order_goods['order_sn'],
    //                 'change_amount'=>$getmoney,
    //                 'left_amount'=>$winnermoney,
    //                 'remark' => '完成开团，单飞获得200%',
    //                 'change_type'=>1,
    //                 'create_time' => time()
    //             ],[
    //                 'user_id' => $winner['follow_user_id'],
    //                 'log_sn' => createSn('account_log','log_sn','',4),
    //                 'source_type' => 103,
    //                 'source_id' => $order_id,
    //                 'source_sn'=>$order['order_sn'],
    //                 'change_amount'=>$order['total_amount'],
    //                 'left_amount'=>$winnerordermoney,
    //                 'remark' => '完成开团，单飞后重新下单',
    //                 'change_type'=>2,
    //                 'create_time' => time()
    //             ]]);
    //             self::addModel1Follow($order_id, $pfound['id'], $winner['follow_user_id']);
    //             self::updateModel1Status($pfound['id']);

    //             unset($followlist[0]);
    //             $followlist = array_values($followlist);
    //             //第一个三人团
    //             //followlist,按照follow_time排序,正序
    //             // // 提取 createtime 列的值
    //             $createtimes = array_column($followlist, 'follow_time');

    //             // 使用 array_multisort 排序（SORT_ASC 表示升序）
    //             array_multisort($createtimes, SORT_ASC, $followlist);
    //             $team1leader = $followlist[0];
    //             $team1member1 = $followlist[2];
    //             $team1member2 = $followlist[count($followlist)-1];
    //             unset($followlist[count($followlist)-1]);
    //             unset($followlist[0]);
    //             unset($followlist[2]);
    //             //第一个三人团成团
    //             $result = Db::name('model1_found')->insertGetId([
    //               'sn'=>createSn('model1_found','sn', '', 4),
    //                 'found_time' => time(),
    //                 'user_id' => $team1leader['follow_user_id'],
    //                 'model1_id'=>$team1leader['model1_id'],
    //                 'nickname'=>$team1leader['follow_user_nickname'],
    //                 'order_id'=>$team1leader['order_id'],
    //                 'need' => $found['need'],
    //                 'join'=>3,
    //                 'status'=>0
    //             ]);
    //             Db::name('model1_follow')->insertAll([[
    //                 'follow_user_id' => $team1leader['follow_user_id'],
    //                 'follow_user_nickname' => $team1leader['follow_user_nickname'],
    //                 'follow_user_avatar' => $team1leader['follow_user_avatar'],
    //                 'follow_time' => time(),
    //                 'order_id' => $team1leader['order_id'],
    //                 'found_id' => $result,
    //                 'model1_id' => $team1leader['model1_id'],
    //                 'type' => 1,
    //                 'status' => 0,
    //                 'contribution_point'=>$team1leader['contribution_point']
    //             ],[
    //                 'follow_user_id' => $team1member1['follow_user_id'],
    //                 'follow_user_nickname' => $team1member1['follow_user_nickname'],
    //                 'follow_user_avatar' => $team1member1['follow_user_avatar'],
    //                 'follow_time' => time(),
    //                 'order_id' => $team1member1['order_id'],
    //                 'found_id' => $result,
    //                 'model1_id' => $team1member1['model1_id'],
    //                 'type' => 0,
    //                 'status' => 0,
    //                 'contribution_point'=>$team1member1['contribution_point']
    //             ],[
    //                 'follow_user_id' => $team1member2['follow_user_id'],
    //                 'follow_user_nickname' => $team1member2['follow_user_nickname'],
    //                 'follow_user_avatar' => $team1member2['follow_user_avatar'],
    //                 'follow_time' => time(),
    //                 'order_id' => $team1member2['order_id'],
    //                 'found_id' => $result,
    //                 'model1_id' => $team1member2['model1_id'],
    //                 'type' => 0,
    //                 'status' => 0,
    //                 'contribution_point'=>$team1member2['contribution_point']
    //             ]]);
    //             $followlist = array_values($followlist);
    //             $team2member2 = $followlist[count($followlist)-1];
    //             $team2leader = $followlist[0];
    //             $team2member1 = $followlist[1];
    //             //第二个三人团
                
    //             //第一个三人团成团
    //             $result = Db::name('model1_found')->insertGetId([
    //                  'sn'=>createSn('model1_found','sn', '', 4),
    //                  'found_time' => time(),
    //                  'user_id' => $team2leader['follow_user_id'],
    //                  'model1_id'=>$team2leader['model1_id'],
    //                  'nickname'=>$team2leader['follow_user_nickname'],
    //                  'order_id'=>$team2leader['order_id'],
    //                  'need' => $found['need'],
    //                  'join'=>3,
    //                  'status'=>0
    //              ]);
    //              Db::name('model1_follow')->insertAll([[
    //                  'follow_user_id' => $team2leader['follow_user_id'],
    //                  'follow_user_nickname' => $team2leader['follow_user_nickname'],
    //                  'follow_user_avatar' => $team2leader['follow_user_avatar'],
    //                  'follow_time' => time(),
    //                  'order_id' => $team2leader['order_id'],
    //                  'found_id' => $result,
    //                  'model1_id' => $team2leader['model1_id'],
    //                  'type' => 1,
    //                  'status' => 0,
    //                 'contribution_point'=>$team2leader['contribution_point']
    //              ],[
    //                  'follow_user_id' => $team2member1['follow_user_id'],
    //                  'follow_user_nickname' => $team2member1['follow_user_nickname'],
    //                  'follow_user_avatar' => $team2member1['follow_user_avatar'],
    //                  'follow_time' => time(),
    //                  'order_id' => $team2member1['order_id'],
    //                  'found_id' => $result,
    //                  'model1_id' => $team2member1['model1_id'],
    //                  'type' => 0,
    //                  'status' => 0,
    //                 'contribution_point'=>$team2member1['contribution_point']
    //              ],[
    //                  'follow_user_id' => $team2member2['follow_user_id'],
    //                  'follow_user_nickname' => $team2member2['follow_user_nickname'],
    //                  'follow_user_avatar' => $team2member2['follow_user_avatar'],
    //                  'follow_time' => time(),
    //                  'order_id' => $team2member2['order_id'],
    //                  'found_id' => $result,
    //                  'model1_id' => $team2member2['model1_id'],
    //                  'type' => 0,
    //                  'status' => 0,
    //                 'contribution_point'=>$team2member2['contribution_point']
    //              ]]);
    //         }catch(Exception $e){
    //             throw new Exception($e);
    //         }
    //         Model1Follow::where(['found_id' => $found_id])->update(['status' => Model1::STATUS_SUCCESS, 'model1_end_time' => time()]);
    //     }
    // }
}