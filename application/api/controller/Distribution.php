<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------


namespace app\api\controller;


use app\api\logic\DistributionLogic;
use app\common\model\User;

class Distribution extends ApiBase
{
    public $like_not_need_login = ['fixAncestorRelation'];
    /**
     * 填写邀请码
     */
    public function code()
    {
        $code = $this->request->post('code');
        $data = [
            'user_id' => $this->user_id,
            'code' => $code,
        ];
        $result = $this->validate($data, 'app\api\validate\DistributionCode');
        if ($result !== true) {
            $this->_error($result, [], 0, 0);
        }
        $result = DistributionLogic::code($code, $this->user_id);
        if ($result !== true) {
            $this->_error($result, [], 0, 0);
        }
        $this->_success();
    }

    /**
     * 分销会员申请
     */
    public function apple()
    {
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $result = $this->validate($post, 'app\api\validate\DistributionApply');
        if ($result !== true) {
            $this->_error($result);
        }
        $result = DistributionLogic::apple($post,$this->user_id);
        if ($result !== true) {
            $this->_error($result);
        }
        $this->_success();
    }

    /**
     * 最新的分销会员申请详情
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function appleDetail()
    {
        $this->_success('',DistributionLogic::appleDetail($this->user_id));
    }


    /**
     * User: 意象信息科技 mjf
     * Desc: 我的分销上级
     */
    public function myLeader()
    {
        $this->_success('', DistributionLogic::myLeader($this->user_id));
    }


    /**
     * 分销推广主页信息
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function index()
    {
        $this->_success('', DistributionLogic::index($this->user_id));
    }



    /**
     * 分销订单
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function order()
    {
        $get = $this->request->get();
        $this->_success('', DistributionLogic::order($this->user_id, $get, $this->page_no, $this->page_size));
    }


    /**
     * 月度账单
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function monthBill()
    {
        $this->_success('', DistributionLogic::getMonthBill($this->user_id, $this->page_no, $this->page_size));
    }

    /**
     * 月度账单明细
     */
    public function monthDetail()
    {
        $get = $this->request->get();
        $this->_success('', DistributionLogic::getMonth($get, $this->user_id, $this->page_no, $this->page_size));
    }


    /**
     * Notes: 分销会员页面判断
     * <AUTHOR> 18:45)
     */
    public function check()
    {
        $distribution = \app\common\model\Distribution::where('user_id', $this->user_id)->findOrEmpty()->toArray();
        if (!empty($distribution) && $distribution['is_distribution'] == 1) {
            $this->_success('', '', 10001);//已是分销会员
        } else {
            $this->_success('', '',20001);//未是分销会员
        }
    }

    /**
     * 修复旧的关系链
     */
    public function fixAncestorRelation()
    {
        $result = DistributionLogic::fixAncestorRelation();
        if ($result['flag']) {
            $this->_success($result['msg']);
        }
        $this->_error($result['msg']);
    }


    /**
     * @notes 获取背景海报
     * @return Json
     * <AUTHOR>
     * @date 2021/11/29 11:35
     */
    public function getPoster()
    {
        $result = DistributionLogic::getPoster();
        $this->_success('',$result);
    }
}