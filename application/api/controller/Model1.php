<?php

namespace app\api\controller;


use app\api\logic\Model1Logic;
use think\Db;

/**
 * 拼团
 * Class Model1
 * @package app\api\controller
 */
class Model1 extends ApiBase
{
    public $like_not_need_login = ['detail','getModel1info'];

    // 拼团商品列表
    public function model1GoodsList()
    {
        $lists = Model1Logic::getModel1GoodsList($this->page_no, $this->page_size);
        if ($lists) {
            $this->_success('获取成功', $lists);
        } else {
            $this->_error(Model1Logic::getError());
        }
    }

    //参与拼团活动
    public function buy()
    {
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $post['client'] = $this->client;
        $check = $this->validate($post, 'app\api\validate\Model1.add');
        if (true !== $check) {
            $this->_error($check);
        }

        Model1Logic::setUser($this->user_id);
        Model1Logic::setModel1Id($post['model1_id']);
        Model1Logic::setModel1GoodsItem($post['item_id']);
        Model1Logic::setModel1GoodsNum($post['goods_num']);
        Model1Logic::setIntegralConfig();
        $info = Model1Logic::calculateInfo($post, $this->user_id);
        if ($info === false){
            $this->_error(Model1Logic::getError());
        }

        if($post['action'] == 'info'){
            $this->_success('', $info);
        }

        $order = Model1Logic::buy($this->user_id, $info, $post);
        if ($order === false){
            $this->_error(Model1Logic::getError());
        }
        $this->_success('', $order);
    }


    //验证拼团
    public function check()
    {
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $check = $this->validate($post, 'app\api\validate\Model1.check');
        if (true !== $check) {
            $this->_error($check);
        }
        $this->_success();
    }

    public function detail()
    {
        $found_id = $this->request->post('found_id');
        $model1_id = $this->request->post('model1_id');
        $user_id = $this->user_id;
        
        $detail = Model1Logic::getFoundInfo($found_id,$model1_id,$user_id);
        if ($detail === false){
            $this->_error(Model1Logic::getError());
        }
        $this->_success('', $detail);
    }

    public function rebate()
    {
        $found_id = $this->request->post('found_id');
        $model1_id = $this->request->post('model1_id');
        $user_id = $this->user_id;
        $data = Model1Logic::rebate($found_id,$model1_id,$user_id);
        $this->_success('', $data);
    }

    //获得组团详情
    public function getModel1info(){
        $user_id = input('user_id');
        $goods_id = input('goods_id');
        //查看用户是不是有已经下单，除取消外，不能再下单
        $order = Db::name('order')->where(['user_id'=>$user_id,'order_type'=>5,'order_status'=>['in','0,1,2,3']])->select();
        if(!empty($order)){
            $this->_error('您有未完成订单，请勿重复下单');
        }
        $found = Db::name('model1_found')->where(['user_id'=>$user_id,'status'=>0])->order('found_time asc')->find();
        if(!empty($found)){
            $this->_success('',['hasgroup'=>1,'cangroup'=>0,'found_id'=>$found['id']]);
        }
        $user = Db::name('user')->where('id',$user_id)->find();

        $data = [];
        if(intval($user['first_leader']) == 0){
            $data['hasgroup'] = 0;//没有可参与的团
        }
        $model1_id = Db::name('model1_goods_item')->where('goods_id',$goods_id)->value('model1_id');
        $follow1 = Db::name('model1_follow')->where(['model1_id'=>$model1_id,'follow_user_id'=>$user_id,'status'=>0])->order('follow_time desc')->find();
        if(empty($follow1)){
            $data['cangroup'] = 1;//可以购买创建团
            $data['hasgroup'] = 0;
        }else{
            $data['found_id'] = $follow1['found_id'];
            $data['cangroup'] = 0;
            $data['hasgroup'] = 1;
        }
        if(empty($follow1)){
            $follow = Db::name('model1_follow')->where(['model1_id'=>$model1_id,'follow_user_id'=>$user['first_leader'],'status'=>0])->order('follow_time desc')->find();
            if(empty($follow)){
                $data['hasgroup'] = 0;
                $data['cangroup'] = 1;
            }else{
                $data['found_id'] = $follow['found_id'];
                $data['cangroup'] = 1;
                $data['hasgroup'] = 1;
            }
        }
        $this->_success('', $data);
    }
}