<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\api\controller;


use app\api\model\Order;
use app\common\model\Order as CommonOrder;
use app\common\model\Client_;
use app\common\server\AliPayServer;
use app\common\server\ConfigServer;
use app\common\server\WeChatPayServer;
use app\common\server\WeChatServer;
use app\common\logic\PaymentLogic;
use app\common\model\Pay;
use think\Db;

/**
 * 支付逻辑
 * Class Payment
 * @package app\api\controller
 */
class Payment extends ApiBase
{

    public $like_not_need_login = ['aliNotify', 'notifyMnp', 'notifyOa', 'notifyApp'];


    /**
     * Notes: 预支付
     * <AUTHOR> 14:33)
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function prepay()
    {
        $post = $this->request->post();
        if(!isset($post['from']) || !isset($post['order_id']) || !isset($post['pay_way'])) {
            $this->_error('参数缺失');
        }
        switch ($post['from']) {
            case 'order':
                $order = Order::get($post['order_id']);
                if ($order['order_status'] == CommonOrder::STATUS_CLOSE || $order['del'] == 1) {
                    $this->_error('订单已关闭');
                }
                break;
            case 'recharge':
                $order = Db::name('recharge_order')->where(['id' => $post['order_id']])->find();
                break;
        }
        //找不到订单
        if (empty($order)) {
            $this->_error('订单不存在');
        }
        // 变更支付方式
        $order['pay_way'] = $post['pay_way'];
        //已支付
        if ($order['pay_status'] == Pay::ISPAID) {
            $this->_success('支付成功', ['order_id' => $order['id']], 10000);
        }

        $result = PaymentLogic::pay($post['from'], $order, $this->client);
        if (false === $result) {
            $this->_error(PaymentLogic::getError(), ['order_id' => $order['id']], PaymentLogic::getReturnCode());
        }

        if (PaymentLogic::getReturnCode() != 0) {
            $this->_success('', $result, PaymentLogic::getReturnCode());
        }

        $this->_success('', $result);
    }



    /**
     * Notes: pc端预支付 NATIVE
     * <AUTHOR> 16:03)
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function pcPrepay()
    {
        $post = $this->request->post();
        $order = Order::get($post['order_id']);
        $order['pay_way'] = $post['pay_way'];

        $return_msg = ['order_id' => $order['id'], 'order_amount' => $order['order_amount']];

        //找不到订单
        if (empty($order)) {
            $this->_error('订单不存在');
        }

        if ($order['order_status'] == CommonOrder::STATUS_CLOSE || $order['del'] == 1) {
            $this->_error('订单已关闭');
        }

        //已支付
        if ($order['pay_status'] == Pay::ISPAID) {
            $this->_success('支付成功', $return_msg, 10001);
        }

        $result = PaymentLogic::pcPay($order, $post['order_source']);

        if (false === $result) {
            $this->_error(PaymentLogic::getError(), $return_msg, PaymentLogic::getReturnCode());
        }

        if ($order['pay_way'] == Pay::BALANCE_PAY) {
            $this->_success('支付成功', $return_msg, PaymentLogic::getReturnCode());
        }

        $return_msg['data'] = $result;

        if (PaymentLogic::getReturnCode() != 0) {
            $this->_success('支付成功', $return_msg, PaymentLogic::getReturnCode());
        }

        $this->_success('支付成功', $return_msg);
    }



    /**
     * Notes: 小程序回调
     * <AUTHOR> 14:34)
     */
    public function notifyMnp()
    {
        $config = WeChatServer::getPayConfig(Client_::mnp);
        return WeChatPayServer::notify($config);
    }


    /**
     * Notes: 公众号回调
     * <AUTHOR> 14:34)
     */
    public function notifyOa()
    {
        $config = WeChatServer::getPayConfig(Client_::oa);
        return WeChatPayServer::notify($config);
    }


    /**
     * Notes: APP回调
     * <AUTHOR> 14:34)
     */
    public function notifyApp()
    {
        $config = WeChatServer::getPayConfig(Client_::ios);
        return WeChatPayServer::notify($config);
    }


    /**
     * Notes: 支付宝回调
     * <AUTHOR> 11:37)
     */
    public function aliNotify()
    {
        $data = $this->request->post();
        $result = (new AliPayServer())->verifyNotify($data);
        if (true === $result) {
            echo 'success';
        } else {
            echo 'fail';
        }
    }



    /**
     * Notes:
     * <AUTHOR> 11:36)
     * @return \think\Model[]
     */
    public function payway()
    {
        $params = $this->request->get();
        if(!isset($params['from']) || !isset($params['order_id'])) {
            return $this->_error('参数缺失');
        }
        if($params['from'] == 'order') {
            $order = Db::name('order')->where('id', $params['order_id'])->find();
        }else if($params['from'] == 'recharge') {
            $order = Db::name('recharge_order')->where('id', $params['order_id'])->find();
        }
        if($this->client == 1){
            if ($params['wxcode']) {
                $openid = WeChatServer::getOpenIdByWxCode($params['wxcode'], Client_::mnp);
                // 生成用户授权信息
                if (!empty($openid)) {
                    $auth = Db::name('user_auth')->where([
                        'user_id' => $order['user_id'],
                        'openid' => $openid,
                        'client' => Client_::mnp
                    ])->find();
                    if (!$auth) {
                        $authData = [
                            'user_id' => $order['user_id'],
                            'openid' => $openid,
                            'client' => Client_::mnp,
                            'create_time' => time(),
                        ];
                        Db::name('user_auth')->insert($authData);
                    }
                }
            }
            
        }
        
        $payModel = new Pay();
        $pay = $payModel->where(['status' => 1])->order('sort')->hidden(['config'])->select()->toArray();

        foreach ($pay as $k => &$item) {
            if ($item['code'] == 'wechat') {
                $item['extra'] = '微信快捷支付';
                $item['pay_way'] = Pay::WECHAT_PAY;
            }

            if ($item['code'] == 'balance') {
                $user_money = Db::name('user')->where(['id' => $this->user_id])->value('user_money');
                $item['extra'] = '可用余额:'.$user_money;
                $item['pay_way'] = Pay::BALANCE_PAY;
            }

            if ($item['code'] == 'alipay') {
                $item['extra'] = '';
                $item['pay_way'] = Pay::ALI_PAY;
            }

            if (in_array($this->client, [Client_::mnp, Client_::oa]) && $item['code'] == 'alipay') {
                unset($pay[$k]);
            }
            if($params['from'] == 'recharge' && $item['code'] == 'balance') {
                unset($pay[$k]);
            }

        }
        // 订单自动取消时间
        $cancelTime = ConfigServer::get('trading', 'order_cancel');
        if(empty($cancelTime)) {
            // 前端检测为0时不显示倒计时
            $cancelTime = 0;
        }else{
            // 订单创建时间 + 后台设置自动关闭未付款订单时长
            $cancelTime = $order['create_time'] + intval($cancelTime) * 60;
        }
        $data = [
            'pay' => array_values($pay),
            'order_amount' => $order['order_amount'],
            'cancel_time' => $cancelTime,
        ];
        $this->_success('', $data);
    }

}