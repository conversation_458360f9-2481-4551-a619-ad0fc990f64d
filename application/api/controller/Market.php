<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------


namespace app\api\controller;
use app\api\logic\LoginLogic;
use app\common\model\DistributionOrderGoods;
use app\common\server\ConfigServer;
use think\Db;
use app\api\model\User;

class Market extends ApiBase
{
    public function allocationPid()   //为每一位用户分配一个pid
    {
        $user = new \app\api\model\User();
        $found_id = $this->request->post('found_id');
        $data = Db::name('model1_follow')->where(['status'=>1,'found_id'=>$found_id])->order('follow_time asc')->select();

        if(count($data)!=7)
        {
            $this->_error('参数有误');
        }
        $user->where('id',$data[1]['follow_user_id'])->update(['pid'=>0]);
        $user->whereIn('id',[$data[2]['follow_user_id'],$data[6]['follow_user_id']])->update(['pid'=>$data[0]['follow_user_id']]);
        $user->whereIn('id',[$data[4]['follow_user_id'],$data[5]['follow_user_id']])->update(['pid'=>$data[3]['follow_user_id']]);
        $userData = Db::name('distribution')->alias('d')->join('distribution_level dl','dl.id=d.level_id','left')->where('user_id',$data[1]['follow_user_id'])->field('user_id,level_id,dl.id')->find();
        $orderGoods = Db::name('order_goods')->where('order_id',$data[0]['order_id'])->find();

        for($i=0;$i<7;$i++)
        {
            $count = Db::name('contribution_point')->where(['user_id'=>$data[$i]['follow_user_id'],'found_id'=>$found_id,'status'=>1])->count();
            switch ($count):
                case 2:
                    $datas = [
                        'sn' => createSn('distribution_order_goods', 'sn'),
                        'user_id' => $data[$i]['follow_user_id'],
                        'real_name' => User::where('id',$data[$i]['follow_user_id'])->value('nickname'),
                        'level_id' => $userData['level_id'],
                        'level' => 1,
                        'ratio' => 200,
                        'order_id' => $data[$i]['order_id'],
                        'order_goods_id' => $orderGoods['goods_id'],
                        'goods_num' => $orderGoods['goods_num'],
                        'money' => round(($orderGoods['total_pay_price'] * 200 / 100), 2),
                        'status' => 1,
                        'create_time' => time(),
                    ];
                    DistributionOrderGoods::create($datas);   //第一顺位继承人可以拿200%的佣金
                    break;
            endswitch;
        }
    }

    //验证是否能参团
    public function checkTeam()
    {
        $user_id = $this->request->post('user_id');
        $found_id = $this->request->post('found_id');
        $leader_id = Db::name('model1_found')->where(['id'=>$found_id,'type'=>1])->value('follow_user_id');
        if($leader_id == $user_id)
        {
            $this->_success('您是团长，请参团');
        }

        $pid= User::where('id',$user_id)->value('pid');
        if(!empty($pid) && $pid != $leader_id)
        {
            $this->_error('您无权参团');
        }
        $this->_success('请参团');
    }

    public function isAllowJoin()  //判断是否允许加入团
    {
        $user_id = $this->user_id;
        $id = Db::name('model1_follow')->where(['follow_user_id'=>$user_id,'status'=>0])->value('id');
        if(!empty($id))
        {
            $this->_error('您正在参与此活动');
        }
        $this->_success('可以参与此活动');
    }

}