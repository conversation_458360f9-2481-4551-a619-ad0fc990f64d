<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\api\controller;

use app\api\logic\WithdrawLogic;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;
use think\Db;
use think\facade\Log;
use app\common\logic\AccountLogLogic;
use app\common\model\AccountLog;

class Withdraw extends ApiBase
{
    public $like_not_need_login = ['querySign','notify'];
    //提现申请
    public function apply()
    {
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $check = $this->validate($post, 'app\api\validate\Withdraw.apply');
        if (true !== $check) {
            $this->_error($check);
        }
        return WithdrawLogic::apply($this->user_id, $post);
    }

    //提现配置
    public function config()
    {
        $data = WithdrawLogic::config($this->user_id);
        $this->_success('', $data);
    }


    //提现记录
    public function records()
    {
        $get = $this->request->get();
        $page = $this->request->get('page_no', $this->page_no);
        $size = $this->request->get('page_size', $this->page_size);
        $res = WithdrawLogic::records($this->user_id, $get, $page, $size);
        $this->_success('', $res);
    }


    //提现详情
    public function info()
    {
        $get = $this->request->get('');
        $check = $this->validate($get, 'app\api\validate\Withdraw.info');
        if (true !== $check) {
            $this->_error($check);
        }
        $this->_success('', WithdrawLogic::info($get['id'], $this->user_id));
    }
    //查询是否签约
    public function querySign()
    {
        $user_id = input('user_id');
        $res = WithdrawLogic::getData();
        if($res['code'] == 0){
            $this->_error($res['msg']);
        }
        $data = json_decode($res['data'],true);
        if($data['isSuccess'] == 'F'){
            $this->_error($data['errorMessage']);
        }
        $position = implode(',',$data['data'][0]['positionList']);
        $user = Db::name('user')->where('id',$user_id)->find();
        if(empty($user['truename']) || empty($user['id_card'])){
            $this->_error('请先完善用户信息');
        }
        $query_info = WithdrawLogic::querySign($user,$position);
        if($query_info['code'] == 0 && $query_info['msg'] == '有待签合同'){
            $sign_code = ConfigServer::get('withdraw', 'sign_code');
            $sign_code = UrlServer::getFileUrl($sign_code);
            $this->_error('有待签合同',$sign_code);
        }else if($query_info['code'] == 0 && $query_info['msg'] == 'success'){
            $this->_success('',$query_info['data']);
        }else if($query_info['code'] == 1){
            $this->_error($query_info['msg']);
        }
    }
    public function notify(){
        $params = input();
        $data['content'] = $params['data']['bizAESContent'];
        $data['secret'] = ConfigServer::get('withdraw','secret','ceshihuanjingyanshizhanghao00000');
        $data['iv'] = ConfigServer::get('withdraw','iv','0000000000000000');
        $content = $this->decrypt($data);
        $content = json_decode($content,true);
        $winfo = Db::name('withdraw_apply')->where('sn',$content['outerTradeNo'])->find();
        if(isset($content['tradeFailCode']) && !empty($content['tradeFailCode']) && $winfo['status'] == 2){
            //错误码：F0001到F0015
            $errCode = [
                'F0001'=>'交易失败',
                'F0002'=>'不存在此支付宝账户',
                'F0003'=>'存在多个支付宝同名账户',
                'F0004'=>'支付宝户名不匹配',
                'F0005'=>'支付宝账户未经过实名认证',
                'F0006'=>'收款支付宝账户格式有误',
                'F0007'=>'对方账号户名不符',
                'F0008'=>'对方账户状态异常',
                'F0009'=>'收款行银行受理失败',
                'F0010'=>'交易失败-可能二三类账户限额或账户状态异常',
                'F0011'=>'收款账户所在行暂不支持',
                'F0012'=>'对方账号不存在','F0013'=>'账户关联的证件有效期到期',
                'F0014'=>'超出发卡银行交易限额',
                'F0015'=>'交易处理失败，建议换一张卡重试或稍后再试',
                'F9999'=>'交易失败,具体原因暂不明确，需与银行确认收集'
            ];
            $errMsg = isset($errCode[$content['tradeFailCode']]) ? $errCode[$content['tradeFailCode']] : '交易失败';
            Db::name('withdraw_apply')->where('sn',$content['outerTradeNo'])->update(['status'=>4,'update_time'=>time(),'result'=>$errMsg]);
            Db::name('user')->where('id',$winfo['user_id'])->update(['user_money'=>Db::raw('user_money+'.$winfo['money'])]);
            //增加佣金变动记录
            AccountLogLogic::AccountRecord(
                $winfo['user_id'],
                $winfo['money'],
                1,
                AccountLog::fail_reduce_earnings,
                '',
                $winfo['id'],
                $winfo['sn']
            );
        }else if($content['notifyType'] == 'tradeResult' && $content['tradeResult'] == '交易成功' && $winfo['status'] == 2){
            Db::name('withdraw_apply')->where('sn',$content['outerTradeNo'])->update(['status'=>3,'update_time'=>time(),'transfer_time'=>time()]);
        }
        return 'success';
    }

      //AES解密
    public function decrypt($data)
    {
        return openssl_decrypt(urldecode($data['content']), "AES-256-CBC", $data['secret'], 0, $data['iv']);
    }
}