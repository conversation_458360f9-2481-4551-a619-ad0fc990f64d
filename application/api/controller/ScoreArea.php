<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------
namespace app\api\controller;

use app\api\logic\ScoreAreaLogic;
use think\Db;

class ScoreArea extends ApiBase
{
    public $like_not_need_login = ['scoreGoodsList','getScoreLog'];

    public function scoreGoodsList()
    {
        $id = $this->request->get('id');
        $list = ScoreAreaLogic::scoreGoodsList($id, $this->page_no, $this->page_size);
        $this->_success('获取成功', $list);
    }
    //获得用户的贡献值记录
    public function getScoreLog(){
        $user_id = $this->request->get('user_id');
        $list = Db::name('score_log')
            ->where('user_id',$user_id)
            ->order('id desc')
            ->limit(($this->page_no-1),$this->page_size)
            ->select();
        foreach ($list as $key => $value) {
            $list[$key]['create_time'] = date('Y-m-d H:i:s',$value['create_time']);
        }
        $this->_success('获取成功', $list);
    }
}