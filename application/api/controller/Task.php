<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------
namespace app\api\controller;
use think\Db;
class Task extends ApiBase{

    public $like_not_need_login = ['calcRatio'];
    //计算当天的兑换比例
    public function calcRatio(){
        $today = date('Y-m-d',strtotime('-1 day'));
        $starttime = strtotime($today);
        $endtime = strtotime($today.' 23:59:59');
        //获得昨天的数据
        $yesterday = date('Y-m-d',strtotime("-2 day"));
        $yesterday_data = Db::name('model2_ratio')->where('date',$yesterday)->find();
        if(empty($yesterday_data)){
            $yesterday_data = [
                'total_amount' => 0,
                'total_integral' => 0,
                'ratio' => 0.001,
                'date' => $yesterday
            ];
        }
        //获得今天的支付金额
        $today_pay = Db::name('order')->where('order_type',5)->where('order_status',3)->where('confirm_take_time','between',[$starttime,$endtime])->sum('total_num');
        $today_pay = bcmul($today_pay,60,2);
        //获得今天的总贡献值
        $today_integral = Db::name('score_log')->where('create_time','between',[$starttime,$endtime])->where('change_type',1)->sum('change_amount');

        $all_money = bcadd($today_pay,$yesterday_data['total_amount'],2);
        $all_integral = bcadd($today_integral,$yesterday_data['total_integral'],0);
        if($all_integral == 0 || $all_money == 0){
            if(!empty($yesterday_data)){
                Db::name('model2_ratio')->insert([
                    'total_amount' => $yesterday_data['total_amount'],
                    'total_integral' => $yesterday_data['total_integral'],
                    'ratio' => $yesterday_data['ratio'],
                    'date' => $today,
                    'createtime' => time()
                ]);
            }
        }else{
            $ratio = bcdiv($all_money,$all_integral,6);
            //如果今天已经有了数据的话
            if(Db::name('model2_ratio')->where('date',$today)->find()){
                Db::name('model2_ratio')->where('date',$today)->update([
                    'total_amount' => $all_money,
                    'total_integral' => $all_integral,
                    'ratio' => $ratio,
                    'date' => $today,
                    'updatetime' => time()
                ]);
            }else{
                Db::name('model2_ratio')->insert([
                    'total_amount' => $all_money,
                    'total_integral' => $all_integral,
                    'ratio' => $ratio,
                    'date' => $today,
                    'createtime' => time()
                ]);
            }
        }
        return $this->success('计算成功');
    }
}