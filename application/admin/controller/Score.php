<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------
namespace app\admin\controller;
use app\admin\logic\ScoreAreaLogic;
class Score extends AdminBase{
    public function lists(){
        $this->assign('score_list',ScoreAreaLogic::getScoreList());
        return $this->fetch();
    }
    /**
     * note 活动专区列表
     * create_time 2020/11/25 10:36
     */
    public function areaLists(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $list = ScoreAreaLogic::areaLists($get);
            $this->_success('',$list);
        }
    }

    /**
     * note 活动商品列表
     * create_time 2020/11/25 10:36
     */
    public function goodsLists(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $list = ScoreAreaLogic::goodsLists($get);
            $this->_success('',$list);
        }
    }

    /**
     * note 添加活动专区
     * create_time 2020/11/25 10:37
     */
    public function addScore(){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = $this->validate($post,'app\admin\validate\ScoreArea.add');
            if($result === true){
                ScoreAreaLogic::addScore($post);
                $this->_success('新增成功',[]);
            }
            $this->_error($result);
        }
        return $this->fetch();
    }

    /**
     * note 编辑活动专区
     * create_time 2020/11/25 10:37
     */
    public function editScore($id){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['del'] = 0;
            $result = $this->validate($post,'app\admin\validate\ScoreArea');
            if($result === true){
                ScoreAreaLogic::editScore($post);
                $this->_success('编辑成功',[]);
            }
            $this->_error($result);
        }
        $this->assign('info',ScoreAreaLogic::getScore($id));
        return $this->fetch();
    }

    /**
     * note 删除活动专区
     * create_time 2020/11/25 10:37
     */
    public function delScore(){
        $id = $this->request->post('id');
        ScoreAreaLogic::delScore($id);
        $this->_success('删除成功',[]);

    }

    /**
     * note 添加活动商品
     * create_time 2020/11/25 10:37
     */
    public function addGoods(){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['goods_list'] = form_to_linear($post);
            $result = $this->validate($post,'app\admin\validate\ScoreGoods.add');
            if($result === true){
                ScoreAreaLogic::addGoods($post);
                $this->_success('新增成功',[]);
            }
            $this->_error($result);
        }
        $this->assign('score_list',ScoreAreaLogic::getScoreList());
        return $this->fetch();
    }

    /**
     * note 编辑活动商品
     * create_time 2020/11/25 10:37
     */
    public function editGoods(){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['del'] = 0;
            $result = $this->validate($post,'app\admin\validate\ScoreGoods');

            if($result === true){
                ScoreAreaLogic::editGoods($post);
                $this->_success('新增成功',[]);
            }
            $this->_error($result);
        }
        $goods_id = $this->request->get('goods_id');
        $score_id = $this->request->get('score_id');
        $this->assign('score_list',ScoreAreaLogic::getScoreList());
        $this->assign('info',ScoreAreaLogic::getScoreGoods($goods_id,$score_id));
        return $this->fetch();
    }

    /**
     * note 删除活动商品
     * create_time 2020/11/25 10:38
     */
    public function delGoods(){
        $goods_id = $this->request->post('goods_id');
        $score_id = $this->request->post('score_id');
        $result = ScoreAreaLogic::delGoods($goods_id,$score_id);
        if($result == true){
            $this->_success('删除成功','');
        }
        return $this->_error('删除失败','');
    }

}