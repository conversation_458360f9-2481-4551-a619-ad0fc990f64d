<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.model1
// +----------------------------------------------------------------------

namespace app\admin\controller;


use app\common\server\ConfigServer;

/**
 * 拼团设置
 * Class TeamSet
 * @package app\admin\controller
 */
class Model1Set extends AdminBase
{
    public function index()
    {
        $automatic = ConfigServer::get('model1', 'automatic', 0);
        $single_fly_commission = ConfigServer::get('model1', 'single_fly_commission', 0);
        $invite_new_user = ConfigServer::get('model1', 'invite_new_user', 0);
        $group_order = ConfigServer::get('model1', 'group_order', 0);
        $this->assign('automatic', $automatic);
        $this->assign('single_fly_commission', $single_fly_commission);
        $this->assign('invite_new_user', $invite_new_user);
        $this->assign('group_order', $group_order);
        return $this->fetch('model1_set/index');
    }

    public function set()
    {
        if ($this->request->isAjax()) {
            $automatic = $this->request->post('automatic', 0, 'intval');
            $single_fly_commission = $this->request->post('single_fly_commission', 200, 'intval');
            $invite_new_user = $this->request->post('invite_new_user', 50, 'intval');
            $group_order = $this->request->post('group_order', '', 'intval');
            ConfigServer::set('model1', 'automatic', $automatic);
            ConfigServer::set('model1', 'single_fly_commission', $single_fly_commission);
            ConfigServer::set('model1', 'invite_new_user', $invite_new_user);
            ConfigServer::set('model1', 'group_order', $group_order);
            $this->_success('设置成功');
        }
    }
}