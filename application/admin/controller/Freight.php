<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\controller;

use app\admin\logic\ExpressLogic;
use app\common\server\ConfigServer;
use app\admin\logic\FreightLogic;
use app\admin\model\Freight as FreightModel;

class Freight extends AdminBase
{
    /**
     * User: 意象信息科技 mjf
     * Desc: 设置快递方式
     */
    public function set()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['is_express'] = isset($post['is_express']) && $post['is_express'] == 'on' ? 1 : 0;
            $post['is_selffetch'] = isset($post['is_selffetch']) && $post['is_selffetch'] == 'on' ? 1 : 0;
            if ($post['is_express'] === 0 && $post['is_selffetch'] === 0) {
                $this->_error('至少保留一种配送方式');
            }
            ConfigServer::set('delivery_type', 'is_express', $post['is_express']);
            ConfigServer::set('delivery_type', 'is_selffetch', $post['is_selffetch']);
            $this->_success('操作成功');
        }
        $is_express = ConfigServer::get('delivery_type', 'is_express', 1);
        $is_selffetch = ConfigServer::get('delivery_type', 'is_selffetch', 0);
        $this->assign('is_express', $is_express);
        $this->assign('is_selffetch', $is_selffetch);
        return $this->fetch();
    }

    /**
     * User: 意象信息科技 mjf
     * Desc: 运费模板列表
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $this->_success('获取成功', FreightLogic::lists($get));//运费模板页
        }
        $this->assign('charge_way_lists', FreightModel::getChargeWay(true));
        $this->assign('config', ExpressLogic::getExpress());
        return $this->fetch('index');
    }

    /**
     * User: 意象信息科技 mjf
     * Desc: 添加运费模板
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = $this->validate($post, 'app\admin\validate\Freight.add');
            if ($result === true) {
                FreightLogic::add($post);
                $this->_success('添加成功！');
            }
            $this->_error($result);
        }
        return $this->fetch();
    }

    /**
     * User: 意象信息科技 mjf
     * Desc: 删除运费模板
     */
    public function del()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = $this->validate($post, 'app\admin\validate\Freight.del');
            if ($result === true) {
                FreightLogic::del($post);
                $this->_success('删除成功！');
            }
            $this->_error($result);
        }
        return $this->fetch();
    }

    /**
     * User: 意象信息科技 mjf
     * Desc: 运费模板详情
     */
    public function detail()
    {
        $id = $this->request->get('id');
        $detail = FreightLogic::detail($id);
        $this->assign('detail', $detail);
        return $this->fetch();
    }


    /**
     * User: 意象信息科技 mjf
     * Desc: 运费模板编辑
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = $this->validate($post, 'app\admin\validate\Freight.edit');
            if ($result !== true) {
                $this->_error($result);
            }
            FreightLogic::edit($post);
            $this->_success('编辑成功！');
        }
        $id = $this->request->get('id');
        $detail = FreightLogic::detail($id);
        $this->assign('detail', $detail);
        return $this->fetch();
    }



    public function area()
    {
        return $this->fetch();
    }

    //编辑页的地区选择
    public function areaEdit()
    {
        return $this->fetch();
    }

}