<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------
namespace app\admin\controller;
use app\admin\logic\AccountLogLogic;
use app\common\model\AccountLog as AccountLogModel;
use think\helper\Time;
use think\Db;

class AccountLog extends AdminBase{
    /**
     * note 资金记录
     * create_time 2020/11/20 17:36
     */
    public function capitalList(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $list = AccountLogLogic::lists($get);
            $this->_success('',$list);
        }
        $this->assign('order_source',AccountLogLogic::orderSourceList(1));
        $this->assign('time',AccountLogLogic::getTime());
        return $this->fetch();
    }
    /**
     * note 贡献值记录
     * create_time 2020/11/20 17:36
     */
    public function integralList(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $list = AccountLogLogic::lists($get);
            $this->_success('',$list);
        }
        $this->assign('order_source',AccountLogLogic::orderSourceList(2));
        $this->assign('time',AccountLogLogic::getTime());
        return $this->fetch();
    }

    /**
     * note 成长值记录
     * create_time 2020/11/20 17:36
     */
    public function growthList(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $list = AccountLogLogic::lists($get);
            $this->_success('',$list);
        }
        $this->assign('order_source',AccountLogLogic::orderSourceList(3));
        $this->assign('time',AccountLogLogic::getTime());
        return $this->fetch();
    }



    /**
     * Notes: 佣金记录
     * <AUTHOR> 11:36)
     * @return mixed
     */
    public function withdrawList()
    {
        if($this->request->isAjax()){
            $get = $this->request->get();
            $list = [];
            switch ($get['type']) {
                case 'distribution':
                    $list = AccountLogLogic::getDistributionLog($get);
                    break;
            }
            $this->_success('',$list);
        }
        return $this->fetch();
    }


    /**
     * Notes: 佣金统计
     * <AUTHOR> 11:36)
     */
    public function withdrawTotalCount()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = AccountLogLogic::withdrawTotalCount($get);
            $this->_success('OK', $result);
        }
    }

    //资金记录导出
    public function exportAccountLog() {
        $get = $this->request->get();
        $source_type = AccountLogModel::money_change;
        $source_type = array_merge($source_type,AccountLogModel::earnings_change);
        if(isset($get['order_source']) && $get['order_source']){
            $source_type = $get['order_source'];
        }

        $where[] = ['source_type','in',$source_type];
        if(isset($get['change_type']) && $get['change_type']){
            $where[] = ['source_type','=',$get['change_type']];
        }
        if(isset($get['keyword']) && $get['keyword']){
            $where[] = [$get['keyword_type'],'like','%'.$get['keyword'].'%'];
        }
        if (isset($get['start_time']) && $get['start_time'] && isset($get['end_time']) && $get['end_time']) {
            $where[] = ['a.create_time', 'between', [strtotime($get['start_time']), strtotime($get['end_time'])]];
        }

        if(isset($get['create_end']) && $get['create_end']!=''){
            $where[] = ['ro.create_time','<=',strtotime($get['create_end'])];
        }
        if (isset($get['change_amount']) && $get['change_amount'] != '') {
            $where[] = ['a.change_amount', '=', $get['change_amount']];
        }
        //备注查询
        if(isset($get['remark']) && $get['remark']){
            $where[] = ['a.remark','like','%'.$get['remark'].'%'];
        }

        $list = Db::name('account_log')->alias('a')
                ->join('user u','a.user_id = u.id','left')
                ->where($where)
                ->order('a.id desc')
                ->field('a.*,nickname,sn,mobile')
                ->select();

        $exportTitle = ['会员昵称', '会员编号', '手机号码', '变动金额', '剩余金额', '变动类型', '来源单号', '备注','记录时间'];
        $exportExt = 'xls';
        $exportData = [];
        foreach($list as $item) {
            $source_type = AccountLogModel::getAcccountDesc($item['source_type']);
            $createTime = date('Y-m-d H:i:s',$item['create_time']);
            $exportData[] = [$item['nickname'], $item['sn'], $item['mobile'], $item['change_amount'], $item['left_amount'], $source_type, $item['log_sn'], $item['remark'], $createTime];
        }

        $data = ['exportTitle'=> $exportTitle, 'exportData' => $exportData, 'exportExt'=>$exportExt, 'exportName'=>'资金列表导出'.date('Y-m-d H:i:s')];
        $this->_success('',$data);
    }

    //积分记录导出
    public function exportIntegralLog(){
        $get = $this->request->get();
        $source_type = [101,102,103];
        if(isset($get['order_source']) && $get['order_source']){
            $source_type = $get['order_source'];
        }

        $where[] = ['source_type','in',$source_type];
        if(isset($get['change_type']) && $get['change_type']){
            $where[] = ['source_type','=',$get['change_type']];
        }
        if(isset($get['keyword']) && $get['keyword']){
            $where[] = [$get['keyword_type'],'like','%'.$get['keyword'].'%'];
        }
        if (isset($get['start_time']) && $get['start_time'] && isset($get['end_time']) && $get['end_time']) {
            $where[] = ['a.create_time', 'between', [strtotime($get['start_time']), strtotime($get['end_time'])]];
        }
        $list = Db::name('score_log')->alias('a')
        ->join('user u','a.user_id = u.id')
        ->where($where)
        ->order('a.id desc')
        ->field('a.*,nickname,sn,mobile')
        ->select();

        $exportTitle = ['会员昵称', '会员编号', '手机号码', '变动贡献值', '剩余贡献值', '变动类型', '来源单号', '记录时间'];
        $exportExt = 'xls';
        $exportData = [];
        $arr = [
            '101'=>'下单返还积分',
            '102'=>'下级下单返还积分',
            '103'=>'积分支付',
        ];
        foreach($list as $item) {
            $source_type = $arr[$item['source_type']];
            //如果source_type中有余额，改为贡献值
            $createTime = date('Y-m-d H:i:s',$item['create_time']);
            $exportData[] = [$item['nickname'], $item['sn'], $item['mobile'], $item['change_amount'], $item['left_amount'], $source_type, $item['log_sn'], $createTime];
        }

        $data = ['exportTitle'=> $exportTitle, 'exportData' => $exportData, 'exportExt'=>$exportExt, 'exportName'=>'贡献值列表导出'.date('Y-m-d H:i:s')];
        $this->_success('',$data);
    }
    
}