<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------
namespace app\admin\controller;
use app\admin\logic\Model2AreaLogic;
class Model2 extends AdminBase{
    public function lists(){
        $this->assign('model2_list',Model2AreaLogic::getModel2List());
        return $this->fetch();
    }
    /**
     * note 活动专区列表
     * create_time 2020/11/25 10:36
     */
    public function areaLists(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $list = Model2AreaLogic::areaLists($get);
            $this->_success('',$list);
        }
    }

    /**
     * note 活动商品列表
     * create_time 2020/11/25 10:36
     */
    public function goodsLists(){
        if($this->request->isAjax()){
            $get = $this->request->get();
            $list = Model2AreaLogic::goodsLists($get);
            $this->_success('',$list);
        }
    }

    /**
     * note 添加活动专区
     * create_time 2020/11/25 10:37
     */
    public function addModel2(){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = $this->validate($post,'app\admin\validate\Model2Area.add');
            if($result === true){
                Model2AreaLogic::addModel2($post);
                $this->_success('新增成功',[]);
            }
            $this->_error($result);
        }
        return $this->fetch();
    }

    /**
     * note 编辑活动专区
     * create_time 2020/11/25 10:37
     */
    public function editModel2($id){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['del'] = 0;
            $result = $this->validate($post,'app\admin\validate\Model2Area');
            if($result === true){
                Model2AreaLogic::editModel2($post);
                $this->_success('编辑成功',[]);
            }
            $this->_error($result);
        }
        $this->assign('info',Model2AreaLogic::getModel2($id));
        return $this->fetch();
    }

    /**
     * note 删除活动专区
     * create_time 2020/11/25 10:37
     */
    public function delModel2(){
        $id = $this->request->post('id');
        Model2AreaLogic::delModel2($id);
        $this->_success('删除成功',[]);

    }

    /**
     * note 添加活动商品
     * create_time 2020/11/25 10:37
     */
    public function addGoods(){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['goods_list'] = form_to_linear($post);
            $result = $this->validate($post,'app\admin\validate\Model2Goods.add');
            if($result === true){
                Model2AreaLogic::addGoods($post);
                $this->_success('新增成功',[]);
            }
            $this->_error($result);
        }
        $this->assign('model2_list',Model2AreaLogic::getModel2List());
        return $this->fetch();
    }

    /**
     * note 编辑活动商品
     * create_time 2020/11/25 10:37
     */
    public function editGoods(){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $post['del'] = 0;
            $result = $this->validate($post,'app\admin\validate\Model2Goods');

            if($result === true){
                Model2AreaLogic::editGoods($post);
                $this->_success('新增成功',[]);
            }
            $this->_error($result);
        }
        $goods_id = $this->request->get('goods_id');
        $model2_id = $this->request->get('model2_id');
        $this->assign('model2_list',Model2AreaLogic::getModel2List());
        $this->assign('info',Model2AreaLogic::getModel2Goods($goods_id,$model2_id));
        return $this->fetch();
    }

    /**
     * note 删除活动商品
     * create_time 2020/11/25 10:38
     */
    public function delGoods(){
        $goods_id = $this->request->post('goods_id');
        $model2_id = $this->request->post('model2_id');
        $result = Model2AreaLogic::delGoods($goods_id,$model2_id);
        if($result == true){
            $this->_success('删除成功','');
        }
        return $this->_error('删除失败','');
    }

}