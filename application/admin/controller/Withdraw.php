<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\controller;

use app\admin\logic\WithdrawLogic;
use app\common\model\Withdraw as WithdrawModel;
use think\helper\Time;
use app\admin\logic\WechatCorporatePaymentLogic;
use GuzzleHttp\Client;
use think\Db;
use app\common\server\ConfigServer;
use PSpell\Config;
use think\facade\Log;

class Withdraw extends AdminBase
{

    /**
     * 提现列表
     * @return mixed
     */
    public function lists()
    {
        if($this->request->isAjax()) {
          $get = $this->request->get();
          return $this->_success('获取列表成功', WithdrawLogic::lists($get));
        }

        $this->assign('type', WithdrawModel::getTypeDesc(true));
        $this->assign('status', WithdrawModel::getStatusDesc(true));

        $today = array_map(function ($time) {
            return date('Y-m-d H:i:s', $time);
        }, Time::today());
        $this->assign('today', $today);

        $yesterday = array_map(function ($time) {
            return date('Y-m-d H:i:s', $time);
        }, Time::yesterday());
        $this->assign('yesterday', $yesterday);


        $days_ago7 = array_map(function ($time) {
            return date('Y-m-d H:i:s', $time);
        }, Time::dayToNow(7));
        $this->assign('days_ago7', $days_ago7);

        $days_ago30 = array_map(function ($time) {
            return date('Y-m-d H:i:s', $time);
        }, Time::dayToNow(30, true));
        $this->assign('days_ago30', $days_ago30);
        return $this->fetch();
    }


    /**
     * Desc: 审核通过
     */
    public function confirm()
    {
        if ($this->request->isAjax()){
            $post = $this->request->post();

            $winfo = Db::name('withdraw_apply')->where('id',$post['id'])->find();
            $url = ConfigServer::get('withdraw','domain','https://uat.xzsz.ltd/').'open/pay/singleSubmit';
            $key = ConfigServer::get('withdraw','key','1e8e59e056be41f8a05c090964d7ce37');
            $iv = ConfigServer::get('withdraw','iv','0000000000000000');
            $secret = ConfigServer::get('withdraw','secret','ceshihuanjingyanshizhanghao00000');
            if(empty($key)){
              return ['code'=>0,'msg'=>'请先设置密钥'];
            }
            $withdraw = new \app\api\logic\WithdrawLogic();
            $data = [
              'appKey' => $key,
              'charset' => 'utf-8',
              'version' => '2.0',
            ];
            //获得基础信息
            $base_data = $withdraw->getData();
            $base_data = json_decode($base_data['data'],true);
            $position = implode(',',$base_data['data'][0]['positionList']);
            //获得当前网址
            $domain = request()->domain();
            $back_url = $domain.'/api/withdraw/notify';
            $item = [
              "notifyUrl"=> $back_url,
              "taxFundId"=> $base_data['data'][0]['taxFundID'],
              "month"=>date("Y-m",time()),
              "outerTradeNo"=>$winfo['sn'],
              "empName"=>$winfo['real_name'],
              "empPhone"=>$winfo['mobile'],
              "licenseType"=>"ID_CARD",
              "licenseId"=>$winfo['id_card'],
              "settleType"=>"bankcard",
              "payAccount"=>$winfo['account'],
              "positionName"=>$position,
              "payAmount"=>$winfo['left_money'],
            ];
            //先计算AES加密
            $params = [
              'content' => json_encode($item),
              'secret' => $secret,
              'iv' => $iv,
            ];
            $data['bizAESContent'] = $this->encrypt($params);
            //data排序
            ksort($data);
            $data['sign']  = $withdraw->getSignV2($data);
            $contentType =['Content-Type:application/json'];
            $response = $this->jsonPost($url,json_encode($data),$contentType);
            Log::record($response);
            $response = json_decode($response,true);
            if($response['isSuccess'] == "T"){
              Db::name('withdraw_apply')
              ->where('id',  $post['id'])
              ->update(['status' => 2,'update_time' => time(),'description'=>$post['description']]);
              $this->_success('审核通过，提现中');
            }else{
              $this->_error($response['errorMessage']);

            }
        }
      }

    //json提交
    public function jsonPost($url, $data, $contentType, $timeout = 10)
    {
        $ch = curl_init();
        //要访问的地址
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, false);//设定返回信息中包含响应信息头
        curl_setopt($ch, CURLOPT_HTTPHEADER, $contentType);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);//将curl_exec()获取的信息以文件流的形式返回

        // 发送一个常规的POST请求
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        //关闭https验证
        //PHP里curl对https的证书配置默认是服务器端要求验证的，如果服务器端没有配置证书验证，
        //则无法请求https路径。如果为了简便使用不需要配置https证书的话，配置curl时将以下两项设置为false即可
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        //执行并获取数据
        $response = curl_exec($ch);
        //释放curl句柄
        curl_close($ch);
        return $response;
    }

      public function encrypt($data)
      {
          return urlencode(openssl_encrypt($data['content'], "AES-256-CBC", $data['secret'], 0, $data['iv']));
      }

    /**
     * Desc: 审核拒绝
     */
    public function refuse()
    {
        if ($this->request->isAjax()){
            $post = $this->request->post();
            WithdrawLogic::refuse($post);
            $this->_success('已拒绝提现');
        }
    }

    /**
     * 提现详情
     */
    public function detail()
    {
      $id = $this->request->get('id', '', 'intval');
      $detail = WithdrawLogic::detail($id);
      $this->assign('detail', $detail);
      return $this->fetch();
    }

    /**
     * 显示提现审核界面
     */
    public function review()
    {
      $id = $this->request->get('id', '', 'intval');
      $this->assign('id', $id);
      return $this->fetch();
    }

    /**
     * 显示提现转账界面
     */
    public function transfer()
    {
      $id = $this->request->get('id', '', 'intval');
      $this->assign('id', $id);
      return $this->fetch();
    }

    /**
     * 转账失败
     */
    public function transferFail()
    {
      $post = $this->request->post();
      $result = WithdrawLogic::transferFail($post);
      if($result['code']) {
        return $this->_success($result['msg']);
      }else{
        return $this->_error($result['msg']);
      }
    }

    /**
     * 转账成功
     */
    public function transferSuccess()
    {
      $post = $this->request->post();
      $result = WithdrawLogic::transferSuccess($post);
      if($result['code']) {
        return $this->_success($result['msg']);
      }else{
        return $this->_error($result['msg']);
      }
    }

    /**
     * 提现结果查询
     */
    public function search()
    {
      $id = $this->request->post('id', '', 'intval');
      $result = WithdrawLogic::search($id);
      if($result['code']) {
        return $this->_success($result['msg']);
      }else{
        return $this->_error($result['msg']);
      }
    }

    /**
     * 提现失败
     */
    public function withdrawFailed() {
      $id = $this->request->post('id', '', 'intval');
      $result = WithdrawLogic::withdrawFailed($id);
      $this->_success('提现失败已回退佣金');
    }

    //提现记录导出
    public function withdrawExport() {
      $get = $this->request->get();
      $where = [];
      // 会员信息
      if (!empty($get['search_key']) && !empty($get['keyword'])) {
          $keyword = $get['keyword'];
          if ($get['search_key'] == 'user_sn') {
              $where[] = ['u.sn', '=', $keyword];
          } elseif ($get['search_key'] == 'nickname'){
              $where[] = ['u.nickname', 'like', '%' . $keyword . '%'];
          }
      }

      //提现单号
      if (isset($get['withdraw_sn']) && $get['withdraw_sn'] != '') {
        $where[] = ['w.sn', '=', $get['withdraw_sn']];
    }

      //提现方式
      if (isset($get['type']) && $get['type'] != '') {
          $where[] = ['type', '=', $get['type']];
      }

      //提现状态
      if (isset($get['status']) && $get['status'] != '') {
          $where[] = ['status', '=', $get['status']];
      }

      // 提现时间
      if (isset($get['start_time']) && $get['start_time'] && isset($get['end_time']) && $get['end_time']) {
          $where[] = ['w.create_time', 'between', [strtotime($get['start_time']), strtotime($get['end_time'])]];
      }else{
//            $where[] = ['w.create_time', 'between', Time::today()];
      }

      $list = Db::name('withdraw_apply w')
          ->field('w.*, u.nickname,u.avatar, u.sn as user_sn, u.mobile, ul.name as user_level_name')
          ->leftJoin('user u', 'u.id = w.user_id')
          ->leftJoin('user_level ul', 'ul.id = u.level')
          ->where($where)
          ->order('w.id desc')
          ->select();

          $exportTitle = ['真实姓名', '会员编号', '手机号码', '身份证号', '银行卡号', '提现单号','提现金额', '提现方式', '提现时间'];
          $exportExt = 'xls';
          $exportData = [];
          foreach($list as $item) {
              $createTime = date('Y-m-d H:i:s',$item['create_time']);
              $exportData[] = [$item['real_name'], $item['user_sn'], $item['mobile'], $item['id_card'], $item['account'], $item['sn'], $item['money'], '银行卡', $createTime];
          }

          $data = ['exportTitle'=> $exportTitle, 'exportData' => $exportData, 'exportExt'=>$exportExt, 'exportName'=>'提现列表导出'.date('Y-m-d H:i:s')];
          return $this->_success('',$data);
    }
}