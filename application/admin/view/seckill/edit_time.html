{layout name="layout2" /}
<style>
    .layui-form-label {
        color: #6a6f6c;
        width: 100px;
    }
    .layui-input-block {
        margin-left: 130px;
    }
    .tips{
        color: red;
    }
    .layui-laydate-content>.layui-laydate-list {
        padding-bottom: 0px;
        overflow: hidden;
    }
    .layui-laydate-content>.layui-laydate-list>li{
        width:50%
    }

    .merge-box .scrollbox .merge-list {
        padding-bottom: 5px;
    }
</style>
<div class="layui-form" lay-filter="layuiadmin-form-user_level" id="layuiadmin-form-user_level" style="padding: 20px 30px 0 0;">
    <input type="hidden" name="id" value="{$detail.id}">
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>开始时间</label>
        <div class="layui-input-inline">
            <input type="text" value="{$detail.start_time}" name="start_time" lay-verify="required" lay-verType="tips"  placeholder="请输入开始时间" autocomplete="off" class="layui-input time">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="tips">*</span>结束时间</label>
        <div class="layui-input-inline">
            <input type="text" value="{$detail.end_time}" name="end_time" lay-verify="required" lay-verType="tips"  placeholder="请输入结束时间" autocomplete="off" class="layui-input time">
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="edit-submit" id="edit-submit" value="确认">
    </div>
</div>
<style>
    .layui-form-label {
        width: 100px;
    }
    .layui-input-block {
        margin-left: 130px;
    }
</style>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'form','like','laydate'], function(){
        var $ = layui.$
            ,form = layui.form
            ,like = layui.like
            ,laydate = layui.laydate;
        lay('.time').each(function() {
            laydate.render({
                elem : this,
                trigger : 'click',
                type: 'time',
                format: 'HH:mm',
            });
        });

    })

</script>