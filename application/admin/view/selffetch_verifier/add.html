{layout name="layout2" /}

<link rel="stylesheet" href="/static/admin/css/goods.css" media="all">

<div class="layui-form" lay-filter="layuiadmin-form-cate" id="layuiadmin-form-cate" style="padding: 20px 30px 0 0;">

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>核销员名称：</label>
        <div class="layui-input-inline">
            <input type="text" name="name" lay-verify="required" lay-verType="tips"  placeholder="" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>用户昵称：</label>
        <div class="layui-input-inline" id="layerDemo" style="width: 50%">
            <input type="hidden" id="user_id" name="user_id" value="">
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" style="height: 50px;padding: 0 20px;display: block;" data-method="addUser"><i class="layui-icon" style="font-size: 25px!important;" id="user_name">+</i></button>
            <div class=" layui-form-mid layui-word-aux" >选择商城用户，核销员可在商城个人中心进行订单核销</div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>自提门店：</label>
        <div class="layui-input-block" style="width: 190px">
            <select name="selffetch_shop_id" id="selffetch_shop_id">
                {foreach $shop_lists as $item}
                <option value="{$item.id}">{$item.name}</option>
                {/foreach}
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>联系电话：</label>
        <div class="layui-input-inline">
            <input type="text" name="mobile" lay-verify="required" lay-verType="tips"  placeholder="" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>核销员状态：</label>
        <div class="layui-input-block">
            <input type="radio" name="status" value="0" title="停用">
            <input type="radio" name="status" value="1" title="启用">
        </div>
    </div>


    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="add-cate-submit" id="add-cate-submit" value="确认">
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'form','like'], function(){
        var $ = layui.$
            ,form = layui.form
            ,like = layui.like;


        var user_id = '';
        var user_name = '+';

        // 触发事件
        var active = {
            addUser: function(obj){
                layer.open({
                    type: 2
                    ,title: '用户列表'
                    ,content: '{:url("selffetch_verifier/userLists")}'
                    ,area: ['90%','90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        layer.closeAll();
                    }
                    ,btn2: function(index, layero){
                        $('#user_id').val(user_id);
                        $('#user_name').html(user_name);
                    }
                });
            }
        };
        $('#layerDemo .layui-btn').on('click', function(){
            var othis = $(this), method = othis.data('method');
            active[method] ? active[method].call(this, othis) : '';
        });

    })
</script>