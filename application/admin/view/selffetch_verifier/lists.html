{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-tab layui-tab-card" lay-filter="tab-all">

            <div class="layui-tab-item layui-show">
                <div class="layui-card">

                    <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">核销员:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="verifier_info" id="verifier_info" placeholder="请输入核销员名称/编号" autocomplete="off" class="layui-input">
                                </div>
                            </div>


                            <div class="layui-inline">
                                <label class="layui-form-label">自提门店:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="shop_name" id="shop_name" placeholder="请输入自提门店名称" autocomplete="off" class="layui-input">
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label class="layui-form-label">核销员状态:</label>
                                <div class="layui-input-block">
                                    <select name="status" id="status">
                                        <option value="">全部</option>
                                        <option value="1">启用</option>
                                        <option value="0">停用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layui-btn-sm layuiadmin-btn-ad {$view_theme_color}" lay-submit lay-filter="ad-search">查询</button>
                                <button class="layui-btn layui-btn-sm layuiadmin-btn-ad layui-btn-primary " lay-submit lay-filter="ad-clear-search">重置</button>
                                <button class="layui-btn layui-btn-sm layuiadmin-btn-ad layui-btn-primary " lay-submit lay-filter="export-file">导出</button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <div style="padding-bottom: 10px;">
                            <button class="layui-btn layui-btn-sm  layuiadmin-btn-ad {$view_theme_color}" data-type="add">新增核销员</button>
                        </div>

                        <table id="ad-lists" lay-filter="ad-lists"></table>

                        <!--用户名称-->
                        <script type="text/html" id="user">
                            <div class="goods-content">
                                <div style="display: flex;align-items: center" class="goods-data">
                                    <img src="{{ d.avatar }}" style="height:80px;width: 80px" class="image-show layui-col-md4">
                                    <span class="layui-col-md7 goods_name_hide">{{ d.nickname }}</span>
                                </div>
                            </div>
                        </script>

                        <script type="text/html" id="ad-operation">
                            <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">编辑</a>
                            {{# if(d.status == 1){ }}
                            <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="status_switch">停用</a>
                            {{# }else if(d.status == 0){ }}
                            <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="status_switch">启用</a>
                            {{#} }}
                            <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</a>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,like = layui.like
            ,element = layui.element;
        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,600);
        });

        //监听搜索
        form.on('submit(ad-search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('ad-lists', {
                where: field,
                page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });
        //清空查询
        form.on('submit(ad-clear-search)', function(){
            $('#verifier_info').val('');
            $('#shop_name').val('');
            $('#status').val('');
            form.render('select');
            //刷新列表
            table.reload('ad-lists', {
                where: [],
                page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });

        //列表
        getList('')
        function getList() {

            table.render({
                elem: '#ad-lists'
                , url: '{:url("selffetch_verifier/lists")}'  //模拟接口
                , cols: [[
                    {field: 'sn', title: '核销员编号'}
                    , {field: 'name', title: '核销员名称'}
                    ,{field: 'nickname', title: '用户名称', align: 'center',templet:'#user'}
                    , {field: 'mobile', title: '联系电话'}
                    , {field: 'status_desc', title: '核销员状态'}
                    , {field: 'create_time', title: '创建时间'}
                    , {fixed: 'right', title: '操作' ,toolbar: '#ad-operation',width:250}
                ]]
                , page: true
                , text: {none: '暂无数据！'}
                , parseData: function (res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.data.count,
                        "data": res.data.lists,
                    };
                }
                ,done: function(res, curr, count){
                    // 解决操作栏因为内容过多换行问题
                    $(".layui-table-main tr").each(function (index, val) {
                        $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                        $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                    });
                }
            });
        }


        //添加事件
        var active = {
            add: function(){
                var add_page = layer.open({
                    type: 2
                    ,title: '新增自提门店'
                    ,content: '{:url("selffetch_verifier/add")}'
                    ,area: ['90%','90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'add-cate-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("selffetch_verifier/add")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('ad-lists'); //数据刷新
                                    }
                                },
                            });
                        });

                        submit.trigger('click');
                    }
                });
            }
        }
        $('.layui-btn.layuiadmin-btn-ad').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });


        //监听工具条
        table.on('tool(ad-lists)', function(obj){
            if(obj.event === 'del'){
                var id = obj.data.id;
                var name = obj.data.name;
                layer.confirm('确认删除自提门店:'+'<span style="color: red">'+name+'</span>', function(index){
                    like.ajax({
                        url:'{:url("selffetch_verifier/del")}',
                        data:{id:id},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1)
                            {
                                obj.del();
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                                layer.close(index); //关闭弹层
                            }
                        },
                    });
                });
            }else if(obj.event === 'edit'){
                var id = obj.data.id;
                var edit_page = layer.open({
                    type: 2
                    ,title: '编辑自提门店'
                    ,content: '{:url("selffetch_verifier/edit")}?id='+id
                    ,area: ['90%','90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'edit-cate-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            like.ajax({
                                url:'{:url("selffetch_verifier/edit")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('ad-lists'); //数据刷新
                                    }
                                },
                            });
                        });

                        submit.trigger('click');
                    }
                })
            }else if(obj.event === 'status_switch'){
                var id = obj.data.id;
                var status = obj.data.status;
                var name = obj.data.name;
                if (status == 1){
                    confirm_text = '确定停用自提门店：';
                    status = 0;
                }else {
                    var confirm_text = '确定启用自提门店：';
                    status = 1;
                }
                layer.confirm(confirm_text +'<span style="color: red">'+name+'</span>', function(index){

                    like.ajax({
                        url:'{:url("selffetch_verifier/status")}',
                        data:{id:id,status:status},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1)
                            {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                                table.reload('ad-lists');
                            }
                        }
                    });
                })
            }
        });


        // 导出
        form.on('submit(export-file)', function(data){
            var field = data.field;
            $.ajax({
                url: '{:url("selffetch_verifier/exportFile")}',
                type: 'get',
                data: field,
                dataType: 'json',
                error: function() {
                    layer.msg('导出超时，请稍后再试!');
                },
                success: function(res) {
                    table.exportFile(res.data.exportTitle,res.data.exportData, res.data.exportExt, res.data.exportName);
                },
                timeout: 15000
            });
            layer.msg('导出中请耐心等待~');
        });
    });
</script>