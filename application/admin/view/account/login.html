<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$config.name}</title>
    <link rel="shortcut icon" href="{$storageUrl}{$config.web_favicon}"/>
    <link rel="stylesheet" href="__PUBLIC__/static/plug/layui-admin/dist/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" type="text/css" href="__PUBLIC__/static/common/css/login.css"/>
</head>
<style>
    #logo{
        width: 176px;height: 42px
    }
        canvas{
            display:block;
            width:100%;
            height:100%;
            position: fixed;
            top: 0;
            left: 0;
        }

        .login {
            width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 999;
            background: url('/images/bg.png');
            background-size: 100% 100%;
        }
        .login-form-box{
            z-index: 9999;
        }
</style>
<body>
<div class="login">
    <canvas id="canvas"></canvas>
    <div class="login-form-box">
        <div class="login-left">
            <div class="login-left-img">
                <img  src="{$storageUrl}{$config.admin_image}"/>
            </div>
        </div>
        <div class="login-right">
            <div class="login-form layui-form">
                <div class="login-title">
                    {$config.admin_title}
                </div>
                <div class="form-box-item">
                    <div class="icon">
                        <img src="__PUBLIC__/static/common/image/login/login_number.png"/>
                    </div>
                    <div>
                        <input type="text" name="account" lay-verify="required" lay-vertype="tips"
                               class="layui-input" style="border:none;width: 300px;padding-left: 20px;"
                               placeholder="请输入账号" value="{$account}"/>
                    </div>
                </div>
                <div class="form-box-item">
                    <div class="icon">
                        <img src="__PUBLIC__/static/common/image/login/login_password.png"/>
                    </div>
                    <div>
                        <input type="password" name="password" lay-verify="required" lay-vertype="tips"
                               class="layui-input" style="border:none;width: 300px;padding-left: 20px;"
                               placeholder="请输入密码"/>
                    </div>
                </div>

                <div class="form-box-checked">
                    <div>
                        <input type="checkbox" lay-skin="primary" name="remember_account" title="记住账号" {notempty name="account"}checked=""{/notempty}>
                    </div>
                </div>
                <button id="login" lay-filter="login" class="submit-btn" lay-submit style="background-color: #2C85EA">登录</button>
            </div>
        </div>
    </div>
</div>
<script src="__PUBLIC__/static/plug/layui-admin/dist/layuiadmin/layui/layui.js"></script>
</body>
<script>
    if (self != top) {
        parent.window.location.replace(window.location.href);
    }
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'user', 'like'], function () {
        var $ = layui.$
            , like = layui.like
            , form = layui.form
        form.render();


        function login(obj) {
            like.ajax({
                url: '{:url("account/login")}' //实际使用请改成服务端真实接口
                , data: obj.field
                , type: 'post'
                , success: function (res) {
                    if (res.code == 1) {
                        layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        }, function () {
                            location.href = '../'; //后台主页
                        });
                    }
                   
                },
            });
        }

        //提交
        form.on('submit(login)', function (obj) {
            console.log(3434);
            login(obj);
        });


        $('[name="account"]').keyup(function (event) {
            if (event.keyCode == 13) {
                $('#login').trigger('click');
            }
        });
        $('[name="password"]').keyup(function (event) {
            if (event.keyCode == 13) {
                $('#login').trigger('click');
            }
        });
        $('[name="code"]').keyup(function (event) {
            if (event.keyCode == 13) {
                $('#login').trigger('click');
            }
        });
    });
</script>
<script type="text/javascript">
    var canvas=document.querySelector("#canvas"),
        ctx=canvas.getContext("2d");
    canvas.width=window.innerWidth,
        canvas.height=window.innerHeight,
        ctx.lineWidth=.3,
        ctx.strokeStyle=new Color(150).style;
    var movePos={x:30*canvas.width/100,y:30*canvas.height/100},dots={nb:250,distance:100,d_radius:150,array:[]};function colorValue(t){return Math.floor(255*Math.random()+t)}function createColorStyle(t,o,i){return"rgba("+t+","+o+","+i+", 0.618)"}function mixComponents(t,o,i,a){return(t*o+i*a)/(o+a)}function averageColorStyles(t,o){var i=t.color,a=o.color,s=mixComponents(i.r,t.radius,a.r,o.radius),n=mixComponents(i.g,t.radius,a.g,o.radius),e=mixComponents(i.b,t.radius,a.b,o.radius);return createColorStyle(Math.floor(s),Math.floor(n),Math.floor(e))}function Color(t){t=t||0,this.r=colorValue(t),this.g=colorValue(t),this.b=colorValue(t),this.style=createColorStyle(this.r,this.g,this.b)}function Dot(){this.x=Math.random()*canvas.width,this.y=Math.random()*canvas.height,this.vx=-.5+Math.random(),this.vy=-.5+Math.random(),this.radius=3*Math.random(),this.color=new Color}function createDots(){for(i=0;i<dots.nb;i++)dots.array.push(new Dot)}function moveDots(){for(i=0;i<dots.nb;i++){var t=dots.array[i];t.y<0||t.y>canvas.height?(t.vx=t.vx,t.vy=-t.vy):(t.x<0||t.x>canvas.width)&&(t.vx=-t.vx,t.vy=t.vy),t.x+=t.vx,t.y+=t.vy}}function connectDots(){for(i=0;i<dots.nb;i++)for(j=0;j<dots.nb;j++)i_dot=dots.array[i],j_dot=dots.array[j],i_dot.x-j_dot.x<dots.distance&&i_dot.y-j_dot.y<dots.distance&&i_dot.x-j_dot.x>-dots.distance&&i_dot.y-j_dot.y>-dots.distance&&i_dot.x-movePos.x<dots.d_radius&&i_dot.y-movePos.y<dots.d_radius&&i_dot.x-movePos.x>-dots.d_radius&&i_dot.y-movePos.y>-dots.d_radius&&(ctx.beginPath(),ctx.strokeStyle=averageColorStyles(i_dot,j_dot),ctx.moveTo(i_dot.x,i_dot.y),ctx.lineTo(j_dot.x,j_dot.y),ctx.stroke(),ctx.closePath())}function drawDots(){for(i=0;i<dots.nb;i++){dots.array[i].draw()}}function runDots(){ctx.clearRect(0,0,canvas.width,canvas.height),moveDots(),connectDots(),drawDots(),requestAnimationFrame(runDots)}Dot.prototype={draw:function(){ctx.beginPath(),ctx.fillStyle=this.color.style,ctx.arc(this.x,this.y,this.radius,0,3*Math.PI,!1),ctx.fill()}};var can=document.querySelector("#canvas");can.addEventListener("mousemove",function(t){movePos.x=t.pageX,movePos.y=t.pageY}),can.addEventListener("mouseleave",function(t){movePos.x=canvas.width/2,movePos.y=canvas.height/2}),createDots(),requestAnimationFrame(runDots);
</script>
</html>