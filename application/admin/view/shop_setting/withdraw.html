{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 100px;
    }
    .img-content{
        height:80px;
        line-height:80px
    }
    .img-container {
        float: left;
        opacity: 1;
        position: relative;
    }

    .img-src {
        width: 80px;
        height: 80px;
        padding: 4px;
    }
    .img-del-x {
        position: absolute;
        z-index: 100;
        top: -4px;
        right: -2px;
        width: 20px;
        height: 20px;
        font-size: 16px;
        line-height: 16px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        background: hsla(0, 0%, 60%, .6);
        border-radius: 10px;
    }
</style>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*会员提现金额要求设置。</p>
                        <p> *会员只能对分销佣金进行提现。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <div class="layui-form" lay-filter="">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="white-space: nowrap; ">最低提现金额：</label>
                    <div class="layui-input-inline">
                        <input type="number" name="min_withdraw" lay-verType="tips" autocomplete="off"
                            value="{$config.min_withdraw}" class="layui-input">
                        <div class=" layui-form-mid layui-word-aux" style="white-space: nowrap">会员提现需满足最低提现金额，才能提交提现申请。
                        </div>
                    </div>
                    <div class="layui-form-mid"> 元</div>
                </div>


                <div class="layui-form-item ">
                    <label class="layui-form-label" style="white-space: nowrap; ">最高提现金额：</label>
                    <div class="layui-input-inline">
                        <input type="number" name="max_withdraw" lay-verType="tips" autocomplete="off"
                            value="{$config.max_withdraw}" class="layui-input">
                        <div class=" layui-form-mid layui-word-aux" style="white-space: nowrap">会员提现允许的最高提现金额。</div>
                    </div>
                    <div class="layui-form-mid"> 元</div>
                </div>

                <div class="layui-form-item ">
                    <label class="layui-form-label" style="white-space: nowrap; ">提现手续费：</label>
                    <div class="layui-input-inline">
                        <input type="number" name="poundage" lay-verType="tips" autocomplete="off"
                            value="{$config.poundage}" class="layui-input">
                        <div class=" layui-form-mid layui-word-aux" style="white-space: nowrap">会员提现时收取的手续费占比。</div>
                    </div>
                    <div class="layui-form-mid"> %</div>
                </div>

                <div class="layui-form-item ">
                    <label class="layui-form-label" style="white-space: nowrap; ">域名：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="domain" lay-verType="tips" autocomplete="off" value="{$config.domain}"
                            class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item ">
                    <label class="layui-form-label" style="white-space: nowrap; ">key：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="key" lay-verType="tips" autocomplete="off" value="{$config.key}"
                            class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item ">
                    <label class="layui-form-label" style="white-space: nowrap; ">secret：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="secret" lay-verType="tips" autocomplete="off" value="{$config.secret}"
                            class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item ">
                    <label class="layui-form-label" style="white-space: nowrap; ">iv：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="iv" lay-verType="tips" autocomplete="off" value="{$config.iv}"
                            class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">签约码：</label>
                    <div class="layui-input-block">
                        <div class="img-content">
                            <input name="sign_code" type="hidden" value="{$config.sign_code}">
                            <div class="img-add" {if $config.sign_code } style="display: none" {/if}></div>
                            {if !empty($config.sign_code)}
                            <div class="img-container">
                                <img class="img-src" src="{$config.sign_code}">
                                <a class="img-del-x">x</a>
                            </div>
                            {/if}
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-sm {$view_theme_color}" lay-submit
                            lay-filter="setWithdrawal">确认</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>


    layui.config({
        version: "{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'table', 'like'], function () {
        var $ = layui.$
            , form = layui.form
            , like = layui.like;

        form.on('submit(setWithdrawal)', function (data) {
            like.ajax({
                url: '{:url("shopSetting/setWithdraw")}'
                , data: data.field
                , type: 'post'
                , success: function (res) {
                    if (res.code == 1) {
                        layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1500
                        });
                    }
                },
            });
        });

        //上传图片
        like.imageUpload('.img-add', function (uris, element) {
            if (uris.length > 1) {
                layer.msg('最多最能选中1张图片');
                return;
            }
            var html = '<div class="img-container">\n' +
                '<img class="img-src" ' +
                'src="' + uris[0] + '">' +
                '<a class="img-del-x">x</a>' +
                '</div>';
            element.prev().val(like.getUrlFileName(uris[0], '{$storageUrl}'));
            element.parent().append(html);
            element.css('display', 'none');
        }, true);

        //删除图片
        $(document).on('click', '.img-del-x', function () {
            $(this).parent().siblings('input').val('');
            $(this).parent().prev().css('display', 'block');
            $(this).parent().remove();
        });

        //显示图片
        $(document).on('click', '.img-src', function () {
            var image = $(this).attr('src');
            like.showImg(image, 600);
        });
    });

</script>