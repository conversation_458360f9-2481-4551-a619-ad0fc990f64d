{layout name="layout1" /}


<div class="layui-col-md12">
    <div class="layui-fluid">

        <div class="layui-form" lay-filter="">
            <div class="layui-card" >
                <div class="layui-card-body">
                    <div class="layui-card-body">
                        <div class="layui-inline">
                            <div class="layui-btn-group">
                                <button type="button"  day="1" class="layui-btn layui-btn-sm layui-btn-primary day">今天</button>
                                <button type="button" id="seven"  day="7" class="layui-btn layui-btn-sm layui-btn-normal day">近7天</button>
                                <button type="button"  day="15" class="layui-btn layui-btn-sm layui-btn-primary day">近15天</button>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">时间筛选:</label>
                            <div class="layui-input-inline">
                                <div class="layui-input-inline">
                                    <input type="text" name="start_time" class="layui-input" id="start_time"
                                           placeholder="" autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-input-inline" style="width: 5px;">
                                <label class="layui-form-mid">-</label>
                            </div>
                            <div class="layui-input-inline">
                                <input type="text" name="end_time" class="layui-input" id="end_time"
                                       placeholder="" autocomplete="off">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <a class="layui-btn layui-btn-sm layuiadmin-btn-ad {$view_theme_color}" lay-submit lay-filter="search">筛选</a>
                            <a class="layui-btn layui-btn-sm layuiadmin-btn-ad layui-btn-primary " lay-submit lay-filter="clear-search">清空</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row layui-col-space15">
                <div class="layui-col-sm6 layui-col-md3" >
                    <div class="layui-card" >
                        <div class="layui-card-header" >
                            商品浏览量
                        </div>
                        <div class="layui-card-body layuiadmin-card-list" >
                            <p class="layuiadmin-big-font" id="goods_click">0</p>
                        </div>
                    </div>
                </div>
                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            用户浏览量
                        </div>
                        <div class="layui-card-body layuiadmin-card-list">
                            <p class="layuiadmin-big-font" id="user_click">0</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-card" style="width: 100%;margin-top:20px">
                <div class="layui-card-body">
                    <div class="layui-form-item">
                        <div id="visitCharts" style="width: 100%;height: 80vh;"> </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>


<script>

    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like','echarts','form', 'laydate'], function(){
        var $ = layui.$
            ,form = layui.form
            ,like = layui.like
            ,echarts = layui.echarts
            ,laydate = layui.laydate;

        const defaultSearch = {start_time:'{$time.days_ago7[0]}', end_time:'{$time.days_ago7[1]}'};
        const color = ["#FFC005", "#00CA69","#0090FF"];
        const hexToRgba = (hex, opacity) => {
            let rgbaColor = "";
            let reg = /^#[\da-f]{6}$/i;
            if (reg.test(hex)) {
                rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
                    "0x" + hex.slice(3, 5)
                )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
            }
            return rgbaColor;
        };


        // 首次加载
        loadData(defaultSearch);

        //监听搜索
        form.on('submit(search)', function(data) {
            formattingDate();
            loadData(data.field);
        });


        //清空查询
        form.on('submit(clear-search)', function() {
            formattingDate();
            $('#seven').trigger("click");
            loadData(defaultSearch);
        });

        // 日期时间范围
        laydate.render({
            elem: '#start_time'
            ,theme: '#1E9FFF'
            , value: "{$time.days_ago7[0]}"
        });

        laydate.render({
            elem: '#end_time'
            ,theme: '#1E9FFF'
            ,value: "{$time.days_ago7[1]}"
        });

        // 切换快捷日期
        $('.day').click(function() {
            $('.day').removeClass('layui-btn-normal');
            $('.day').removeClass('layui-btn-primary');
            $('.day').addClass('layui-btn-primary');
            $(this).removeClass('layui-btn-primary');
            $(this).addClass('layui-btn-normal');
            var day = $(this).attr('day');
            switch (day) {
                case '1':
                    $('#start_time').val('{$time.today[0]}');
                    $('#end_time').val('{$time.today[1]}');
                    break;
                case '7':
                    $('#start_time').val('{$time.days_ago7[0]}');
                    $('#end_time').val('{$time.days_ago7[1]}');
                    break;
                case '15':
                    $('#start_time').val('{$time.days_ago15[0]}');
                    $('#end_time').val('{$time.days_ago15[1]}');
                    break;
            }
        });


        // 加载数据
        function loadData(data) {
            like.ajax({
                url: '{:url("statistics/visit")}',
                data: data,
                type: "get",
                success: function (res) {

                    $('#goods_click').html(res.data.goods_click);
                    $('#user_click').html(res.data.user_click);

                    var xData = res.data.days;
                    var goodsClickData = res.data.echarts_goods_click;
                    var userClickData = res.data.echarts_user_click;

                    var option = setOption(xData, goodsClickData, userClickData);
                    var visitCharts = echarts.init(document.getElementById('visitCharts'));
                    visitCharts.setOption(option, true);
                    window.addEventListener('resize', function () {
                        visitCharts.resize()
                    });
                }
            });
        }


        // 格式化快捷日期
        function formattingDate() {
            $('.day').removeClass('layui-btn-normal');
            $('.day').removeClass('layui-btn-primary');
            $('.day').addClass('layui-btn-primary');
        }

        // 设置图表
        function setOption(xData, goodsClickData, userClickData) {
            option = {
                backgroundColor: '#fff',
                color: color,
                legend: {
                    top: 20,
                },
                tooltip: {
                    trigger: "axis",
                    formatter: function(params) {
                        let html = '';
                        params.forEach(v => {
                            html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color[v.componentIndex]};"></span>
                ${v.seriesName}${v.name}
                <span style="color:${color[v.componentIndex]};font-weight:700;font-size: 18px;margin-left:5px">${v.value}</span>
                次`;
                        })
                        return html
                    },
                    extraCssText: 'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;',
                    axisPointer: {
                        type: 'shadow',
                        shadowStyle: {
                            color: '#ffffff',
                            shadowColor: 'rgba(225,225,225,1)',
                            shadowBlur: 5
                        }
                    }
                },
                grid: {
                    top: 100,
                    containLabel: true
                },
                xAxis: [{
                    type: "category",
                    name: '(日期)',
                    boundaryGap: false,
                    axisLabel: {
                        textStyle: {
                            color: "#333"
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: "#D9D9D9"
                        }
                    },
                    data: xData,
                }],
                yAxis: [{
                    type: "value",
                    name: '(次)',
                    axisLabel: {
                        textStyle: {
                            color: "#666"
                        }
                    },
                    nameTextStyle: {
                        color: "#666",
                        fontSize: 12,
                        lineHeight: 40
                    },
                    // 分割线
                    splitLine: {
                        lineStyle: {
                            type: "dashed",
                            color: "#E9E9E9"
                        }
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    }
                }],
                series: [{
                    name: "商品浏览量",
                    type: "line",
                    smooth: true,
                    symbolSize: 8,
                    zlevel: 3,
                    lineStyle: {
                        normal: {
                            color: color[0],
                            shadowBlur: 3,
                            shadowColor: hexToRgba(color[0], 0.5),
                            shadowOffsetY: 8
                        }
                    },
                    symbol: 'circle',//数据交叉点样式
                    areaStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(
                                0,
                                0,
                                0,
                                1,
                                [{
                                    offset: 0,
                                    color: hexToRgba(color[0], 0.3)
                                },
                                    {
                                        offset: 1,
                                        color: hexToRgba(color[0], 0.1)
                                    }
                                ],
                                false
                            ),
                            shadowColor: hexToRgba(color[0], 0.1),
                            shadowBlur: 10
                        }
                    },
                    data: goodsClickData
                }, {
                    name: "用户浏览量",
                    type: "line",
                    smooth: true,
                    symbolSize: 8,
                    zlevel: 3,
                    lineStyle: {
                        normal: {
                            color: color[1],
                            shadowBlur: 3,
                            shadowColor: hexToRgba(color[1], 0.5),
                            shadowOffsetY: 8
                        }
                    },
                    symbol: 'circle',
                    areaStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(
                                0,
                                0,
                                0,
                                1,
                                [{
                                    offset: 0,
                                    color: hexToRgba(color[1], 0.3)
                                },
                                    {
                                        offset: 1,
                                        color: hexToRgba(color[1], 0.1)
                                    }
                                ],
                                false
                            ),
                            shadowColor: hexToRgba(color[1], 0.1),
                            shadowBlur: 10
                        }
                    },
                    data: userClickData
                }]
            };
            return option;
        }


    });

</script>