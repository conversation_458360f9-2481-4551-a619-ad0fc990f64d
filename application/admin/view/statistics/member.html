{layout name="layout1" /}

<div class="layui-col-md12">
    <div class="layui-fluid">
        <div class="layui-form" lay-filter="">
            <!--概况统计-->
            <div class="layui-card" >
                <div class="layui-card-body">
                    <div class="layui-inline">
                        <div class="layui-btn-group">
                            <button type="button"  day="1" class="layui-btn layui-btn-sm layui-btn-primary day">今天</button>
                            <button type="button" id="seven"  day="7" class="layui-btn layui-btn-sm layui-btn-normal day">近7天</button>
                            <button type="button"  day="15" class="layui-btn layui-btn-sm layui-btn-primary day">近15天</button>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">时间筛选:</label>
                        <div class="layui-input-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="start_time" class="layui-input" id="start_time"
                                       placeholder="" autocomplete="off">
                            </div>
                        </div>
                        <div class="layui-input-inline" style="width: 5px;">
                            <label class="layui-form-mid">-</label>
                        </div>
                        <div class="layui-input-inline">
                            <input type="text" name="end_time" class="layui-input" id="end_time"
                                   placeholder="" autocomplete="off">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <a class="layui-btn layui-btn-sm layuiadmin-btn-ad {$view_theme_color}" lay-submit lay-filter="search">筛选</a>
                        <a class="layui-btn layui-btn-sm layuiadmin-btn-ad layui-btn-primary " lay-submit lay-filter="clear-search">清空</a>
                    </div>
                </div>
            </div>

            <!--概况统计-->
            <div class="layui-row layui-col-space15">
                <div class="layui-col-sm6 layui-col-md3" >
                    <div class="layui-card" >
                        <div class="layui-card-header" >
                            会员数量
                        </div>
                        <div class="layui-card-body layuiadmin-card-list" >
                            <p class="layuiadmin-big-font" id="count_user">0</p>
                        </div>
                    </div>
                </div>

                <div class="layui-col-sm6 layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            新增会员数量
                        </div>
                        <div class="layui-card-body layuiadmin-card-list">
                            <p class="layuiadmin-big-font" id="add_user">0</p>
                        </div>
                    </div>
                </div>
            </div>

            <!--会员分析图-->
            <div class="layui-card" style="margin-top:20px">
                <div class="layui-card-body">
                    <div class="layui-form-item">
                        <div id="memberCharts" style="width: 100%;height: 80vh;"></div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>


<script>

    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like','echarts','form', 'laydate'], function(){
        var $ = layui.$
            ,form = layui.form
            ,like = layui.like
            ,echarts = layui.echarts
            ,laydate = layui.laydate;

        const defaultSearch = {start_time:'{$time.days_ago7[0]}', end_time:'{$time.days_ago7[1]}'};
        // 图表配置
        const colorList = ["#9E87FF", '#73DDFF', '#fe9a8b', '#F56948', '#9E87FF'];

        // 首次加载数据
        loadData(defaultSearch);

        //监听搜索
        form.on('submit(search)', function(data) {
            formattingDate();
            loadData(data.field);
        });

        //清空查询
        form.on('submit(clear-search)', function() {
            formattingDate();
            $('#seven').trigger("click");
            loadData(defaultSearch);
        });


        // 日期时间范围
        laydate.render({
            elem: '#start_time'
            ,theme: '#1E9FFF'
            , value: "{$time.days_ago7[0]}"
        });

        laydate.render({
            elem: '#end_time'
            ,theme: '#1E9FFF'
            ,value: "{$time.days_ago7[1]}"
        });

        // 切换快捷日期
        $('.day').click(function() {
            $('.day').removeClass('layui-btn-normal');
            $('.day').removeClass('layui-btn-primary');
            $('.day').addClass('layui-btn-primary');
            $(this).removeClass('layui-btn-primary');
            $(this).addClass('layui-btn-normal');
            var day = $(this).attr('day');
            switch (day) {
                case '1':
                    $('#start_time').val('{$time.today[0]}');
                    $('#end_time').val('{$time.today[1]}');
                    break;
                case '7':
                    $('#start_time').val('{$time.days_ago7[0]}');
                    $('#end_time').val('{$time.days_ago7[1]}');
                    break;
                case '15':
                    $('#start_time').val('{$time.days_ago15[0]}');
                    $('#end_time').val('{$time.days_ago15[1]}');
                    break;
            }
        });


        // 加载数据
        function loadData(data) {
            like.ajax({
                url: '{:url("statistics/member")}',
                data: data,
                type: "get",
                success: function (res) {
                    $('#count_user').html(res.data.count);
                    $('#add_user').html(res.data.new);

                    var memberNum =  res.data.echarts_count;
                    var newMember =  res.data.echarts_new;
                    var xData     = res.data.days;
                    var option    = setOption(memberNum, newMember, xData);

                    let chart     = document.getElementById('memberCharts');
                    let memberChart = echarts.init(chart);
                    memberChart.setOption(option, true);
                    window.addEventListener('resize', function () {
                        memberChart.resize()
                    });
                }
            });
        }

        // 格式化快捷日期
        function formattingDate() {
            $('.day').removeClass('layui-btn-normal');
            $('.day').removeClass('layui-btn-primary');
            $('.day').addClass('layui-btn-primary');
        }

        // 设置图表
        function setOption(memberNum, newMember, xData) {
            console.log(xData);
            option = {
                backgroundColor: '#fff',
                legend: {
                    icon: 'circle',
                    top: '5%',
                    right: '5%',
                    itemWidth: 6,
                    itemGap: 20,
                    textStyle: {
                        color: '#556677'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: '#fff',
                    textStyle: {
                        color: '#5c6c7c'
                    },
                    padding: [10, 10],
                    extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)'
                },
                grid: {
                    top: '15%'
                },
                xAxis: [{
                    type: 'category',
                    name: '(日期)',
                    data: xData,
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(107,107,107,0.37)', //x轴颜色
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        interval: 0,
                        textStyle: {
                            color: '#999' //坐标轴字颜色
                        },
                        margin: 15
                    },
                    axisPointer: {
                        label: {
                            padding: [11, 5, 7],
                            backgroundColor: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0,
                                    color: '#fff' // 0% 处的颜色
                                }, {
                                    offset: 0.9,
                                    color: '#fff' // 0% 处的颜色
                                }, {
                                    offset: 0.9,
                                    color: '#33c0cd' // 0% 处的颜色
                                }, {
                                    offset: 1,
                                    color: '#33c0cd' // 100% 处的颜色
                                }],
                                global: false // 缺省为 false
                            }
                        }
                    },
                    boundaryGap: false
                }],
                yAxis: [{
                    type: 'value',
                    name: '(人)',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(107,107,107,0.37)', //y轴颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#999'
                        }
                    },
                    splitLine: {
                        show: false
                    }
                }],
                series: [{
                    name: '会员数量',
                    type: 'line',
                    data: memberNum,
                    symbolSize: 1,
                    symbol: 'circle',
                    smooth: true,
                    yAxisIndex: 0,
                    showSymbol: false,
                    lineStyle: {
                        width: 5,
                        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                            offset: 0,
                            color: '#9effff'
                        },
                            {
                                offset: 1,
                                color: '#9E87FF'
                            }
                        ]),
                        shadowColor: 'rgba(158,135,255, 0.3)',
                        shadowBlur: 10,
                        shadowOffsetY: 20
                    },
                    itemStyle: {
                        normal: {
                            color: colorList[0],
                            borderColor: colorList[0]
                        }
                    }
                }, {
                    name: '新增会员数量',
                    type: 'line',
                    data: newMember,
                    symbolSize: 1,
                    symbol: 'circle',
                    smooth: true,
                    yAxisIndex: 0,
                    showSymbol: false,
                    lineStyle: {
                        width: 5,
                        color: new echarts.graphic.LinearGradient(1, 1, 0, 0, [{
                            offset: 0,
                            color: '#73DD39'
                        },
                            {
                                offset: 1,
                                color: '#73DDFF'
                            }
                        ]),
                        shadowColor: 'rgba(115,221,255, 0.3)',
                        shadowBlur: 10,
                        shadowOffsetY: 20
                    },
                    itemStyle: {
                        normal: {
                            color: colorList[1],
                            borderColor: colorList[1]
                        }
                    }
                }]
            };
            return option;
        }

    });

</script>