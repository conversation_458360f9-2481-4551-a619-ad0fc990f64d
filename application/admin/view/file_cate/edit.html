{layout name="layout2" /}
<style>
    .layui-form-label {
        color: #6a6f6c;
    }
</style>


<div class="layui-form" lay-filter="layuiadmin-form-cate" id="layuiadmin-form-cate" style="padding: 20px 30px 0 0;">
    <input type="hidden" value="{$detail.id}" name="id">
    <div class="layui-form-item">
        <label class="layui-form-label">上级分类</label>
        <div class="layui-input-inline">
            <select name="pid" lay-verify="required" placeholder="请选择父级分类" >
                <option value="0">顶级分类</option>
                {foreach $cate_tree as $item => $val}
                <option value="{$item}" {if condition="$item eq $detail.pid "}selected="selected"{/if}> {$val}</option>
                {/foreach}
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">分类名称</label>
        <div class="layui-input-inline">
            <input type="text" name="name" value="{$detail.name}" lay-verify="required" lay-verType="tips"  placeholder="请输入名称" autocomplete="off" class="layui-input">
        </div>
    </div>


    <div class="layui-form-item">
        <label class="layui-form-label">排序</label>
        <div class="layui-input-inline">
            <input type="numbernumber" name="sort" value="{$detail.sort | default = 50}" autocomplete="off" class="layui-input"  lay-verify="required" placeholder="请输入排序，数字越大越靠前" lay-vertype="tips" >
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="edit-cate-submit" id="edit-cate-submit" value="确认">
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'form','like'], function(){
        var $ = layui.$
            ,form = layui.form
            ,like = layui.like;
    })
</script>