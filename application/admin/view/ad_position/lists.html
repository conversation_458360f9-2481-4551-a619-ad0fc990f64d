{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
        <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
            <div class="layui-colla-item">
                <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                <div class="layui-colla-content layui-show">
                    <p>*平台管理广告位信息，系统默认了部分广告位，允许新建广告位。</p>
                    <p>*广告位区分不同的终端，移动端商城（含移动端商城、小程序、APP）和PC端商城。</p>
                    <p>*广告位停用时，该广告位所有广告都会隐藏。</p>
                </div>
            </div>
        </div>
        </div>
        <div class="layui-tab layui-tab-card" lay-filter="tab-all">

            <ul class="layui-tab-title">
                {foreach $type as $item => $val}
                <li data-type = {$item} {if $item == 1} class="layui-this" {/if}  >{$val}</li>
                {/foreach}
            </ul>

            <div class="layui-tab-item layui-show">
                <div class="layui-card">

                    <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">广告位:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="keyword" id="keyword" placeholder="请输入广告标题" autocomplete="off" class="layui-input">
                                </div>
                            </div>

                            <div class="layui-inline">
                                <label class="layui-form-label">广告位属性:</label>
                                <div class="layui-input-block">
                                    <select name="attr" id="attr">
                                        <option value="">全部</option>
                                        <option value="1">系统默认</option>
                                        <option value="0">自定义</option>
                                    </select>
                                </div>
                            </div>


                            <div class="layui-inline">
                                <button class="layui-btn layui-btn-sm  layuiadmin-btn-ad_position_position {$view_theme_color}" lay-submit lay-filter="ad_position-search">查询</button>
                                <button class="layui-btn layui-btn-sm layuiadmin-btn-ad_position layui-btn-primary " lay-submit lay-filter="ad_position-clear-search">清空查询</button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <div style="padding-bottom: 10px;">
                            <button class="layui-btn layui-btn-sm layuiadmin-btn-ad_position {$view_theme_color}" data-type="add">新增广告位</button>
<!--                            <button class="layui-btn layui-btn-sm layuiadmin-btn-ad_position layui-btn-danger" data-type="batch_del">删除所选</button>-->
                        </div>
                        <table id="ad_position-lists" lay-filter="ad_position-lists"></table>
                        <script type="text/html" id="attr-info">
                            {{# if(d.attr == 1){ }}
                            系统默认
                            {{# }else{}}
                            自定义
                            {{#}}}
                        </script>
                        <script type="text/html" id="status-info">
                            {{# if(d.status == 1){ }}
                            启动
                            {{# }else{}}
                            停用
                            {{#}}}
                        </script>
                        <script type="text/html" id="ad_position-operation">
                            <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">编辑</a>
                            {{# if(d.status == 1){ }}
                            <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="status_switch">停用</a>
                            {{# }else{ }}
                            <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="status_switch">启用</a>
                            {{#} }}
                            <a class="layui-btn layui-btn-danger layui-btn-sm"  lay-event="del"{{#  if(d.attr=='1'){ }} style="display: none" {{#  } }} >删除</a>
                        </script>

                        <script type="text/html" id="image">
                            <img src="{{d.image}}" style="height:80px;width: 80px" class="image-show">
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,like = layui.like
            ,element = layui.element;

        var client = 1;


        //监听搜索
        form.on('submit(ad_position-search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('ad_position-lists', {
                where: field,
                page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });
        //清空查询
        form.on('submit(ad_position-clear-search)', function(){
            $('#keyword').val('');
            $('#attr').val('');
            $('#client').val('');
            form.render('select');
            //刷新列表
            table.reload('ad_position-lists', {
                where: [],
                page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });


        //事件
        var active = {
            add: function(){
                var add_page = layer.open({
                    type: 2
                    ,title: '新增广告位'
                    ,content: '{:url("AdPosition/add")}?client='+client
                    ,area: ['60%','60%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'add-ad_position-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            field['client'] = client;
                            like.ajax({
                                url:'{:url("AdPosition/add")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('ad_position-lists'); //数据刷新
                                    }
                                },
                            });
                        });

                        submit.trigger('click');
                    }
                });
            }
            ,batch_del:function(){ //删除所选
                var checkStatus = table.checkStatus('ad_position-lists')
                    ,checkData = checkStatus.data; //得到选中的数据
                //是否已选数据
                if(checkData.length === 0){
                    return layer.msg('请选择数据');
                }else {
                    //获取所选id
                    ids = [];
                    attrs = [];
                    for (var i in checkData){
                        ids.push(checkData[i]['id']);
                        attrs.push(checkData[i]['attr']);
                    }

                    layer.confirm('确定删除所选广告位？', function(index){
                        like.ajax({
                            url:'{:url("AdPosition/del")}',
                            data:{delData:ids,client:client,attr:attrs},
                            type:"post",
                            success:function(res)
                            {
                                if(res.code == 1)
                                {
                                    layui.layer.msg(res.msg, {
                                        offset: '15px'
                                        , icon: 1
                                        , time: 1000
                                    });
                                    layer.close(index); //关闭弹层
                                    table.reload('ad_position-lists'); //数据刷新
                                }
                            }
                        });
                        layer.close(index);
                    });
                }
            }
        }
        $('.layui-btn.layuiadmin-btn-ad_position').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
        getList(client)

        element.on('tab(tab-all)', function (data) {
            client = $(this).attr('data-type');

            getList(client);
        });

        function getList(client) {

                //管理员管理
                table.render({
                    elem: '#ad_position-lists'
                    , url: '{:url("AdPosition/lists")}?client='+client  //模拟接口
                    , cols: [[
                        {field: 'id', title: 'id',width:60}
                        ,{field: 'client_name', title: '终端',width:200}
                        , {field: 'name', title: '广告位', align: 'center'}
                        , {field: 'attr_show', title: '广告位属性', align: 'center',toolbar: '#attr-info'}
                        , {field: 'status', title: '广告位状态', align: 'center',toolbar: '#status-info'}
                        , {fixed: 'right', title: '操作',  align: 'center', toolbar: '#ad_position-operation', width: 200}
                    ]]
                    , page: true
                    , text: {none: '暂无数据！'}
                    , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                        return {
                            "code": res.code,
                            "msg": res.msg,
                            "count": res.data.count, //解析数据长度
                            "data": res.data.list, //解析数据列表
                        };
                    }
                    ,done: function(res, curr, count){
                        // 解决操作栏因为内容过多换行问题
                        $(".layui-table-main tr").each(function (index, val) {
                            $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                            $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                        });
                    }
                });
        }

        //监听工具条
        table.on('tool(ad_position-lists)', function(obj){
            if(obj.event === 'del'){
                var id = obj.data.id;
                var attr = obj.data.attr;
                var name = obj.data.name;
                layer.confirm('确认删除广告位：'+'<span style="color: red">'+name+'</span>', function(index){
                    like.ajax({
                        url:'{:url("AdPosition/del")}',
                        data:{delData:id,client:client,attr:attr},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1)
                            {
                                // obj.del();
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                                layer.close(index); //关闭弹层
                                table.reload('ad_position-lists'); //数据刷新
                            }
                        },
                    });
                });
            }else if(obj.event === 'edit'){
                var id = obj.data.id;
                var edit_page = layer.open({
                    type: 2
                    ,title: '编辑广告位'
                    ,content: '{:url("AdPosition/edit")}?id='+id
                    ,area: ['60%','60%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'edit-ad_position-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var field = data.field;
                            field['client'] = client;
                            like.ajax({
                                url:'{:url("AdPosition/edit")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('ad_position-lists'); //数据刷新
                                    }
                                },
                            });
                        });

                        submit.trigger('click');
                    }
                })
            }else if(obj.event === 'status_switch'){
                var id = obj.data.id;
                var status = obj.data.status;
                var name = obj.data.name;
                if (status == 1){
                    confirm_text = '确定停用广告位：';
                    status = 0;
                }else {
                    var confirm_text = '确定启用广告位：';
                    status = 1;
                }

                layer.confirm(confirm_text +'<span style="color: red">'+name+'</span>', function(index){
                    like.ajax({
                        url:'{:url("AdPosition/switchStatus")}',
                        data:{id:id,status:status,client:client},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1)
                            {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                });
                                table.reload('ad_position-lists');
                            }
                        }
                    });
                })


            }
        });
    });
</script>