{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*会员账户余额变动记录。</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form layui-card-header layuiadmin-card-header-auto">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">订单搜索:</label>
                    <div class="layui-input-inline" style="width: 200px;">
                        <select name="keyword_type" id="keyword_type">
                            <option value="sn">会员编号</option>
                            <option value="nickname">会员昵称</option>
                            <option value="mobile">会员手机号码</option>
                        </select>
                    </div>
                    <div class="layui-input-inline" style="width: 200px;">
                        <input type="text" id="keyword" name="keyword" placeholder="请输入关键词" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">变动类型：</label>
                    <div class="layui-input-inline">
                        <select name="order_source" id="order_source">
                            <option value="0">全部</option>
                            {foreach $order_source as $value}
                            <option value={$value.source}>{$value.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">下单时间:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input time" id="start_time" name="start_time"  autocomplete="off">
                    </div>
                    <div class="layui-input-inline" style="margin-right: 5px;width: 10px;">
                        <label class="layui-form-mid">-</label>
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input time" id="end_time" name="end_time"  autocomplete="off">
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-btn-group">
                        <button type="button" id="today" day="1" class="layui-btn layui-btn-sm layui-btn-primary day">今天</button>
                        <button type="button"  day="-1" class="layui-btn layui-btn-sm  layui-btn-primary day">昨天</button>
                        <button type="button"  day="7" class="layui-btn layui-btn-sm layui-btn-primary day">近7天</button>
                        <button type="button"  day="30" class="layui-btn layui-btn-sm layui-btn-primary day">近30天</button>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm layuiadmin-btn-account {$view_theme_color}" lay-submit lay-filter="account-search">查询</button>
                    <button class="layui-btn layui-btn-sm layuiadmin-btn-account layui-btn-primary }" lay-submit lay-filter="account-clear-search">清空查询</button>
                </div>
            </div>

        </div>

        <div class="layui-card-body">
            <table id="account-lists" lay-filter="account-lists"></table>
        </div>
    </div>
</div>
<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like','laydate'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,like = layui.like
            ,laydate = layui.laydate;
        //日期时间范围

        laydate.render({
            elem: '#start_time'
            , type: 'datetime'
            ,theme: '#1E9FFF'
            , value: ""
        });

        laydate.render({
            elem: '#end_time'
            , type: 'datetime'
            ,theme: '#1E9FFF'
            ,value: ""
        });

        //监听搜索
        form.on('submit(account-search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('account-lists', {
                where: field,
                page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });

        //清空查询
        form.on('submit(account-clear-search)', function(){
            $('.day').removeClass('layui-btn-normal');
            $('.day').removeClass('layui-btn-primary');
            $('.day').addClass('layui-btn-primary');
            $('#keyword').val('');
            $('#keyword_type').val('sn');
            $('#pay_status').val(0);
            $('#order_source').val(0);
            $('#pay_way').val(0);
            $('#create_start').val('');
            $('#create_end').val('');
            $('#start_time').val('');
            $('#end_time').val('');
            form.render('select');
            //刷新列表
            table.reload('account-lists', {
                where: [],
                page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });
    });



    layui.define(['table', 'form'], function(exports){
        var $ = layui.$
            ,table = layui.table
            ,form = layui.form;

        //管理员管理
        table.render({
            id:'account-lists'
            ,elem: '#account-lists'
            ,url: '{:url("scoreLog/capitalList")}?type='+1 //模拟接口
            ,cols: [[
                {field: 'nickname',title: '会员昵称'}
                ,{field: 'sn', title: '会员编号'}
                ,{field: 'mobile', title: '手机号码'}
                ,{field: 'change_amount', title: '变动贡献值'}
                ,{field: 'left_amount', title: '剩余贡献值'}
                ,{field: 'remark', title: '变动类型'}
                ,{field: 'log_sn', title: '来源单号'}
                ,{field: 'create_time', title: '记录时间'}

            ]]
            ,page:true
            ,text: {none: '暂无数据！'}
            ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
                return {
                    "code":res.code,
                    "msg":res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            }
            , done: function fix() {
                $(".layui-table-main tr").each(function (index, val) {
                    $(".layui-table-fixed").each(function () {
                        $($(this).find(".layui-table-body tbody tr")[index]).height($(val).height());
                    });
                });
                $(".layui-table-header tr").each(function (index, val) {
                    $(".layui-table-fixed").each(function () {
                        $($(this).find(".layui-table-header thead tr")[index]).height($(val).height());
                    });
                });
                window.onresize = function () {
                    fix()
                }
            }
        });
        $('.day').click(function(){
            $('.day').removeClass('layui-btn-normal');
            $('.day').removeClass('layui-btn-primary');
            $('.day').addClass('layui-btn-primary');
            $(this).removeClass('layui-btn-primary');
            $(this).addClass('layui-btn-normal');
            var day = $(this).attr('day');
            switch (day) {
                case '-1':
                    $('#start_time').val('{$time.yesterday[0]}');
                    $('#end_time').val('{$time.yesterday[1]}');
                    break;
                case '1':
                    $('#start_time').val('{$time.today[0]}');
                    $('#end_time').val('{$time.today[1]}');
                    break;
                case '7':
                    $('#start_time').val('{$time.days_ago7[0]}');
                    $('#end_time').val('{$time.days_ago7[1]}');
                    break;
                case '30':
                    $('#start_time').val('{$time.days_ago30[0]}');
                    $('#end_time').val('{$time.days_ago30[1]}');
                    break;
            }
        });

    });
</script>