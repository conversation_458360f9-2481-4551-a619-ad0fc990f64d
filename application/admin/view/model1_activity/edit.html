{layout name="layout2" /}

<style>
    .layui-form-label {
        width: 110px;
    }
</style>

<div class="layui-card layui-form">
    <div class="layui-card-body">
        <!-- 选择商品 -->
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>活动商品：</label>
            <div class="layui-input-block">
                <a class="layui-btn layui-btn-normal select-goods">选择活动商品</a>
            </div>
        </div>
        <!-- 商品信息 -->
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-block">
                <div class="goods_info">
                    <img src="{$detail.image}" alt="商品图片" style="width:80px;height:80px;">
                    <span style="margin-left:5px;">{$detail.name}</span>
                    <input type="hidden" name="goods_id" value="{$detail.goods_id}">
                </div>
                <table id="goods_list" class="layui-table" lay-size="sm" style="width: 630px;">
                    <thead>
                        <tr style="background-color: #f3f5f9">
                            <th style="width: 120px;text-align: center">商品规格</th>
                            <th style="width: 60px;text-align: center">商品价格</th>
                            <th style="width: 40px;text-align: center">活动价格</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="$detail.item" id="vo"}
                        <tr>
                            <td style="text-align: center">{$vo.spec_value_str}</td>
                            <td style="text-align: center">{$vo.spec_item_price}</td>
                            <td style="width: 40px;">
                                <input onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, \'$1\')"
                                       type="number" name=item[{$detail.goods_id}][{$vo.spec_item_id}]
                                       lay-verType="tips" lay-verify="required" autocomplete="off"
                                       class="layui-input" value="{$vo.model1_price}">
                            </td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
            </div>
        </div>
        <!-- 活动人数 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label for="people_num" class="layui-form-label"><font color="red">*</font>活动人数：</label>
            <div class="layui-input-inline">
                <input type="hidden" name="model1_id" value="{$detail.model1_id}">
                <input type="number" id="people_num" name="people_num" value="{$detail.people_num}" class="layui-input" autocomplete="off"
                       onkeyup="value=value.replace(/[^\d]/g,'')"
                       lay-verType="tips" lay-verify="required|number|people_num">
            </div>
            <div class="layui-form-mid layui-word-aux">人</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux">最少两人完成，设置商品活动人数</div>
            </div>
        </div>
        <!-- 活动时效 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label for="time_limit" class="layui-form-label"><font color="red">*</font>活动时效：</label>
            <div class="layui-input-inline">
                <input type="number" id="time_limit" name="time_limit" value="{$detail.time_limit}" class="layui-input" autocomplete="off"
                       onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                       lay-verType="tips" lay-verify="required|number|time_limit">
            </div>
            <div class="layui-form-mid layui-word-aux">小时</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux">活动有效期，超出时间还未完成则活动失败</div>
            </div>
        </div>
        <!-- 活动活动时间 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label class="layui-form-label" style="width:110px;"><font color="red">*</font>活动活动时间：</label>
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <input type="text" id="start_time" name="start_time" value="{$detail.start_time}" class="layui-input" autocomplete="off" lay-verType="tips" lay-verify="required">
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <input type="text" id="end_time" name="end_time" value="{$detail.end_time}" class="layui-input" autocomplete="off" lay-verType="tips" lay-verify="required">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width:110px;"></label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux">活动活动时间，超出活动时间活动商品自动失效</div>
            </div>
        </div>

        <!-- 拼够分享标题 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label for="share_title" class="layui-form-label" style="width:110px;">活动分享标题：</label>
            <div class="layui-input-inline" style="width: 300px;">
                <input type="text" id="share_title" name="share_title" value="{$detail.share_title}" class="layui-input" autocomplete="off" lay-verType="tips">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width:110px;"></label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux">分享活动活动时的标题，为空时默认使用商品标题</div>
            </div>
        </div>

        <!-- 拼够分享简介 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label for="share_intro" class="layui-form-label" style="width:110px;">活动分享简介：</label>
            <div class="layui-input-inline" style="width: 300px;">
                <input type="text" id="share_intro" name="share_intro" value="{$detail.share_intro}"  class="layui-input" autocomplete="off" lay-verType="tips">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width:110px;"></label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux">分享活动活动时的简介，为空时默认使用商品简介</div>
            </div>
        </div>


        <!-- 活动状态 -->
        <div class="layui-form-item" style="margin-bottom: 0;">
            <label class="layui-form-label"><font color="red">*</font>活动状态：</label>
            <div class="layui-input-inline">
                <input type="radio" name="status" value="1" title="开启" {if $detail.status}checked{/if}>
                <input type="radio" name="status" value="0" title="关闭" {if !$detail.status}checked{/if}>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux">活动开启并且在活动活动时间内才能进行活动活动</div>
            </div>
        </div>

        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
        </div>

    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index'
    }).use(['index', 'form', 'like', 'laydate'], function() {
        var $ = layui.$
            , form = layui.form
            , like = layui.like
            , laydate = layui.laydate;

        // 开始时间
        laydate.render({
            type: 'datetime'
            ,elem: '#start_time'
            ,trigger: 'click'
        });

        // 结束时间
        laydate.render({
            type: 'datetime'
            ,elem: '#end_time'
            ,trigger: 'click'
        });

        // 选择商品
        $(document).on('click','.select-goods',function () {
            layer.open({
                type: 2
                ,title: '选择商品'
                ,content: '{:url("common/selectGoods")}'
                ,area: ['90%', '90%']
                ,btn: ['确认', '取消']
                ,yes: function(index, layero){
                    var data = window["layui-layer-iframe" + index].callbackdata();
                    if(data.length){
                        goods_ids = [];
                        $('#goods_list tbody').remove();
                    }
                    data.forEach(function(item, index) {
                        console.log(item);
                        var goods_info_html = '<img src="'+item.image+'" alt="商品图片" style="width:80px;height:80px;">\n' +
                                              '<span style="margin-left:5px;">'+item.name+'</span>\n' +
                                              '<input type="hidden" name="goods_id" value="'+item.id+'">';
                        $(".goods_info").html(goods_info_html);
                        $("#goods_list").show();
                        for(var i = 0; item.goods_item.length > i; i++){
                            var goods_item = item.goods_item[i];
                            if(goods_ids.indexOf(goods_item.id) === -1) {
                                goods_ids.push(goods_item.id);
                                var goods_html = '<tr>\n' +
                                    '<td style="text-align: center">'+goods_item.spec_value_str+'</td>\n' +
                                    '<td style="text-align: center">'+goods_item.price+'</td>\n' +
                                    '<td style="width: 40px;"><input onkeyup="value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, \'$1\')" type="number" name=item['+item.id+']['+goods_item.id+'] lay-verType="tips" lay-verify="required" autocomplete="off" class="layui-input"></td>\n' +
                                    '</tr>';
                                $('#goods_list').append(goods_html);
                            }
                        }
                    });
                    $('.goods').show();
                }
            })
        });

        // 表单验证
        form.verify({
            people_num: function(value){
                if(parseInt(value) < 2){
                    return '活动人数需大于等于2';
                }
            },
            time_limit: function(value){
                if(parseFloat(value) <= 0){
                    return '完成有效期不能少于等于0';
                }
            }
        });

    });
</script>