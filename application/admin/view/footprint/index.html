{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-form">
        <!-- 操作提示 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                        <div class="layui-colla-content layui-show">
                            <p>*设置商城首页，商品详情页显示足迹气泡。营造活动氛围，增强气氛。</p>
                            <p>*注意事项：设置中的足迹气泡开关是整个气泡的总开关,如果这里关了则任何地方都不显示气泡。</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 选项卡 -->
            <div class="layui-tab layui-tab-card">
                <ul class="layui-tab-title">
                    <li data-type=2 class="layui-this">足迹气泡</li>
                    <li data-type=1>设置</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 足迹气泡 -->
                    <div class="layui-tab-item layui-show">
                        <div class="bubble">
                            {volist name="footprint" id="vo"}
                            <div class="layui-card">
                                <div class="layui-card-header">{$vo.name}</div>
                                <div class="layui-card-body">
                                    <div class="bubble-content">
                                        <button type="button" data-id="{$vo.id}" class="layui-btn layui-btn-normal layui-btn-sm edit-bubble">编辑</button>
                                        {if $vo.status}<span>已开启</span>{else}<span>已关闭</span>{/if}
                                    </div>
                                </div>
                            </div>
                            {/volist}
                        </div>
                    </div>
                    <!-- 设置模块 -->
                    <div class="layui-tab-item">
                        <form class="layui-form">
                            <div class="layui-form-item">
                                <label for="duration" class="layui-form-label" style="width:110px;">足迹气泡时长：</label>
                                <div class="layui-input-inline" style="width:220px;">
                                    <input type="number" id="duration" name="duration" lay-verify="duration"  value="{$set.footprint_duration}" autocomplete="off" class="layui-input">
                                    <p style="color:#ccc;font-size: 13px;">查询多长时间范围内的足迹信息</p>
                                </div>
                                <div class="layui-input-inline" style="line-height: 38px;">分钟</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width:110px;">足迹气泡状态：</label>
                                <div class="layui-input-inline" style="width:220px;">
                                    <input type="radio" name="status" value="1" title="开启" {if $set.footprint_status}checked{/if}>
                                    <input type="radio" name="status" value="0" title="关闭"  {if !$set.footprint_status}checked{/if}>
                                    <p style="color:#ccc;font-size: 13px;">开启还是关闭足迹气泡，默认开启</p>
                                </div>
                            </div>
                            <div class="layui-form-item ">
                                <div class="layui-input-block">
                                    <input type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="update-set-submit" id="update-set-submit" value="确定">
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .bubble { display: flex;justify-content: start;flex-wrap: wrap; }
    .bubble .layui-card { background:#eee; width:19%; min-width:155px; height:100px; margin-right:10px; }
    .bubble-content { padding-top: 10px; font-size: 14px; display: flex; justify-content: space-between;}
</style>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'like','form'], function () {
        var $ = layui.$
            , form = layui.form
            , like = layui.like
            ,element = layui.element;
        form.render();
        form.verify({
            duration: function (value){
                if(value < 0){
                    return '足迹气泡时长不能小于零分钟';
                }
            }
        });
        // 编辑气泡
        $(document).on('click', '.edit-bubble', function () {
            var id = $(this).attr('data-id');
            layer.open({
                type: 2
                ,title: '编辑足迹气泡'
                ,content: '{:url("footprint/edit")}?id='+id
                ,area: ['400px', '400px']
                ,btn: ['保存', '取消']
                ,maxmin: true
                ,yes: function(index, layero){
                    var iframeWindow = window['layui-layer-iframe'+ index]
                        ,submitID = 'edit-bubble-submit'
                        ,submit = layero.find('iframe').contents().find('#'+ submitID);
                    //监听提交
                    iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                        var field = data.field;
                        like.ajax({
                            url:'{:url("footprint/edit")}',
                            data:field,
                            type:"post",
                            success:function(res) {
                                if(res.code === 1) {
                                    layui.layer.msg(res.msg, {offset:'15px', icon:1, time: 1000});
                                    location.href = '{:url("footprint/index")}';
                                    layer.close(index);
                                } else {
                                    layui.layer.msg(res.msg, {offset:'15px', icon:2, time: 1000});
                                }
                            }
                        });
                    });
                    submit.trigger('click');
                }
            });
        });

        // 更新设置
        form.on('submit(update-set-submit)', function(data){
            like.ajax({
                url:'{:url("footprint/set")}',
                data:data.field,
                type:"post",
                success:function(res) {
                    if(res.code === 1) {
                        layui.layer.msg(res.msg, {offset:'15px', icon:1, time: 1000});
                        // location.href = '{:url("footprint/index")}';
                        // layer.close(index);
                    } else {
                        layui.layer.msg(res.msg, {offset:'15px', icon:2, time: 1000});
                    }
                }
            });
            return false;
        });
    });
</script>