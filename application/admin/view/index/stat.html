{layout name="layout1" /}
<style>
    .header-font{
        font-size: 20px
    }
    .layui-card .data-all {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
    }
    .layui-card  .text-style {
        font-size: 16px;
    }
    .header-time{
        margin-left: 20px
    }
    .layadmin-shortcut .shortcut-list  {
        padding: 30px 0;
        display: flex;
        flex-wrap: wrap;
    }
    .layadmin-shortcut .shortcut-list li {
        min-width: 100px;
        margin-bottom: 20px;
        width: 12.5%;
    }
 
    .layadmin-shortcut .shortcut-list .icon{
        width: 62px;
        height: 62px;
        margin-bottom: 5px;
    }

    .tips {
        float: right;
        padding: 0 10px;
        margin-top: 10px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        color: #3A91FB;
        border-radius: 30px;
        border: 1px #3A91FB solid;
        background: #eaf3ff;
    }

</style>
<div class="layui-fluid" style="margin-bottom: 100px">
    <div class="layui-card" >
        <div class="layui-card-header">
            <span class="header-font">运营数据</span>
            <span class="header-time">更新时间：{$res.time}</span>
        </div>
    </div>
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm3">
            <div class="layui-card">
                <div class="layui-card-header">
                    成交订单（笔）
                    <span class="tips">今日</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font">{$res.order_num_today}</p>
                    <p>昨日：<span class="text-style">{$res.order_num_yesterday}</span>
                        <span class="text-style" {if condition="$res.order_num_change lt 0" } style="color: #009688" {elseif condition="$res.order_num_change egt 0" } style="color: #FA2828" {/if}>{$res.order_num_change}</span></p>
                
                        <div class="data-all">成交订单总量（笔）<div class="text-style">{$res.order_num_all}</div></div>
                    </div>
            </div>
        </div>
        <div class="layui-col-sm3 ">
            <div class="layui-card">
                <div class="layui-card-header">
                    销售金额（元）
                    <span class="tips">今日</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font">{$res.order_amount_today}</p>

                    <p>昨日：<span class="text-style">{$res.order_amount_yesterday}</span>
                        <span class="text-style" {if condition="$res.order_amount_change lt 0" } style="color: #009688" {elseif condition="$res.order_amount_change egt 0" } style="color: #FA2828" {/if}>{$res.order_amount_change}</span></p>
                        <div class="data-all">销售总额（元）<div class="text-style">{$res.order_amount_all}</div></div>
                    </div>
            </div>
        </div>
        <div class="layui-col-sm3 ">
            <div class="layui-card">
                <div class="layui-card-header">
                    新增会员（人）
                    <span class="tips">今日</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font">{$res.add_user_today}</p>
                    <p >昨日：<span class="text-style">{$res.add_user_yesterday}</span>
                        <span class="text-style"  {if condition="$res.add_user_change lt 0" } style="color: #009688" {elseif condition="$res.add_user_change egt 0" } style="color: #FA2828" {/if}>{$res.add_user_change}</span></p>
                        <div class="data-all">会员总量（人）<div class="text-style">{$res.user_all}</div></div>
                    </div>
            </div>
        </div>
        <div class="layui-col-sm3 ">
            <div class="layui-card">
                <div class="layui-card-header">
                    用户访问量（人）
                    <span class="tips">今日</span>
                </div>
                <div class="layui-card-body layuiadmin-card-list">
                    <p class="layuiadmin-big-font">{$res.today_user_ip}</p>
                    <p>昨日：<span class="text-style">{$res.yesterday_user_ip}</span>
                        <span class="text-style" {if condition="$res.ip_change lt 0" } style="color: #009688" {elseif condition="$res.ip_change egt 0" } style="color: #FA2828" {/if}>{$res.ip_change}</span></p>
                        <div class="data-all">总用户访问量（人）<div class="text-style">{$res.total_user_ip}</div></div>
                    </div>
            </div>
        </div>

        <!--快捷功能-->
        <div class="layui-col-sm12">
            <div class="layui-card">
                <div class="layui-card-header">快捷功能</div>
                <div class="layui-card-body">
                    <div class="layadmin-shortcut">
                        <ul class="shortcut-list">
                            <li class="shop-item" >
                                <a lay-href="{:url('goods/lists')}" data-id="29">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/goods.png" alt="">
                                    <cite>商品管理</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('user/lists')}" data-id="33">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/user.png" alt="">
                                    <cite>会员管理</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('order/lists')}" data-id="37">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/order.png" alt="">
                                    <cite>订单管理</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('coupon/lists')}" data-id="39">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/coupon.png" alt="">
                                    <cite>优惠券</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('seckill/lists')}" data-id="39">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/seckill.png" alt="">
                                    <cite>限时秒杀</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('distributionMember/index')}" data-id="85">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/distribution.png" alt="">
                                    <cite>分销管理</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('article/lists')}" data-id="65">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/content.png" alt="">
                                    <cite>内容管理</cite>
                                </a>
                            </li>
                            <li class="shop-item">
                                <a lay-href="{:url('recharge_log/lists')}" data-id="81">
                                    <img class="icon" src="__PUBLIC__/static/common/image/workbench/finance.png" alt="">
                                    <cite>财务</cite>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!--表格-->
        <div class="layui-col-sm12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-row">
                        近15天销售金额（元）
                        <div class="layadmin-dataview">
                            <div id="sale-charts" style="width: 100%;height: 100%">
                                <div><i class="layui-icon layui-icon-loading1 layadmin-loading"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="layui-col-sm12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-row">
                        近15天用户访问量UV（人）
                        <div class="layadmin-dataview">
                            <div id="user-charts" style="width: 100%;height: 100%;">
                                <div><i class="layui-icon layui-icon-loading1 layadmin-loading"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<footer class="info_footer">
    {$company_name}&nbsp;&nbsp;|&nbsp;&nbsp;版本号：{$version}
    <br><br>
</footer>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'like','echarts','form','element'], function () {
        var $ = layui.$
            ,like = layui.like
            ,echarts = layui.echarts;

        let bgColor = "#fff";
        let color = [
            "#0090FF",
            "#36CE9E",
            "#FFC005",
            "#FF515A",
            "#8B5CFF",
            "#00CA69"
        ];
        const hexToRgba = (hex, opacity) => {
            let rgbaColor = "";
            let reg = /^#[\da-f]{6}$/i;
            if (reg.test(hex)) {
                rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
                    "0x" + hex.slice(3, 5)
                )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
            }
            return rgbaColor;
        }



        like.ajax({
            url: '{:url("index/stat")}',
            type: "get",
            success: function (res) {
                var dates = res.data.dates,
                    echarts_order_amount  = res.data.echarts_order_amount,
                    echarts_user_visit = res.data.echarts_user_visit;

                var sale_option = {
                    backgroundColor: bgColor,
                    color: color,
                    legend: {
                        right: 10,
                        top: 10
                    },
                    tooltip: {
                        trigger: "axis",
                        formatter: function(params) {
                            let html = '';
                            params.forEach(v => {
                                html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color[v.componentIndex]};"></span>
                ${v.name}
                <span style="color:${color[v.componentIndex]};font-weight:700;font-size: 18px">${v.value}</span>
                元`;
                            })
                            return html
                        },
                        extraCssText: 'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;',
                        axisPointer: {
                            type: 'shadow',
                            shadowStyle: {
                                color: '#ffffff',
                                shadowColor: 'rgba(225,225,225,1)',
                                shadowBlur: 5
                            }
                        }
                    },
                    grid: {
                        top: 100,
                        containLabel: true
                    },
                    xAxis: [{
                        type: "category",
                        boundaryGap: false,
                        axisLabel: {
                            formatter: '{value}',
                            textStyle: {
                                color: "#333"
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#D9D9D9"
                            }
                        },
                        data: dates
                    }],
                    yAxis: [{
                        type: "value",
                        name: '销售金额',
                        axisLabel: {
                            textStyle: {
                                color: "#666"
                            }
                        },
                        nameTextStyle: {
                            color: "#666",
                            fontSize: 12,
                            lineHeight: 40
                        },
                        splitLine: {
                            lineStyle: {
                                type: "dashed",
                                color: "#E9E9E9"
                            }
                        },
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        }
                    }],
                    series: [{
                        type: "line",
                        smooth: true,
                        symbolSize: 8,
                        zlevel: 3,
                        lineStyle: {
                            normal: {
                                color: color[0],
                                shadowBlur: 3,
                                shadowColor: hexToRgba(color[0], 0.5),
                                shadowOffsetY: 8
                            }
                        },
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    0,
                                    1,
                                    [{
                                        offset: 0,
                                        color: hexToRgba(color[0], 0.3)
                                    },
                                        {
                                            offset: 1,
                                            color: hexToRgba(color[0], 0.1)
                                        }
                                    ],
                                    false
                                ),
                                shadowColor: hexToRgba(color[0], 0.1),
                                shadowBlur: 10
                            }
                        },
                        data: echarts_order_amount
                    }]
                };

                var user_option = {
                    backgroundColor: bgColor,
                    color: color,
                    legend: {
                        right: 10,
                        top: 10
                    },
                    tooltip: {
                        trigger: "axis",
                        formatter: function(params) {
                            let html = '';
                            params.forEach(v => {
                                html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color[v.componentIndex]};"></span>
                ${v.name}
                <span style="color:${color[v.componentIndex]};font-weight:700;font-size: 18px">${v.value}</span>
                人`;
                            })
                            return html
                        },
                        extraCssText: 'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;',
                        axisPointer: {
                            type: 'shadow',
                            shadowStyle: {
                                color: '#ffffff',
                                shadowColor: 'rgba(225,225,225,1)',
                                shadowBlur: 5
                            }
                        }
                    },
                    grid: {
                        top: 100,
                        containLabel: true
                    },
                    xAxis: [{
                        type: "category",
                        boundaryGap: false,
                        axisLabel: {
                            formatter: '{value}',
                            textStyle: {
                                color: "#333"
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#D9D9D9"
                            }
                        },
                        data: dates
                    }],
                    yAxis: [{
                        type: "value",
                        name: '用户访问量',
                        axisLabel: {
                            textStyle: {
                                color: "#666"
                            }
                        },
                        nameTextStyle: {
                            color: "#666",
                            fontSize: 12,
                            lineHeight: 40
                        },
                        splitLine: {
                            lineStyle: {
                                type: "dashed",
                                color: "#E9E9E9"
                            }
                        },
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        }
                    }],
                    series: [{
                        type: "line",
                        smooth: true,
                        symbolSize: 8,
                        zlevel: 3,
                        lineStyle: {
                            normal: {
                                color: color[0],
                                shadowBlur: 3,
                                shadowColor: hexToRgba(color[0], 0.5),
                                shadowOffsetY: 8
                            }
                        },
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    0,
                                    1,
                                    [{
                                        offset: 0,
                                        color: hexToRgba(color[0], 0.3)
                                    },
                                        {
                                            offset: 1,
                                            color: hexToRgba(color[0], 0.1)
                                        }
                                    ],
                                    false
                                ),
                                shadowColor: hexToRgba(color[0], 0.1),
                                shadowBlur: 10
                            }
                        },
                        data: echarts_user_visit
                    }]
                };


                var sale_charts= echarts.init(document.getElementById('sale-charts'));
                sale_charts.setOption(sale_option, true);

                var user_charts = echarts.init(document.getElementById('user-charts'));
                user_charts.setOption(user_option, true);

            }
        });




    });
</script>
