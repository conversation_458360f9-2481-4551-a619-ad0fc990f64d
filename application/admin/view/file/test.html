{layout name="layout1" /}
<div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">修改密码</div>
                <div class="layui-card-body" pad15>
                    <div class="layui-form" lay-filter="">
                        <div class="layui-form-item">
                            <label class="layui-form-label">当前密码</label>
                            <div class="layui-input-inline">
                                <input type="text" name="old_password" lay-verify="required" readonly="readonly" lay-verType="tips" class="layui-input plug-image">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">新密码</label>
                            <div class="layui-input-inline">
                                <input type="text" maxlength="16" name="password" lay-verify="required|length" lay-verType="tips" autocomplete="off" class="layui-input plug-image">
                            </div>
                            <div class="layui-form-mid layui-word-aux">6到16个字符</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">确认新密码</label>
                            <div class="layui-input-inline">
                                <input type="password" name="re_password" lay-verify="required|comparison" lay-verType="tips" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn {$view_theme_color}" lay-submit lay-filter="setmypass">确认修改</button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
 <script>


     layui.config({
        version:"{$front_version}",
         base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
     }).extend({
         index: 'lib/index' //主入口模块
     }).use(['index','table','like'], function(){
         $= layui.$;
         like = layui.like;
         like.imageUpload('.plug-image',function(uri,element) {
             element.val(uri);
         });
     });

 </script>