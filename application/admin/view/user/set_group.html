{layout name="layout2" /}
<div class="layui-form" lay-filter="layuiadmin-form-user" id="layuiadmin-form-user" style="padding: 20px 30px 0 0;">

    <div class="layui-form-item">
        <label class="layui-form-label">选择分组：</label>
        <div class="layui-input-inline">
            <select name="group_id" lay-verify="required" placeholder="请选择分组" >
                <option value="">请选择分组</option>
                {foreach $group_list as $item}
                <option value="{$item.id}" >{$item.name}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="set_group-submit" id="set_group-submit" value="确认">
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'form','like_area'], function(){
        var $ = layui.$
            ,like_area = layui.like_area
            ,form = layui.form ;

    })
</script>