{layout name="layout2" /}
<style>
    .layui-form-label{
        width: 120px;
    }
    .layui-card .layui-tab{
        margin-left: 7px;
    }
    .layui-form-label{
        margin-left: 20px;
        width: 98px;
    }
    .layui-input-inline{
        width:160px;
    }
    .layui-table-cell {
        height: auto;
    }

</style>
<div class="layui-form" lay-filter="layuiadmin-form-user_group" id="layuiadmin-form-user_group" style="padding: 20px 30px 0 0;">
    <div class="layui-form-item div-flex">
        <fieldset class="layui-elem-field layui-field-title">
            <legend>推荐信息</legend>
        </fieldset>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">上级推荐人：</label>
        <div class="layui-input-inline" style="width: 100px;">
            {if $detail.first_leader_info != '系统'}
            <label class="layui-form-mid" style="width:300px">{$detail.first_leader_info.nickname}({$detail.first_leader_info.sn})</label>
            {else}
            <label class="layui-form-mid">{$detail.first_leader_info}</label>
            {/if}
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">推荐下级人数：</label>
        <div class="layui-input-inline">
            <label class="layui-form-mid">{$detail.first_fans}人</label>
        </div>
        <div class="layui-input-inline">
            <button class="layui-btn layui-btn-sm layui-bg-blue" id="showFans" data-id="{$detail.id}">查看推荐下级</button>
        </div>
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/modules/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['jquery', 'layer'], function(){
        let $ = layui.jquery;
        let layer = layui.layer;

        // 查看推荐下级
        $('#showFans').click(function() {
            let id = $(this).data('id');
            // 弹窗显示添加页
            layer.open({
                type: 2
                ,title: "查看下级推荐"
                ,content: "{:url('user/fans')}?id=" + id
                ,area: ["90%", "90%"]
                ,btn: ["返回"]
            });
        });
    });
</script>