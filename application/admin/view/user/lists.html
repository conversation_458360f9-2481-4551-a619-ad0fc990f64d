{layout name="layout1" /}
<style>
    .layui-table-cell {
        height: auto;
        white-space: normal;
    }
    .mb10{
        margin-bottom: 10px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
        <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
            <div class="layui-colla-item">
                <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                <div class="layui-colla-content layui-show">
                    <p>*会员列表，对会员进行编辑，账户调整，等级调整和资料查看等操作。</p>
                </div>
            </div>
        </div>
        </div>
        <div class="layui-form layui-card-header layuiadmin-card-header-auto">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">会员信息：</label>
                    <div class="layui-input-inline" style="width: 200px;">
                        <select name="keyword_type" id="keyword_type">
                            <option value="sn">会员编号</option>
                            <option value="nickname">会员昵称</option>
                            <option value="mobile">会员手机号码</option>
                        </select>
                    </div>
                    <div class="layui-input-inline" style="width: 200px;">
                        <input type="text" id="keyword" name="keyword" placeholder="请输入关键词" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">会员等级：</label>
                    <div class="layui-input-inline">
                        <select id="level" name="level"   style="height:80px;width: 80px" >
                            <option value="" selected>全部</option>
                            <option value="0">无等级</option>
                            {foreach $level_list as $item }
                            <option value="{$item.id}">{$item.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">会员分组：</label>
                    <div class="layui-input-inline">
                        <select id="group_id" name="group_id"   style="height:80px;width: 80px" >
                            <option value="">全部</option>
                            {foreach $group_list as $item }
                            <option value="{$item.id}">{$item.name}</option>
                            {/foreach}

                        </select>
                    </div>
                </div>

            </div>
            <div class="layui-form-item">

                <div class="layui-inline">
                    <label class="layui-form-label">消费金额：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="total_amount_start"  autocomplete="off">
                    </div>
                    <div class="layui-input-inline" style="margin-right: 5px;width: 10px;">
                        <label class="layui-form-mid">-</label>
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" name="total_amount_end"  autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">注册时间:</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="start_time" name="start_time"  autocomplete="off">
                    </div>
                    <div class="layui-input-inline" style="margin-right: 5px;width: 10px;">
                        <label class="layui-form-mid">-</label>
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="end_time" name="end_time"  autocomplete="off">
                    </div>
                </div>

                <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm layuiadmin-btn-user {$view_theme_color}" lay-submit lay-filter="user-search">查询</button>
                    <button class="layui-btn layui-btn-sm layuiadmin-btn-user layui-btn-primary " lay-submit lay-filter="user-clear-search">清空查询</button>
                    <button class="layui-btn layui-btn-sm layuiadmin-btn-user layui-btn-primary " lay-submit lay-filter="export-file">导出</button>
                </div>
            </div>

        </div>

        <div class="layui-card-body">
            <div style="padding-bottom: 10px;">
                <button class="layui-btn layui-btn-sm layuiadmin-btn-user {$view_theme_color}" data-type="send_coupon">发放优惠券</button>
                <button class="layui-btn layui-btn-sm layuiadmin-btn-user {$view_theme_color}" data-type="set_group">设置分组</button>
            </div>

            <table id="user-lists" lay-filter="user-lists"></table>

            <script type="text/html" id="user-info">
                <img src="{{d.abs_avatar}}" style="height:80px;width: 80px;margin-right: 10px;" class="image-show">
                <div class="layui-input-inline" style="text-align:left;">
                    <p>用户编号：{{d.sn}}</p>
                    <p>用户昵称：{{d.nickname}}</p>
                    <p>账号：{{d.account}}</p>
                </div>
            </script>

            <script type="text/html" id="first-info">
                {{# if(d.first_leader_info != '系统'){}}
                <img src="{{d.first_leader_info.avatar}}" style="height:80px;width: 80px;margin-right: 10px;" class="image-show">
                <div class="layui-input-inline" style="text-align:left;">
                    <p>用户编号：{{d.first_leader_info.sn}}</p>
                    <p style="width: 180px;text-overflow:ellipsis;overflow: hidden">用户昵称：{{d.first_leader_info.nickname}}</p>
                </div>
                {{# }else{ }}
                {{d.first_leader_info}}
                {{# } }}
            </script>

            <script type="text/html" id="account">
                <div class="layui-input-inline">
                    <label>余额：{{d.user_money}}</label>
                    <br/>
                    <label>贡献值：{{d.user_integral}}</label>
                </div>
            </script>

            <script type="text/html" id="wallet-info">
                <div class="layui-input-inline" style="text-align:left;width: 240px">
                    <p>总资产：{{d.total_amount}}</p>
                    <p>可提现金额：{{d.earnings}}</p>
                    <p>不可提现金额：{{d.user_money}}</p>
                </div>
            </script>

            <script type="text/html" id="user-operation">
                <a class="layui-btn layui-btn-primary layui-btn-sm mb10" lay-event="info">资料</a>
                <a class="layui-btn layui-btn-normal layui-btn-sm mb10" lay-event="edit">编辑</a>
                <a class="layui-btn layui-btn-normal layui-btn-sm mb10"  id="adjust_user" lay-event="adjust_user">账户调整</a>
                <a class="layui-btn layui-btn-normal layui-btn-sm mb10"  id="adjust_level" lay-event="adjust_level">等级调整</a>
                <a class="layui-btn layui-btn-normal layui-btn-sm mb10"  id="adjust_first_leader" lay-event="adjust_first_leader">推荐人调整</a>
                <a class="layui-btn layui-btn-normal layui-btn-sm mb10"  id="adjust_lower_level" lay-event="adjust_lower_level">查看下级</a>
                <a class="layui-btn layui-bg-red layui-btn-sm mb10"  id="delUser" lay-event="delUser">删除</a>

            </script>
        </div>
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like','laydate'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,like = layui.like
            , laydate = layui.laydate;

        //监听搜索
        form.on('submit(user-search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('user-lists', {
                where: field,
                page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });
        //日期时间范围
        laydate.render({
            elem: '#start_time'
            ,type: 'datetime'
            ,trigger: 'click'
        });

        //日期时间范围
        laydate.render({
            elem: '#end_time'
            ,type: 'datetime'
            ,trigger: 'click'
        });


        //清空查询
        form.on('submit(user-clear-search)', function(){
            $('#keyword_type').val('sn');
            $('#keyword').val('');  //清空输入框
            $('#level').val('');  //清空输入框
            $('#group_id').val('');  //清空输入框
            $('#start_time').val('');  //清空输入框
            $('#end_time').val('');  //清空输入框
            form.render('select');
            //刷新列表
            table.reload('user-lists', {
                where: [],
                page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
        });

        // 导出
        form.on('submit(export-file)', function(data){
            var field = data.field;
            $.ajax({
                url: '{:url("user/exportFile")}',
                type: 'get',
                data: field,
                dataType: 'json',
                error: function() {
                    layer.msg('导出超时，请稍后再试!');
                },
                success: function(res) {
                    table.exportFile(res.data.exportTitle,res.data.exportData, res.data.exportExt, res.data.exportName);
                },
                timeout: 15000
            });
            layer.msg('导出中请耐心等待~');
        });


        //事件
        var active = {
            send_coupon:function() { //发放优惠券
                var check_status = table.checkStatus('user-lists')
                    ,user_list = check_status.data; //得到选中的数据

                //是否已选数据
                if(0 === user_list.length ){
                    return layer.msg('请选择用户');
                }
                //获取所选id
                ids = [];
                for (var i in user_list){
                    ids.push(user_list[i]['id']);
                }
                layer.open({
                    type: 2
                    ,title: '发放优惠券'
                    ,content: '{:url("coupon/sendCouponList")}'
                    ,area: ['90%','90%']
                    ,btn: ['确定发放', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'send-submit'
                            ,submit = layero.find('iframe').contents().find('#'+ submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                            var coupon_list = window["layui-layer-iframe" + index].callbackdata();
                            if(coupon_list.length === 0){
                                return layer.msg('请选择优惠券');
                            }
                            coupon_ids = [];
                            for (var i in coupon_list){
                                coupon_ids.push(coupon_list[i]['id']);
                            }
                            like.ajax({
                                url:'{:url("coupon/sendCoupon")}',
                                data:{coupon_ids:coupon_ids,user_ids:ids},
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('goods_brand-lists'); //数据刷新
                                    }

                                }
                            });
                        });

                        submit.trigger('click');
                    }
                });
            }
            ,set_group:function() { //设置分组
                var check_status = table.checkStatus('user-lists')
                    ,user_list = check_status.data; //得到选中的数据


                //是否已选数据
                if( user_list.length === 0){
                    return layer.msg('请选择用户');
                }
                //获取所选id
                ids = [];
                for (var i in user_list){
                    ids.push(user_list[i]['id']);
                }

                layer.open({
                    type: 2
                    ,title: '设置分组'
                    ,content: '{:url("user/setGroup")}'
                    ,area: ['60%', '60%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submitID = 'user-submit-edit'
                            ,submit = layero.find('iframe').contents().find("#set_group-submit");
                        //监听提交
                        iframeWindow.layui.form.on('submit(set_group-submit)', function(data){
                            var group_id = data.field.group_id;
                            $.ajax({
                                url:'{:url("user/setGroup")}',
                                data:{group_id:group_id,user_ids:ids},
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('user-lists'); //数据刷新
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                });

            }
        }
        $('.layui-btn.layuiadmin-btn-user').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,600);
        });
    });



    layui.define(['table', 'form'], function(exports){
        var $ = layui.$
            ,table = layui.table
            ,form = layui.form;

        //管理员管理
        table.render({
            id:'user-lists'
            ,elem: '#user-lists'
            ,url: '{:url("user/lists")}' //模拟接口
            ,cols: [[
                {type: 'checkbox'}
                ,{field: 'id', width: 80, title: 'ID', sort: true, hide:true}
                ,{title: '用户信息',width: 320,align: 'center', toolbar: '#user-info'}
                ,{field: 'mobile_register_count', title: '当前手机号注册/人',width: 150,align: 'center'}
                ,{field: 'level_name', title: '用户等级',align:"center",width: 100}
                ,{field: 'user_growth', title: '成长值',align:"center",width: 100}
                ,{title: '钱包信息',width: 200,align: 'center', templet: '#wallet-info'}
                ,{field: 'total_order_amount',align:"center", title: '消费金额',width: 90}
                ,{field: 'team_total_money',align:"center", title: '团队业绩',width: 90}
                ,{field: 'team_month_money',align:"center", title: '团队月业绩',width: 120}
                ,{title: '上级推荐人',align: 'center',width: 380,toolbar: '#first-info'}
                ,{field: 'fans', title: '下级人数',align:"center",width: 110}
                ,{field: 'create_time',align:"center", title: '注册时间',width: 180}
                ,{field: 'group_name', title: '用户分组',align:"center",width: 100}
                ,{fixed: 'right', title: '操作', width: 300,toolbar: '#user-operation'}

            ]]
            ,page:true
            ,text: {none: '暂无数据！'}
            ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
                return {
                    "code":res.code,
                    "msg":res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            }
            , done: function() {
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });

        var moreShow = 0;
        //监听工具条
        table.on('tool(user-lists)', function(obj){
            if(obj.event === 'edit'){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '编辑会员'
                    ,content: '{:url("user/edit")}?id='+id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submit = layero.find('iframe').contents().find('#edit-submit');
                        //监听提交
                        iframeWindow.layui.form.on('submit(edit-submit)', function(data){
                            var field = data.field;
                            $.ajax({
                                url:'{:url("user/edit")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('user-lists'); //数据刷新
                                    }else{
                                        layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 2
                                            , time: 1000
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })

            }
            if(obj.event === 'info'){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '会员资料'
                    ,content: '{:url("user/info")}?id='+id
                    ,area: ['90%','90%']
                    ,btn: ['返回']
                })
            }
            if(obj.event === 'adjust_user'){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '账户调整'
                    ,content: '{:url("user/adjustAccount")}?id='+id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submit = layero.find('iframe').contents().find('#adjust_user-submit');
                        //监听提交
                        iframeWindow.layui.form.on('submit(adjust_user-submit)', function(data){
                            var field = data.field;
                            $.ajax({
                                url:'{:url("user/adjustAccount")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('user-lists'); //数据刷新
                                    }else{
                                        layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 2
                                            , time: 1000
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            }
            if(obj.event === 'adjust_level'){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '等级调整'
                    ,content: '{:url("user/adjustLevel")}?id='+id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submit = layero.find('iframe').contents().find('#adjust_level-submit');
                        //监听提交
                        iframeWindow.layui.form.on('submit(adjust_level-submit)', function(data){
                            var field = data.field;
                            $.ajax({
                                url:'{:url("user/adjustLevel")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('user-lists'); //数据刷新
                                    }else{
                                        layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 2
                                            , time: 1000
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            }
            if(obj.event === 'adjust_first_leader'){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '推荐人调整'
                    ,content: '{:url("user/adjustFirstLeader")}?id='+ id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submit = layero.find('iframe').contents().find('#formSubmit');
                        //监听提交
                        iframeWindow.layui.form.on('submit(formSubmit)', function(data){
                            var field = data.field;
                            $.ajax({
                                url:'{:url("user/adjustFirstLeader")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('user-lists'); //数据刷新
                                    }else{
                                        layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 2
                                            , time: 1000
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            }
            if(obj.event === 'adjust_lower_level'){
                var id = obj.data.id;
                layer.open({
                    type: 2
                    ,title: '查看下级'
                    ,content: '{:url("user/lowlevel")}?id='+id
                    ,area: ['90%','90%']
                    ,btn: ['返回']
                })
            }
            if(obj.event === 'delUser'){
                var id = obj.data.id;
                layer.confirm('确认删除该用户吗？', function(index){
                    $.ajax({
                        url:'{:url("user/delUser")}',
                        data:{id:id},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1)
                            {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                   , icon: 1
                                   , time: 1000
                                });
                                table.reload('user-lists'); //数据刷新
                            }
                        }
                    })
                })
            }
        });
    });
</script>