{layout name="layout2" /}
<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <title>阶梯树状图</title>

  <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
  <style>
    html, body {
      height: 100%;
      background: #f6f8fa !important; /* 设置页面背景为浅灰色 */
    }
    body {
      min-height: 100vh;
      background: #f6f8fa !important; /* 防止被覆盖 */
    }
    #treeChart { width: 100%; height: 600px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.03);}
    .search-bar {
      margin: 20px 0;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 10px;
    }
    #searchInput {
      padding: 8px 12px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 16px;
      outline: none;
      transition: border-color 0.2s;
    }
    #searchInput:focus {
      border-color: #409eff;
    }
    .search-btn {
      padding: 8px 20px;
      background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
      color: #fff;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: background 0.2s;
      box-shadow: 0 2px 8px rgba(64,158,255,0.08);
    }
    .search-btn:hover {
      background: linear-gradient(90deg, #66b1ff 0%, #409eff 100%);
    }
  </style>
</head>
<body>
  <div class="search-bar">
    <input type="text" id="searchInput" placeholder="请输入关键词" />
    <button class="search-btn" id="searchBtn">搜索</button>
  </div>
  <div id="treeChart"></div>
  <script>
    layui.config({
      base: '/static/plug/layui-admin/dist/layuiadmin/',
      version: true
    }).extend({
      index: 'lib/index'
    }).use(['index', 'layer', 'util'], function(){
      var $ = layui.$, layer = layui.layer;

      // 递归处理children为对象的情况，转为数组
      function fixChildren(node) {
        if (!node) return node;
        // 如果children是对象，转为数组
        if (node.children && !Array.isArray(node.children)) {
          // 兼容children为{}或null
          if (typeof node.children === 'object' && node.children !== null) {
            node.children = [fixChildren(node.children)];
          } else {
            node.children = [];
          }
        } else if (Array.isArray(node.children)) {
          node.children = node.children.map(fixChildren);
        }
        return node;
      }

      // 默认数据
      var data = {
        nickname: "会员名",
        
      };

      var chart = echarts.init(document.getElementById('treeChart'));
      function renderTree(treeData) {
        // 递归将nickname转为name，兼容ECharts
        function convert(node) {
          var newNode = {
            name: node.nickname || node.name || '',
            children: []
          };
          if (node.children && node.children.length) {
            newNode.children = node.children.map(convert);
          }
          return newNode;
        }
        var option = {
          tooltip: { trigger: 'item', triggerOn: 'mousemove' },
          series: [
            {
              type: 'tree',
              data: [convert(treeData)],
              top: '8%',
              left: '10%',
              bottom: '8%',
              right: '10%',
              symbolSize: 16,
              orient: 'vertical',
              edgeShape: 'polyline',
              edgeForkPosition: '63%',
              initialTreeDepth: 1, // 默认只展开根节点
              label: {
                position: 'top',
                verticalAlign: 'middle',
                align: 'center',
                fontSize: 16
              },
              leaves: {
                label: {
                  position: 'bottom',
                  verticalAlign: 'middle',
                  align: 'center'
                }
              },
              expandAndCollapse: true,
              animationDuration: 550,
              animationDurationUpdate: 750
            }
          ]
        };
        chart.setOption(option);
      }

      renderTree(data);

      // 获取token（如有不同存储方式请自行调整）
      function getToken() {
        return localStorage.getItem('token') || '';
      }

      // 搜索并调用API
      $('#searchBtn').on('click', function() {
        var keyword = $('#searchInput').val().trim();
        // 清空树状图内容，防止残留
        chart.clear();
        if (!keyword) {
          renderTree(data);
          return;
        }
        $.ajax({
          url: "{:url('user/tree')}",
          type: 'get',
          data: { id: keyword },
          headers: {
            'Authorization': 'Bearer ' + getToken()
          },
          success: function(res) {
            // 兼容children为对象的情况
            //{"code":0,"msg":"用户不存在","data":[],"show":1,"time":"0.052037","wait":3,"url":""}
  
            var fixed = fixChildren(res);
            renderTree(fixed);
          },
          error: function() {
            layer.msg('搜索失败', {icon: 2});
          }
        });
      });
    });
  </script>
</body>
</html>