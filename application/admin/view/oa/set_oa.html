{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 120px;
    }
    .img-content{
        height:80px;
        line-height:80px
    }
    .img-container {
        float: left;
        opacity: 1;
        position: relative;
    }

    .img-src {
        width: 80px;
        height: 80px;
        padding: 4px;
    }
    .img-del-x {
        position: absolute;
        z-index: 100;
        top: -4px;
        right: -2px;
        width: 20px;
        height: 20px;
        font-size: 16px;
        line-height: 16px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        background: hsla(0, 0%, 60%, .6);
        border-radius: 10px;
    }
    .copy{
        margin-left: 10px;
    }
    .tips{
        color: red;
    }
    .layui-form-item .layui-input-inline{
        width: 410px;
    }
    .layui-input{
        display: inline-block;
        width: 80%;
    }
</style>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
        <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
            <div class="layui-colla-item">
                <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                <div class="layui-colla-content layui-show">
                    *填写微信公众号开发配置，请前往微信公众平台申请服务号并完成认证。
                </div>
            </div>
        </div>
        </div>
        <div class="layui-card-header" style="margin-top: 20px">公众号配置</div>
        <div class="layui-card-body" pad15>
            <div class="layui-form" lay-filter="">
<!--                微信公众号-->
                <div class="layui-form-item div-flex">
                    <fieldset class="layui-elem-field layui-field-title">
                        <legend>微信公众号</legend>
                    </fieldset>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">公众号名称：</label>
                    <div class="layui-input-inline">
                         <input type="text" name="name" value="{$oa.name}" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">原始ID：</label>
                    <div class="layui-input-inline">
                         <input type="text" name="original_id" value="{$oa.original_id}" autocomplete="off" class="layui-input">
                    </div>

                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">公众号二维码：</label>
                    <div class="layui-input-block">
                        <div class="img-content">
                            <input name="qr_code" type="hidden" value="{$oa.qr_code}">
                            <div class="img-add" {if $oa.qr_code } style="display: none" {/if}></div>
                        {if !empty($oa.qr_code)}
                        <div class="img-container">
                            <img class="img-src" src="{$oa.abs_qr_code}">
                            <a class="img-del-x">x</a>
                        </div>
                        {/if}
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <span style="color: #a3a3a3;font-size: 9px">建议尺寸：宽400px*高400px。jpg，jpeg，png格式</span>
                </div>
<!--                公众号开发者信息-->
                <div class="layui-form-item div-flex">
                    <fieldset class="layui-elem-field layui-field-title">
                        <legend>公众号开发者信息</legend>
                    </fieldset>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="tips">*</span>AppID：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="app_id" value="{$oa.app_id}"  lay-verify="required" lay-verType="tips" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="tips">*</span>AppSecret：</label>
                    <div class="layui-input-inline">
                        <input type="text"  name="app_secret" value="{$oa.app_secret}"  lay-verify="required"  lay-verType="tips" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <span style="color: #a3a3a3;font-size: 9px">登录微信公众平台，点击开发>基本配置>公众号开发信息，设置AppID和AppSecret</span>
                </div>
<!--                服务器配置-->
                <div class="layui-form-item div-flex">
                    <fieldset class="layui-elem-field layui-field-title">
                        <legend>服务器配置</legend>
                    </fieldset>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">URL：</label>
                    <div class="layui-input-inline">
                        <input  type="text" value="{$oa.url}" readonly="readonly" class="layui-input">
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm copy" >复制</button>
                    </div>

                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <span style="color: #a3a3a3;font-size: 9px">登录微信公众平台，点击开发>基本配置>服务器配置，填写服务器地址（URL）</span>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">Token：</label>
                    <div class="layui-input-inline">
                        <input type="text"  name="token" value="{$oa.token}" autocomplete="off" class="layui-input">
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm copy " >复制</button>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <span style="color: #a3a3a3;font-size: 9px">登录微信公众平台，点击开发>基本配置>服务器配置，设置令牌Token。不填默认为“LikeShop”</span>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">EncodingAESKey：</label>
                    <div class="layui-input-inline">
                        <input type="text"  name="encoding_ses_key" value="{$oa.encoding_ses_key}" autocomplete="off" class="layui-input">
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm copy" >复制</button>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <span style="color: #a3a3a3;font-size: 9px">消息加密密钥由43位字符组成，字符范围为A-Z,a-z,0-9</span>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">消息加密方式：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="encryption_type" value="1" title="明文模式(不使用消息体加解密功能，安全系数较低)"{if $oa.encryption_type == 1} checked {/if}   >
                    </div>
                    <div class="layui-input-block">
                        <input type="radio" name="encryption_type"  value="2" title="兼容模式(明文、密文将共存，方便开发者调试和维护)" {if $oa.encryption_type == 2} checked {/if} >
                    </div>
                    <div class="layui-input-block" style="margin-left: 150px">
                        <input type="radio" name="encryption_type"  value="3" title="安全模式（推荐）(消息包为纯密文，需要开发者加密和解密，安全系数高)" {if $oa.encryption_type == 3} checked {/if}  >
                    </div>
                </div>
<!--                功能设置-->
                <div class="layui-form-item div-flex">
                    <fieldset class="layui-elem-field layui-field-title">
                        <legend>功能设置</legend>
                    </fieldset>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">业务域名：</label>
                    <div class="layui-input-inline">
                        <input type="text" readonly="readonly" value="{$oa.business_domain}"  class="layui-input">
                        <button type="button"  class="layui-btn layui-btn-primary layui-btn-sm copy" >复制</button>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <span style="color: #a3a3a3;font-size: 9px">登录微信公众平台，点击设置>公众号设置>功能设置，填写业务域名</span>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">JS接口安全域名：</label>
                    <div class="layui-input-inline">
                        <input type="text" readonly="readonly" value="{$oa.safety_domain}"  class="layui-input">
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm copy" >复制</button>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <span style="color: #a3a3a3;font-size: 9px">登录微信公众平台，点击设置>公众号设置>功能设置，填写JS接口安全域名</span>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">网页授权域名：</label>
                    <div class="layui-input-inline">
                        <input type="text" readonly="readonly"  value="{$oa.auth_domain}"  class="layui-input">
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm copy" >复制</button>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <span style="color: #a3a3a3;font-size: 9px">登录微信公众平台，点击设置>公众号设置>功能设置，填写网页授权域名</span>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-inline">
                        <button class="layui-btn {$view_theme_color}" lay-submit lay-filter="setoa">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>


    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'table', 'like'], function () {
        var $ = layui.$
            , form = layui.form
            , like = layui.like;
        //上传图片
        like.imageUpload('.img-add', function (uris, element) {
            if(uris.length>1){
                layer.msg('最多最能选中1张图片');
                return;
            }
            var html = '<div class="img-container">\n' +
                '<img class="img-src" ' +
                'src="' + uris[0] + '">' +
                '<a class="img-del-x">x</a>' +
                '</div>';
            element.prev().val(like.getUrlFileName(uris[0], '{$storageUrl}'));
            element.parent().append(html);
            element.css('display','none');
        }, true);
        //删除图片
        $(document).on('click', '.img-del-x', function () {
            $(this).parent().siblings('input').val('');
            $(this).parent().prev().css('display','block');
            $(this).parent().remove();
        });
        //显示图片
        $(document).on('click', '.img-src', function () {
            var image = $(this).attr('src');
            like.showImg(image,600);
        });
        //复制
        $(document).on('click','.copy',function () {
            var copyText = $(this).prev()
            copyText.select()
            document.execCommand("Copy");


        })
        form.on('submit(setoa)', function (data) {
            layui.like.ajax({
                url: '{:url("oa/setOa")}' //实际使用请改成服务端真实接口
                , data: data.field
                , type: 'post'
                , success: function (res) {
                    if (res.code == 1) {
                        layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        });
                    }

                }
            });
        });
    });

</script>