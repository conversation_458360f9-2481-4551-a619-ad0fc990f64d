{layout name="layout2" /}
<style>
    .div-flex {
        display: flex;
        align-items: center;
        justify-content: left;
    }
    .width-160 {
        width: 200px;
    }
    .layui-table th {
        text-align: center;
    }
    .table-margin{
        margin-left: 50px;
        margin-right: 50px;
        text-align: center;
    }
    .image{
        height:80px;
        width: 80px;
    }

    .mt50{
        margin-left: 50px;
    }

</style>

<div class="layui-card-body" >
    <!--基本信息-->
    <div class="layui-form" lay-filter="layuiadmin-form-after-sale" id="layuiadmin-form-after-sale" >
        <input type="hidden" class="after_sale_id" name="after_sale_id" value="{$detail.id}">

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>售后信息</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">售后单号:</label>
            <div class="width-160">{$detail.sn}</div>
            <label class="layui-form-label ">申请时间:</label>
            <div class="width-160">{$detail.create_time}</div>
            <label class="layui-form-label ">退款方式:</label>
            <div class="width-160 refund_type_text">{$detail.refund_type_text}</div>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">退款金额:</label>
            <div class="width-160 refund_price_text">￥{$detail.refund_price}</div>
            <label class="layui-form-label ">退款原因:</label>
            <div class="width-160 ">{$detail.refund_reason}</div>
            <label class="layui-form-label ">退款说明:</label>
            <div class="width-160">{$detail.refund_remark}</div>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">申请状态:</label>
            <div class="width-160">{$detail.status_text}</div>
            <label class="layui-form-label ">售后说明:</label>
            <div class="width-160">{$detail.admin_remark}</div>
        </div>

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>订单信息</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">订单编号:</label>
            <div class="width-160">{$detail.order.order_sn}</div>
            <label class="layui-form-label ">订单金额:</label>
            <div class="width-160">￥{$detail.order.order_amount}</div>
            <label class="layui-form-label ">付款方式:</label>
            <div class="width-160 pay_way_text">{$detail.order.pay_way}</div>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">配送方式:</label>
            <div class="width-160">{$detail.order.delivery_type_text}</div>
            <label class="layui-form-label ">订单状态:</label>
            <div class="width-160">{$detail.order.order_status}</div>
        </div>

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>会员信息</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">会员编号:</label>
            <div class="width-160">{$detail.user.sn}</div>
            <label class="layui-form-label ">会员昵称:</label>
            <div class="width-160">{$detail.user.nickname}</div>
            <label class="layui-form-label ">手机号码:</label>
            <div class="width-160">{$detail.user.mobile}</div>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">性别:</label>
            <div class="width-160">{$detail.user.sex}</div>
            <label class="layui-form-label ">注册时间:</label>
            <div class="width-160">{$detail.user.create_time}</div>
        </div>

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>退款商品</legend>
            </fieldset>
        </div>

        <div class="layui-form-item table-margin">
            <table class="layui-table">
                <colgroup>
                    <col width="250">
                    <col width="100">
                    <col width="100">
                    <col width="100">
                </colgroup>
                <thead>
                <tr>
                    <th>商品信息</th>
                    <th>价格(元)</th>
                    <th>数量</th>
                    <th>小计(元)</th>
                </tr>
                </thead>
                <tbody>
                {foreach $detail.order_goods as $k => $goods}
                <tr>
                    <td>
                        <div style="text-align: left">
                            <div class="layui-col-md3">
                                <img src="{$goods.image}" class="image-show image" >
                            </div>
                            <div class="layui-col-md9">
                                <p style="margin-top: 10px">{$goods.goods_name}</p>
                                <br>
                                <p>{$goods.spec_value}</p>
                            </div>
                        </div>
                    </td>
                    <td>￥{$goods.goods_price}</td>
                    <td>{$goods.goods_num}</td>
                    <td>{$goods.total_price}</td>
                </tr>
                {/foreach}
                </tbody>
            </table>
        </div>

        {if condition = "$detail.status egt 3 && $detail.invoice_no neq null && $detail.refund_type eq 1"}
        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>收货信息</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">快递公司:</label>
            <div class="width-160">{$detail.express_name}</div>
            <label class="layui-form-label ">快递单号:</label>
            <div class="width-160">{$detail.invoice_no}</div>
            <label class="layui-form-label ">快递说明:</label>
            <div class="width-160">{$detail.express_remark}</div>
        </div>

        <div class="layui-form-item div-flex">
            <label class="layui-form-label ">退货地址:</label>
            <div class="width-160 refund_address_text">{$detail.shop_address}</div>
        </div>
        {/if}

        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>售后操作</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex ">
            <div class="layui-input-block ">

                {eq name="$detail.status" value="0"}
                <button type="submit" class="layui-btn layui-btn-sm layui-btn-normal width_160 " id="agree">商家同意</button>
                <button type="submit" class="layui-btn layui-btn-sm layui-btn-danger width_160 " id="refuse">商家拒绝</button>
                {/eq}


                {eq name="$detail.status" value="3"}
                <button type="submit" class="layui-btn layui-btn-sm layui-btn-normal width_160 " id="take_goods">商家收货</button>
                <button type="submit" class="layui-btn layui-btn-sm layui-btn-danger width_160 " id="refuse_goods">拒绝收货</button>
                {/eq}

                {eq name="$detail.status" value="5"}
                <button type="submit" class="layui-btn layui-btn-sm layui-btn-normal width_160 " id="confirm">确认退款</button>
                {/eq}

            </div>
        </div>
        
        <div class="layui-form-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>售后日志</legend>
            </fieldset>
        </div>

        <div class="layui-form-item div-flex">
            <div class="layui-input-block ">
            <ul class="layui-timeline">
                {volist name="$detail.logs" id="vo"}
                <li class="layui-timeline-item">
                    <i class="layui-icon layui-timeline-axis"></i>
                    <div class="layui-timeline-content layui-text">
                        <h3 class="layui-timeline-title">{$vo.content}({$vo.create_time})</h3>
                        <!--日志备注-->
                        {notempty name="$vo.log_remark"}
                        <h4>{$vo.log_remark}</h4>
                        {/notempty}
                        <!--日志图片-->
                        {notempty name="$vo.log_img"}
                            <img src="{$vo.log_img}" alt="" width="80" height="80" class="image-show">
                        {/notempty}
                    </div>
                </li>
                {/volist}
            </ul>
            </div>
        </div>

    </div>
</div>


<script type="text/javascript">
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作

    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'element', 'jquery', 'like'], function () {
        var $ = layui.$;
        var like = layui.like;

        //主图放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,400);
        });

        var id = $('.after_sale_id').val();
        var pay_way = $('.pay_way_text').text();
        var refund_type = $('.refund_type_text').text();
        var refund_price = $('.refund_price_text').text();
        var refund_address = $('.refund_address_text').text();

        //商家同意
        $('#agree').click(function () {
            layer.confirm(
                "提示：该笔订单通过" + pay_way + "付款，商家同意后，退款将自动原路退回会员账户。"+"<br/>"+
                "退款方式："+ refund_type + "<br/>"+
                "退款金额："+ "<span style='color: red'>"+refund_price+"</span>", {
                btn: ['确认','取消'] //按钮
            }, function(){
                like.ajax({
                    url: '{:url("after_sale/agree")}'
                    , data: {'id': id}
                    , type: 'post'
                    , success: function (res) {
                        if (res.code == 1) {
                            layui.layer.msg(res.msg, {
                                offset: '15px'
                                , icon: 1
                                , time: 1000
                            },function () {
                                location.reload();
                            });
                        }
                    }
                });
            });
        });

        //商家拒绝
        $('#refuse').click(function () {
            layer.confirm(
                "提示：请与会员协商后确认拒绝申请，会员可再次发起退款。"+"<br/>"+
                "退款方式："+ refund_type + "<br/>"+
                "退款金额："+ "<span style='color: red'>"+refund_price+"</span>"+ "<br/>"+
                "拒绝原因："+"<textarea id='remark' name='remark' class='layui-textarea' type='text' style='height:100px'></textarea>", {
                    btn: ['确认','取消'] //按钮
                }, function(){
                    var remark = $('#remark').val();
                    if (remark == null || remark == undefined || remark == ''){
                        layer.msg('请填写拒绝原因!');
                        return false;
                    }
                    like.ajax({
                        url: '{:url("after_sale/refuse")}'
                        , data: {'id': id,'remark':remark}
                        , type: 'post'
                        , success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                },function () {
                                    location.reload();
                                });
                            }
                        }
                    });
                    return false;
                });
        });


        //商家收货
        $('#take_goods').click(function () {
            layer.confirm(
                "提示：该笔订单通过" + pay_way + "付款，商家同意后，退款将自动原路退回会员账户。"+"<br/>"+
                "退款方式："+ refund_type + "<br/>"+
                "退款金额："+ "<span style='color: red'>"+refund_price+"</span>"+ "<br/>"+
                "退货地址："+ refund_address, {
                    btn: ['确认','取消'] //按钮
                }, function(){
                    like.ajax({
                        url: '{:url("after_sale/take")}'
                        , data: {'id': id}
                        , type: 'post'
                        , success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                },function () {
                                    location.reload();
                                });
                            }
                        }
                    });
                });
        });



        //商家拒绝收货
        $('#refuse_goods').click(function () {
            layer.confirm(
                "提示：请与会员协商后确认拒绝申请，会员可再次发起退款。"+"<br/>"+
                "退款方式："+ refund_type + "<br/>"+
                "退款金额："+ "<span style='color: red'>"+refund_price+"</span>"+ "<br/>"+
                "拒绝原因："+"<textarea id='remark' name='remark' class='layui-textarea' type='text' style='height:100px'></textarea>", {
                    btn: ['确认','取消'] //按钮
                }, function(){
                    var remark = $('#remark').val();
                    if (remark == null || remark == undefined || remark == ''){
                        layer.msg('请填写拒绝原因!');
                        return false;
                    }
                    like.ajax({
                        url: '{:url("after_sale/refuseGoods")}'
                        , data: {'id': id,'remark':remark}
                        , type: 'post'
                        , success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                },function () {
                                    location.reload();
                                });
                            }
                        }
                    });
                    return false;
                });
        });



        //确认退款
        $('#confirm').click(function () {
            layer.confirm(
                "提示：该笔订单通过" + pay_way + "付款，商家确认退款后，退款将自动原路退回会员账户。"+"<br/>"+
                "退款方式："+ refund_type + "<br/>"+
                "退款金额："+ "<span style='color: red'>"+refund_price+"</span>",  {
                    btn: ['确认','取消'] //按钮
                }, function(){
                    like.ajax({
                        url: '{:url("after_sale/confirm")}'
                        , data: {'id': id}
                        , type: 'post'
                        , success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                },function () {
                                    location.reload();
                                });
                            }
                        }
                    });
                });
        });

    });
</script>