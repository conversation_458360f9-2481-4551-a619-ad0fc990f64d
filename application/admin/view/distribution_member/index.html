{layout name="layout1" /}
<style>
    .search {
        margin-top: 15px;
    }
    .search .layui-form-label {
        width: 100px;
        text-align: right;
    }
    .btns {
        margin-top: 15px;
    }
    .layui-table-cell {
        height:auto;
    }
    .user-info {
        width: 60px !important;
    }
</style>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <!--操作提示-->
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*分销会员；</p>
                    </div>
                </div>
            </div>
            <!--搜索区域-->
            <div class="search layui-form">
                <div class="layui-inline">
                    <div class="layui-form-label user-info">用户信息</div>
                    <div class="layui-input-inline">
                        <input type="text" id="keyword" name="keyword" class="layui-input" />
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-form-label">分销会员等级</div>
                    <div class="layui-input-inline">
                        <select name="level_id" id="level_id"  placeholder="请选择" >
                            <option value="all">全部</option>
                            {foreach $levels as $val }
                            <option value="{$val.id}">{$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-form-label">分销状态</div>
                    <div class="layui-input-inline">
                        <select name="is_freeze" id="is_freeze"  placeholder="请选择" >
                            <option value="all">全部</option>
                            <option value="0">正常</option>
                            <option value="1">冻结</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-primary layui-bg-blue" lay-submit lay-filter="search">搜索</button>
                    <button class="layui-btn layui-btn-primary" lay-submit lay-filter="reset">重置</button>
                </div>
            </div>
            <!--功能按钮-->
            <div class="btns">
                <buttion class="layui-btn layui-btn-sm layui-bg-blue" id="open" style="margin-bottom: 10px;">开通分销会员</buttion>
            </div>
            <!--数据表格-->
            <table id="lists" lay-filter="lists"></table>
            <!--工具条模板-->
            <script type="text/html" id="operate">
                <a class="layui-btn layui-btn-sm layui-bg-blue" lay-event="adjust">等级调整</a>
                {{#  if(d.is_freeze == 0){ }}
                <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="freeze">冻结资格</a>
                {{#  } else { }}
                <a class="layui-btn layui-bg-blue layui-btn-sm" lay-event="unfreeze">恢复资格</a>
                {{#  } }}
            </script>
            <!--自定义模板-->
            <script type="text/html" id="user-info">
                <img src="{{d.avatar}}" style="height:60px;width: 60px" class="image-show">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>用户编号:{{d.user_sn}}</p>
                    <p>用户昵称:{{d.nickname}}</p>
                </div>
            </script>
            <script type="text/html" id="level-info">
                {{d.level_name}}({{d.weights}}级)
            </script>
            <script typpe="text/html" id="yx_count">
                {{ d.yx_count }}
            </script>
            <script type="text/html" id="earnings-wait">
                {{d.earnings.wait}}
            </script>
            <script type="text/html" id="earnings-success">
                {{d.earnings.success}}
            </script>
            <script type="text/html" id="earnings-fail">
                {{d.earnings.fail}}
            </script>
            <script type="text/html" id="user-distribution">
                {{#  if(d.is_freeze){ }}
                冻结
                {{#  } else { }}
                正常
                {{#  } }}
            </script>
        </div>
    </div>
</div>


<script>

    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/modules/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['table', 'form', 'like'], function () {
        let $ = layui.$
            , form = layui.form
            , like = layui.like
            , table = layui.table;

        //监听搜索
        form.on('submit(search)', function(data){
            var field = data.field;
            //执行重载
            table.reload('lists', {
                where: field,
                page: {curr: 1}
            });
        });

        //清空查询
        form.on('submit(reset)', function(){
            $('#keyword').val('');
            $('#level_id').val('all');
            $('#is_freeze').val('all');
            form.render('select');
            //刷新列表
            table.reload('lists', {
                where: [], page: {curr: 1}
            });
        });

        // 数据表格渲染
        table.render({
            elem: '#lists'
            ,url: '{:url("distribution_member/index")}' //数据接口
            ,method: 'post'
            ,page: true //开启分页
            ,cols: [[ //表头
                {templet: '#user-info', title: '用户信息', width:380}
                ,{templet: '#level-info', title: '分销等级', width:180}
                ,{templet: '#yx_count', title: '有效会员', width:180}
                ,{templet: '#earnings-success', title: '已入账佣金', width:120}
                ,{templet: '#earnings-wait', title: '待结算佣金', width:120}
                ,{templet: '#earnings-fail', title: '已失效佣金', width:120}
                ,{templet: '#user-distribution', title: '分销状态', width: 120}
                ,{field: 'distribution_time', title: '成为分销会员时间', width: 200}
                ,{title: '操作', width: 200, align: 'center', toolbar: '#operate'}
            ]]
            , text: {none: '暂无数据！'}
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            },
            response: {
                statusCode: 1
            }
            ,done: function(res, curr, count){
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });

        // 工具条事件
        table.on('tool(lists)', function(obj){
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）

            if(layEvent === 'freeze'){ // 冻结资格
                layer.confirm('确定要冻结资格吗?', function(index){
                    layer.close(index);
                    like.ajax({
                        url: "{:url('distribution_member/isFreeze')}",
                        data: {user_id: obj.data.user_id,is_freeze:1},
                        type: "post",
                        success:function(res) {
                            if(res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                table.reload("lists");
                            }
                        }
                    });
                });
            } else if(layEvent === 'unfreeze'){ // 恢复资格
                layer.confirm('确定要恢复资格吗?', function(index){
                    layer.close(index);
                    like.ajax({
                        url: "{:url('distribution_member/isFreeze')}",
                        data: {user_id: obj.data.user_id,is_freeze:0},
                        type: "post",
                        success:function(res) {
                            if(res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                table.reload("lists");
                            }
                        }
                    });
                });
            } else if(layEvent === 'adjust'){ // 分销等级调整
                id = obj.data.user_id;
                // 分销等级调整
                layer.open({
                    type: 2
                    ,title: "分销等级调整"
                    ,content: "{:url('distribution_member/adjust')}?id=" + id
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#formSubmit");
                        iframeWindow.layui.form.on("submit(formSubmit)", function(data){
                            like.ajax({
                                url: "{:url('distribution_member/adjust')}",
                                data: data.field,
                                type: "post",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("lists");
                                    }
                                }
                            });
                            return false;
                        });
                        submit.trigger("click");
                    }
                });
            }
        });

        // 功能按钮
        $('#open').click(function() { // 开通分销会员
            layer.open({
                type: 2
                ,title: "开通分销会员"
                ,content: "{:url('distribution_member/open')}"
                ,area: ["90%", "90%"]
                ,btn: ["确定", "取消"]
                ,yes: function(index, layero){
                    var iframeWindow = window["layui-layer-iframe" + index];
                    var submit = layero.find("iframe").contents().find("#openSubmit");
                    iframeWindow.layui.form.on("submit(openSubmit)", function(data){
                        like.ajax({
                            url: "{:url('distribution_member/open')}",
                            data: data.field,
                            type: "post",
                            success:function(res) {
                                if(res.code === 1) {
                                    layui.layer.msg(res.msg);
                                    layer.close(index);
                                    table.reload("lists");
                                }
                            }
                        });
                        return false;
                    });
                    submit.trigger("click");
                }
            });
        });

    });


</script>