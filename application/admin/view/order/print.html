{layout name="layout2" /}
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>山东新九汇生物科技有限公司销售单</title>
    <link rel="stylesheet" href="/static/plug/layui-admin/dist/layuiadmin/layui/css/layui.css" />
    <style>
      body {
        font-family: "SimSun", "宋体", Arial, sans-serif;
        background: #fff;
        margin: 0;
        padding: 0;
      }
      .left { text-align: left; }
      .right { text-align: right; }
      .center { text-align: center; }
      .invoice-container {
        width: 210mm;
        min-height: 99mm;
        margin: 0 auto;
        background: #fff;
        padding: 0 6mm 6mm 6mm;
        box-sizing: border-box;
      }
      .invoice-title {
        text-align: center;
        font-size: 22px;
        font-weight: bold;
        margin: 10px 0 6px 0;
        letter-spacing: 2px;
      }
      .invoice-meta {
        width: 100%;
        font-size: 15px;
        margin-bottom: 4px;
        border-collapse: collapse;
      }
      .invoice-meta td { padding: 2px 6px; }
      .invoice-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
        margin-bottom: 0;
        table-layout: fixed;
      }
      .invoice-table th,
      .invoice-table td {
        border: 1px solid #333;
        padding: 4px 2px;
        text-align: center;
        height: 22px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .invoice-table th {
        font-weight: normal;
        background: #fafafa;
        font-size: 14px;
      }
      .invoice-table .left { text-align: left; padding-left: 6px; }
      .footer-table {
        width: 100%;
        font-size: 13px;
        margin-top: 6px;
        border-collapse: collapse;
      }
      .footer-table td { padding: 2px 6px; border: none; }
      .footer-table .white-link,
      .footer-table .red-link { font-size: 12px; }
      @media print {
        body { background: none; }
        .invoice-container {
          box-shadow: none;
          border: none;
          margin: 0;
          padding: 0;
          width: 210mm;
          min-height: 99mm;
        }
        .print-btn { display: none !important; }
        @page {
          size: 210mm 99mm;
          margin: 0;
        }
      }
    </style>
    <script src="/static/plug/layui-admin/dist/layuiadmin/layui/layui.js"></script>
  </head>
  <body>
    <div class="invoice-container">
      <div class="invoice-title">山东新九汇生物科技有限公司销售单</div>
      <table class="invoice-meta" style="font-size:13px;">
        <tr>
          <td style="width: 20%; text-align: left;">
        客户：<span id="customer"></span>
          </td>
          <td style="width: 25%; text-align: left;">
        发货仓库：<span id="warehouse"></span>
          </td>
          <td style="width: 35%; text-align: left;">
        单据日期：<span id="date"></span>
          </td>
          <td style="width: 20%; text-align: left;">
        订单类型：<span id="order_type_text"></span>
          </td>
        </tr>
      </table>
      <table class="invoice-table">
        <tr>
          <th style="width: 13%;">存货全名</th>
          <th style="width: 20%;">订单号</th>
          <th style="width: 8%;">单位</th>
          <th style="width: 8%;">数量</th>
          <th style="width: 16%;">物流单号</th>
          <th style="width: 15%;">签字</th>
        </tr>
        <tbody id="items">
          <!-- 动态插入行 -->
        </tbody>
        <!-- 合计行 -->
        <!-- <tr>
          <td class="left">合计</td>
          <td></td>
          <td></td>
          <td id="total-qty"></td>
          <td></td>
          <td></td>
        </tr> -->
        <!-- 大写金额行 -->
        <!-- <tr>
          <td class="left">合计大写</td>
          <td colspan="5" class="left" id="total-cn"></td>
        </tr> -->
      </table>
      <table class="footer-table">
        <tr>
          <td></td>
          <td class="center" style="width: 50%">主管签字：</td>
          <td class="center" style="width: 25%">
            制单人：<span id="maker"></span>
          </td>
        </tr>
        <tr>
          <td>
            <span class="white-link">白联财务</span>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <span class="red-link">红联仓库</span>
          </td>
        </tr>
      </table>
    </div>
    <div style="text-align:center;margin:20px;">
      <button class="layui-btn layui-btn-primary print-btn" id="printBtn">打印单据</button>
    </div>
    <script>
      layui.config({
        base: '/static/plug/layui-admin/dist/layuiadmin/',
        version: true
      }).extend({
        index: 'lib/index'
      }).use(['index', 'layer'], function(){
        var $ = layui.$, layer = layui.layer;

        // 最小行数
        const minRows = 4;
        let data = {
          customer: "",
          warehouse: "",
          date: "",
          items: [],
          total: 0,
          totalCn: "零",
          order_sn: "",
          totalQty: "",
          maker: "",
          order_type_text: ""
        };

        // 获取get请求参数
        function getOrderId() {
          var reg = new RegExp("(^|&)" + "order_id" + "=([^&]*)(&|$)");
          var r = window.location.search.substr(1).match(reg);
          return r ? decodeURIComponent(r[2]) : '';
        }

        // 从后端获取数据
        function fetchData() {
          return $.ajax({
            url: "{:url('order/print')}",
            type: "post",
            data:{order_id: getOrderId()},
            success: function(res) {
              if(res){
                data.items = res.order_goods;
                data.customer = res.user.nickname || '';
                data.warehouse = '办公楼仓库';
                data.date = res.pay_time;
                data.maker = res.maker || '赵平';
                data.total = Number(res.total_amount) || 0;
                data.order_sn = res.order_sn || '';
                data.order_type_text = res.order_type_text || '';
                data.totalCn = "";
                data.totalQty = "";
              } else {
                layer.msg('未找到订单信息', {icon: 2});
                return;
              }
            },
            error: function() {
              layer.msg('获取数据失败', {icon: 2});
            }
          });
        }

        // 绘制表格
        function drawTable(dom) {
          dom.customer.text(data.customer);
          dom.warehouse.text(data.warehouse);
          dom.date.text(data.date);
          dom.maker.text(data.maker);
          dom.total.text(Number(data.total).toFixed(2));
          dom.totalCn.text(data.totalCn);
          dom.totalQty.text("");
          dom.order_type_text.text(data.order_type_text);
          dom.items.html("");
          // 填充表格内容
          (data.items || []).forEach(function(item) {
            var tr = $('<tr></tr>');
            tr.html(
              '<td class="left">' + (item.goods_name || '') + '</td>' +
              '<td>' + (data.order_sn || '') + '</td>' +
              '<td>' + (item.unit || '盒') + '</td>' +
              '<td>' + (item.num || '') + '</td>' +
              '<td class="left">' + (item.delivery && item.delivery.invoice_no ? item.delivery.invoice_no : '自提') + '</td>' +
              '<td></td>' // 签字列留空
            );
            dom.items.append(tr);
          });
          drawBlankRow(dom, minRows - (data.items ? data.items.length : 0));
        }
        // 转换金额为大写
        function numberToChinese(n) {
                  var fraction = ['角', '分'];
                  var digit = [
                    '零', '壹', '贰', '叁', '肆',
                    '伍', '陆', '柒', '捌', '玖'
                  ];
                  var unit = [
                    ['元', '万', '亿'],
                    ['', '拾', '佰', '仟']
                  ];
                  var head = n < 0 ? '负' : '';
                  n = Math.abs(n);

                  var s = '';

                  for (var i = 0; i < fraction.length; i++) {
                    s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
                  }
                  s = s || '整';
                  n = Math.floor(n);

                  for (var i = 0; i < unit[0].length && n > 0; i++) {
                    var p = '';
                    for (var j = 0; j < unit[1].length && n > 0; j++) {
                      p = digit[n % 10] + unit[1][j] + p;
                      n = Math.floor(n / 10);
                    }
                    s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
                  }
                  return head + s.replace(/(零.)*零元/, '元')
                                 .replace(/(零.)+/g, '零')
                                 .replace(/^整$/, '零元整');
        }

        // 绘制空白行
        function drawBlankRow(dom, len) {
          for (var i = 0; i < len; i++) {
            var tr = $('<tr></tr>');
            tr.html('<td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td>');
            dom.items.append(tr);
          }
        }

        // 初始化
        $(function(){
          var dom = {
            customer: $("#customer"),
            warehouse: $("#warehouse"),
            date: $("#date"),
            maker: $("#maker"),
            total: $("#total"),
            totalCn: $("#total-cn"),
            totalQty: $("#total-qty"),
            items: $("#items"),
            order_type_text: $("#order_type_text")
          };
          // 先画空白
          drawBlankRow(dom, minRows);

          fetchData().then(function(){
            drawTable(dom);
          });

          // 打印按钮
          $('#printBtn').on('click', function(){
            $(this).hide();
            window.print();
            setTimeout(() => { $('#printBtn').show(); }, 500);
          });
        });
      });
    </script>
  </body>
</html>
