{layout name="layout1" /}
<style>
  .layui-tab-content {
    height: auto !important;
  }
  .layui-form-label {
    color: #6a6f6c;
  }
  .img-content{
    height:80px;
    line-height:80px
  }
  .img-container {
    float: left;
    opacity: 1;
    position: relative;
  }

  .img-src {
    width: 80px;
    height: 80px;
    padding: 4px;
  }
  .img-del-x {
    position: absolute;
    z-index: 100;
    top: -4px;
    right: -2px;
    width: 20px;
    height: 20px;
    font-size: 16px;
    line-height: 16px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    background: hsla(0, 0%, 60%, .6);
    border-radius: 10px;
  }
</style>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show tips">
                      *设置移动端商城首页导航区的菜单。支持选择系统模块、填写自定义链接
                    </div>
                </div>
            </div>
        </div>
          <!-- 选项卡 -->
          <div class="layui-tab">
            <ul class="layui-tab-title">
              <li class="layui-this">导航设置</li>
              <li>其它设置</li>
            </ul>
            <div class="layui-tab-content" style="height: 100px;">
              <div class="layui-tab-item layui-show">
                <!-- 导航设置 -->
                <div style="padding-bottom: 10px;">
                    <button class="layui-btn layui-btn-sm layuiadmin-btn-menu_decorate {$view_theme_color}" data-type="add">新增</button>
                </div>
                <table id="menu_decorate-lists" lay-filter="menu_decorate-lists"></table>
                <script type="text/html" id="image">
                    <img src="{{d.image}}" style="height:80px;width: 80px" class="image-show">
                </script>
                <script type="text/html" id="status">
                    <input type="checkbox"  lay-filter="switch-status" data-id={{d.id}} data-field='is_show'  lay-skin="switch"
                            lay-text="显示|隐藏" {{#  if(d.is_show){ }} checked  {{#  } }} />
                </script>
                <script type="text/html" id="menu_decorate-operation">
                    <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit"><i class="layui-icon"></i>编辑</a>
                    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del"><i class="layui-icon"></i>删除</a>
                </script>
              </div>
              <div class="layui-tab-item">
                <!-- 其它设置 -->
                <div class="layui-form" lay-filter="index-setting">
                  <div class="layui-form-item">
                    <label class="layui-form-label">商城logo</label>
                    <div class="layui-input-block">
                      <input type="radio" name="logo" value="1" title="开启" {if $index_setting_logo == 1}checked{/if} />
                      <input type="radio" name="logo" value="0" title="关闭" {if $index_setting_logo == 0}checked{/if}  />
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block">
                      <span class="layui-word-aux">开启或关闭商城logo在首页的显示，默认开启</span>
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">热销榜单</label>
                    <div class="layui-input-block">
                      <input type="radio" name="hots" value="1" title="开启" {if $index_setting_hots == 1}checked{/if} />
                      <input type="radio" name="hots" value="0" title="关闭" {if $index_setting_hots == 0}checked{/if}  />
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block">
                      <span class="layui-word-aux">开启或关闭热销榜单在首页的显示，默认开启</span>
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">新品推荐</label>
                    <div class="layui-input-block">
                      <input type="radio" name="news" value="1" title="开启" {if $index_setting_news == 1}checked{/if} />
                      <input type="radio" name="news" value="0" title="关闭" {if $index_setting_news == 0}checked{/if}  />
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block">
                      <span class="layui-word-aux">开启或关闭新品推荐在首页的显示，默认开启</span>
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label">顶部背景图</label>
                    <div class="layui-input-block">
                      <div class="img-content">
                        <input name="top_bg_image" type="hidden" value="{$index_setting_top_bg_image}">
                        <div class="img-add"  {if $index_setting_top_bg_image } style="display: none" {/if} ></div>
                        {if !empty($index_setting_top_bg_image)}
                        <div class="img-container">
                            <img style="width: 100px" class="img-src" src="{$index_setting_top_bg_image}">
                            <a class="img-del-x">x</a>
                        </div>
                        {/if}
                    </div>
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block">
                      <span class="layui-word-aux">页面顶部背景图，建议尺寸：宽400px*高400px。jpg，jpeg，png格式</span>
                    </div>
                  </div>
                  <div class="layui-form-item">
                    <label class="layui-form-label"></label>
                    <div class="layui-input-block">
                      <button class="layui-btn layui-btn-normal layui-btn-sm" lay-submit lay-filter="index-setting">确 定</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
    </div>
</div>
<style>
    .layui-table-cell {
        height: auto;
    }
</style>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like'], function(){
      var $ = layui.$
        ,form = layui.form
        ,table = layui.table
        ,like = layui.like;
      // 数据表格渲染
      table.render({
        elem: '#menu_decorate-lists'
        ,url: '{:url("menu_decorate/indexList")}'
        ,cols: [[
          {field: 'name', title: '菜单名称',width: 200,}
          ,{title: '菜单图标', width: 160,style:'height:100px;',templet: '#image'}
          ,{field: 'link_address', title: '链接地址',width: 180}
          ,{field: 'sort', title: '排序',width: 100, event: 'tips',edit:'text'}
          ,{field: 'is_show', title:'显示', templet: '#status', width: 100, align: 'center'}
          ,{fixed: 'right', title: '操作', align: 'center', toolbar: '#menu_decorate-operation'}
        ]]
        ,page:true
        ,text: {none: '暂无数据！'}
        ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
            return {
                "code":res.code,
                "msg":res.msg,
                "count": res.data.count, //解析数据长度
                "data": res.data.lists, //解析数据列表
            };
        }
        ,done: function(res, curr, count){
            // 解决操作栏因为内容过多换行问题
            $(".layui-table-main tr").each(function (index, val) {
                $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
            });
        }
    });

    //图片放大
    $(document).on('click', '.image-show', function () {
      var src = $(this).attr('src');
      like.showImg(src,600);
    });

    //监听显示开关
    form.on('switch(switch-status)',function (obj) {
        var ids = [];
        var id = obj.elem.attributes['data-id'].nodeValue
        var fields = obj.elem.attributes['data-field'].nodeValue
        var field_value = 0;
        ids.push(id);
        if(this.checked){
            field_value = 1;
        }
        changeFields(ids,fields,field_value)
    });

    //修改某个字段的值
    function changeFields(ids,fields,value) {
      if(fields === 'is_show' && ids.length == 0){
          layer.msg('请选择菜单');
          return false;
      }
      $.ajax({
        url:'{:url("menu_decorate/changeFields")}',
        data:{id:ids,field:fields,value:value},
        type:'post',
        dataType:'json',
        success:function (res) {
          if(res.code == 1) {
            layui.layer.msg(res.msg, {
                offset: '15px'
                , icon: 1
                , time: 1000
            });
          } else {
            layui.layer.msg(res.msg, {
              offset: '15px'
              , icon: 2
              , time: 1000
            }, function(){
              location.href = location.href;
            });
          }
        }
      })
    }

    //事件
    var active = {
      add: function(){
        layer.open({
          type: 2
          ,title: '新增'
          ,content: '{:url("menu_decorate/add")}?type=1'
          ,area: ['90%','90%']
          ,btn: ['确定', '取消']
          ,yes: function(index, layero){
            var iframeWindow = window['layui-layer-iframe'+ index]
              ,submitID = 'add-menu_decorate-submit'
              ,submit = layero.find('iframe').contents().find('#'+ submitID);
            //监听提交
            iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
              var field = data.field;
              console.log(field);
              return;
              field['decorate_type'] = 1,
              $.ajax({
                url:'{:url("menu_decorate/add")}',
                data:field,
                type:"post",
                success:function(res)
                {
                  if(res.code == 1)
                  {
                      layui.layer.msg(res.msg, {
                          offset: '15px'
                          , icon: 1
                          , time: 1000
                      });
                      layer.close(index); //关闭弹层
                      table.reload('menu_decorate-lists'); //数据刷新
                  }else{
                      iframeWindow.layer.msg(res.msg, {
                          offset: '15px'
                          , icon: 2
                          , time: 1000
                      });
                  }
                  }
              });
            });

            submit.trigger('click');
          }
        });
      }
    }

    $('.layui-btn.layuiadmin-btn-menu_decorate').on('click', function(){
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    // 监听编辑事件
    table.on('edit(menu_decorate-lists)', function (obj) {
      var ids = [];
      var id = obj.data.id;
      var fields = obj.field;
      var field_value = obj.value;
      ids.push(id);
      if(isNaN(field_value)){
        var old_value=$(this).prev().text();
        layer.tips('请输入数字', $(this), {tips: [1, '#FF5722']});
        $(this).val(old_value);
        return false;
      }
      changeFields(ids,fields,field_value)
    });


    //监听工具条
    table.on('tool(menu_decorate-lists)', function(obj){
      if(obj.event === 'del'){
        var id = obj.data.id;
        var name = obj.data.name;
        var decorate_type = obj.data.decorate_type;
        var tips = '确定删除菜单：';
        if(decorate_type == 2){
            tips = '确定删除导航：';
        }

        layer.confirm(tips+'<span style="color: red">'+name+'</span>', function(index){
          $.ajax({
            url:'{:url("menu_decorate/del")}',
            data:{'id':id},
            type:"post",
            success:function(res)
            {
              if(res.code == 1)
              {
                obj.del();
                layui.layer.msg(res.msg, {
                    offset: '15px'
                    , icon: 1
                    , time: 1000
                });
                layer.close(index); //关闭弹层
              }else{
                layer.msg(res.msg, {
                    offset: '15px'
                    , icon: 2
                    , time: 1000
                });
              }
            }
          });
        });
      }

      if(obj.event === 'edit'){
        var id = obj.data.id;
        layer.open({
          type: 2
          ,title: '编辑菜单'
          ,content: '{:url("menu_decorate/edit")}?id='+id
          ,area: ['90%','90%']
          ,btn: ['确定', '取消']
          ,yes: function(index, layero){
            var iframeWindow = window['layui-layer-iframe'+ index]
                ,submitID = 'edit-menu_decorate-submit'
                ,submit = layero.find('iframe').contents().find('#'+ submitID);

            //监听提交
            iframeWindow.layui.form.on('submit('+ submitID +')', function(data){
                var field = data.field;
                field['decorate_type'] = 1
                $.ajax({
                  url:'{:url("menu_decorate/edit")}',
                  data:field,
                  type:"post",
                  success:function(res)
                  {
                    if(res.code == 1)
                    {
                      layui.layer.msg(res.msg, {
                          offset: '15px'
                          , icon: 1
                          , time: 1000
                      });
                      layer.close(index); //关闭弹层
                      table.reload('menu_decorate-lists'); //数据刷新
                    }else{
                      iframeWindow.layer.msg(res.msg, {
                          offset: '15px'
                          , icon: 2
                          , time: 1000
                      });
                    }
                  }
                });
              });

              submit.trigger('click');
            }
        })
      }

      if(obj.event === 'tips'){
        layer.tips('数字越大，越靠前', $(this), {tips: [1, '#FF5722'],time:1500});
      }
    });

    //上传图片
    like.imageUpload('.img-add', function (uris, element) {
    if(uris.length>1){
        layer.msg('最多最能选中1张图片');
        return;
    }
    var html = '<div class="img-container">\n' +
        '<img style="width:100px" class="img-src" ' +
        'src="' + uris[0] + '">' +
        '<a class="img-del-x">x</a>' +
        '</div>';
    element.prev().val(like.getUrlFileName(uris[0], '{$storageUrl}'));
    element.parent().append(html);
    element.css('display','none');
  }, true);
  //删除图片
  $(document).on('click', '.img-del-x', function () {
      $(this).parent().siblings('input').val('');
      $(this).parent().prev().css('display','block');
      $(this).parent().remove();
  });
  //显示图片
  $(document).on('click', '.img-src', function () {
      var image = $(this).attr('src');
      like.showImg(image,600);
  });

  // 其它设置 - 表单提交
  form.on('submit(index-setting)', function(data) {
    $.ajax({
      url:'{:url("menu_decorate/setIndexSetting")}',
      data: data.field,
      type:"post",
      success:function(res)
      {
        if(res.code == 1)
        {
          layui.layer.msg(res.msg, {
            offset: '15px'
            , icon: 1
            , time: 1000
          });
        }else{
          layui.layer.msg(res.msg, {
            offset: '15px'
            , icon: 2
            , time: 1000
          }, function() {
            location.reload();
          });
        }
      }
    });
    return false;
  });
});
</script>