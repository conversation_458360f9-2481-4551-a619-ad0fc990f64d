{layout name="layout2" /}
<style>
    .layui-form-label {
        width: 120px;
    }
    .pay-li {
        float: left;
        opacity: 1;
        position: relative;
    }
    .pay-img {
        width: 80px;
        height: 80px;
        padding: 4px;
    }
    .pay-img-del-x {
        position: absolute;
        z-index: 100;
        top: -4px;
        right: -2px;
        width: 20px;
        height: 20px;
        font-size: 16px;
        line-height: 16px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        background: hsla(0, 0%, 60%, .6);
        border-radius: 10px;
    }
    .copy{
        margin-left: 10px;
        margin-top: 5px;
    }


    .cert-add,.key-add{
        height: 80px;
        width: 80px;
        float: left;
        opacity: 1;
        position: relative;
        border:1px dashed #a0a0a0;
        background-image:url('/static/common/image/default/add_file.png');
        background-repeat: no-repeat;
        background-position: 50% 35%;
        background-size:40px 40px;
        margin: 4px;
        text-align: center;
    }
    .upload-cert-a{
        cursor: pointer;
        position: absolute;
        z-index: 100;
        top: 58px;
        right: -10%;
        width: 100px;
        height: 20px;
        font-size: 8px;
        line-height: 16px;
        text-align: center;
        border-radius: 10px;
        color: #4e8bff;
    }
    .upload-cert-a:hover {
        color: #0641cb;
    }
    .pay-cert{
        height:80px;line-height:80px
    }


</style>
<div class="layui-form" lay-filter="layuiadmin-form-official" id="layuiadmin-form-official" style="padding: 20px 30px 0 0;">

    <div class="layui-form-item">
        <label class="layui-form-label">支付简称：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="short_name" value="{$info.short_name}" lay-verify="check_required" lay-verType="tips"   autocomplete="off" class="layui-input ">
                <div class="layui-form-mid layui-word-aux">会员在商城看见的支付名称</div>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">支付图标</label>
        <div class="layui-input-inline">
            <div style="height:80px;line-height:80px">
                <input name="icon" type="hidden" value="{$info.icon}">
                {if !empty($info.icon)}
                <div class="pay-img-add" style="display: none" ></div>
                <div class="pay-li">
                    <img class="pay-img" src="{$info.icon}">
                    <a class="pay-img-del-x" style="display: none">x</a>
                </div>
                {else}
                <div class="pay-img-add" ></div>
                {/if}
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <label class=" layui-form-mid layui-word-aux">支付方式图标。建议尺寸：宽100px*高100px，jpg，jpeg，png格式</label>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">商户API密钥：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="pay_sign_key" value="{$info.config.pay_sign_key | default = ''}" lay-verify="check_required" lay-verType="tips"  autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux">微信支付商户API密钥（paySignKey）</div>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">微信支付商户号：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text" name="mch_id" value="{$info.config.mch_id | default = ''}" lay-verify="check_required" lay-verType="tips"  autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux">微信支付商户号（MCHID）</div>
            </div>
        </div>
    </div>

    <!--支付证书-->
    <div class="layui-form-item">
        <label class="layui-form-label">支付证书：</label>
        <div class="layui-inline">
            <div class="pay-cert">
                <input class="text" type="hidden" name="apiclient_cert" value="{$info.config.apiclient_cert | default = ''}" >
                {if !empty($info.config.apiclient_cert)}
                    <div class="cert-add" style="display: none;cursor: pointer;">
                        <a class="upload-cert-a">+ 添加文件</a>
                    </div>
                    <div class="pay-li">
                        <img class="pay-img"  src="/static/common/image/default/upload.png">
                        <a class="pay-img-del-x" style="display: none">x</a>
                    </div>
                {else/}
                    <div class="cert-add" style="cursor: pointer;">
                        <a class="upload-cert-a">+ 添加文件</a>
                    </div>
                {/if}
            </div>
            <div class=" layui-form-mid layui-word-aux">
                微信支付证书（apiclient_cert.pem），前往微信商家平台下载，文件名一般为apiclient_cert.pem
            </div>
        </div>
    </div>

    <!--支付证书密钥-->
    <div class="layui-form-item">
        <label class="layui-form-label">支付证书密钥：</label>
        <div class="layui-inline">
            <div class="pay-cert">
                <input class="text" type="hidden" name="apiclient_key" value="{$info.config.apiclient_key | default = ''}" >
                {if !empty($info.config.apiclient_key)}
                    <div class="key-add" style="display: none;cursor: pointer;">
                        <a class="upload-cert-a">+ 添加文件</a>
                    </div>
                    <div class="pay-li">
                        <img class="pay-img"  src="/static/common/image/default/upload.png">
                        <a class="pay-img-del-x" style="display: none">x</a>
                    </div>
                {else/}
                    <div class="key-add" style="cursor: pointer;">
                        <a class="upload-cert-a">+ 添加文件</a>
                    </div>
                {/if}
            </div>
            <div class=" layui-form-mid layui-word-aux">
                微信支付证书密钥（apiclient_key.pem），前往微信商家平台下载。文件名一般为apiclient_key.pem
            </div>
        </div>
    </div>

    <!--支付授权目录-->
    <div class="layui-form-item">
        <label class="layui-form-label" >支付授权目录：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="text"   value="{$domain}/" autocomplete="off" class="layui-input">
            </div>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm copy" >复制</button>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <label class="layui-form-mid layui-word-aux">支付授权目录仅供参考，请根据微信支付的系统提示进行设置</label>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">排序：</label>
        <div class="layui-input-block">
            <div class="layui-col-md4">
                <input type="number"  name="sort" value="{$info.sort}"  class="layui-input" autocomplete="off">
                <div class=" layui-form-mid layui-word-aux">排序越小越前</div>
            </div>
        </div>
    </div>


    <div class="layui-form-item">
        <label class="layui-form-label">状态：</label>
        <div class="layui-input-block">
            <input type="radio" name="status" value=1 title="启用"{if condition="$info.status eq 1" }checked{/if}>
            <input type="radio" name="status" value=0 title="关闭" {if condition="$info.status eq 0" }checked{/if}>
        </div>
    </div>

    <div class="layui-form-item">
        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="pay_config-submit-edit" id="pay_config-submit-edit" value="确认">
        </div>
    </div>
</div>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'form','like','upload'], function(){
        var $ = layui.$
            ,form = layui.form
            ,like = layui.like
            ,upload = layui.upload;

        //复制
        $(document).on('click', '.copy', function () {
            var copyText = $(this).prev().children();
            copyText.select();
            document.execCommand("Copy");
        });


        //==========================================上传证书start=========================================================

        like.certUpload('.cert-add', '{:url("file/other")}?local=1&sub_dir=cert', '{$storageUrl}');
        like.certUpload('.key-add', '{:url("file/other")}?local=1&sub_dir=cert', '{$storageUrl}');

        //==========================================上传证书end======================================================



        //===========================================支付图标start======================================================
        like.imageUpload('.pay-img-add', function (uri, element) {
            if(uri.length>1){
                layer.msg('最多最能选中1张图片');
                return;
            }
            var html = '<div class="pay-li">\n' +
                '<img class="pay-img" ' +
                'src="' + uri[0] + '">' +
                '<a class="pay-img-del-x">x</a>' +
                '</div>';
            element.prev().val(like.getUrlFileName(uri[0], '{$storageUrl}'));
            element.parent().append(html);
            element.css('display','none');
        }, true);

        //===========================================支付图标end======================================================


        //  删除按钮的显示与隐藏
        $(document).on('mouseover', '.pay-img', function () {
            $(this).next().show();
        });
        $(document).on('mouseout', '.pay-img', function () {
            $(this).next().hide();
        });
        $(document).on('mouseover', '.pay-img-del-x', function () {
            $(this).show();
        });
        $(document).on('mouseout', '.pay-img-del-x', function () {
            $(this).hide();
        });


        //删除图片/证书
        $(document).on('click', '.pay-img-del-x', function () {
            $(this).parent().siblings('input').val('');
            $(this).parent().siblings().css('display','block');
            $(this).parent().remove();
        });



        form.verify({
            check_required: function (value, item) {
                var status = $('input[name="status"]:checked').val();
                value = value.trim();
                if (status == 1 && value.length == 0) {
                    return '请填写完整';
                }
            }
        });

    })
</script>