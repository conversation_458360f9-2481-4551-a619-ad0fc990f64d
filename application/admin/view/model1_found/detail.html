{layout name="layout1" /}
<style>
    .layui-table-cell { height: auto; }
    .layui-input-block {
        line-height: 38px;
    }
</style>
<div class="layui-fluid">
    <div class="layui-card">

        <div class="layui-form layui-card-body">
            <!-- 创建人信息 -->
            <div class="layui-form-item" style="margin-bottom:0;">
                <span>创建人信息</span>
            </div>
            <div style="margin-left:30px">
                <div class="layui-form-item" style="margin-bottom:0;">
                    <label class="layui-form-label">会员编号：</label>
                    <div class="layui-input-block">{$detail.user.sn}</div>
                </div>
                <div class="layui-form-item" style="margin-bottom:0;">
                    <label class="layui-form-label">会员昵称：</label>
                    <div class="layui-input-block">{$detail.user.nickname}</div>
                </div>
                <div class="layui-form-item" style="margin-bottom:0;">
                    <label class="layui-form-label">手机号码：</label>
                    <div class="layui-input-block">{$detail.user.mobile}</div>
                </div>
                <div class="layui-form-item" style="margin-bottom:0;">
                    <label class="layui-form-label">会员等级：</label>
                    <div class="layui-input-block">{$detail.user_level}</div>
                </div>
            </div>
            <!-- 活动信息 -->
            <div class="layui-form-item" style="margin-bottom:0;">
                <span>活动信息</span>
            </div>
            <div style="margin-left:30px">
                <div class="layui-form-item" style="margin-bottom:0;">
                    <label class="layui-form-label">完成人数：</label>
                    <div class="layui-input-block">{$detail.need}/{$detail.join}</div>
                </div>
                <div class="layui-form-item" style="margin-bottom:0;">
                    <label class="layui-form-label">开始时间：</label>
                    <div class="layui-input-block">{$detail.found_time}</div>
                </div>
                <div class="layui-form-item" style="margin-bottom:0;">
                    <label class="layui-form-label">结束时间：</label>
                    <div class="layui-input-block">{$detail.found_end_time}</div>
                </div>
                <div class="layui-form-item" style="margin-bottom:0;">
                    <label class="layui-form-label">活动状态：</label>
                    <div class="layui-input-block">{$detail.status}</div>
                </div>
            </div>
            <!-- 数据表格 -->
            <div class="layui-form-item" style="margin:30px 0 10px 0;">
                <span>活动订单</span>
            </div>
            <div style="margin-left:30px">
                <table id="table-lists" lay-filter="table-lists"></table>
                <script type="text/html" id="table-goods">
                    <div>
                        <img src="{{d.image}}" alt="图片" style="width:60px;height:60px;float:left;" class="image-show">
                        <div style="margin-left:5px; width:150px;height:60px;white-space:normal;float: left;">{{d.name}}</div>
                    </div>
                </script>
                <!--会员信息-->
                <script type="text/html" id="table-user">
                    <div class="layui-input-inline"  style="text-align: left">
                        {{#  if(d.user){ }}
                            <p>会员编号：{{d.user.sn}}</p>
                            <p>手机号码：{{d.user.mobile}}</p>
                            <p>会员昵称：{{d.user.nickname}}</p>
                            <p>会员等级：{{d.user_level}}</p>
                        {{#  } }}
                    </div>
                </script>
            </div>
        </div>
    </div>
</div>

<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index','table','like'], function(){
        var $ = layui.$
            ,form = layui.form
            ,table = layui.table
            ,like = layui.like;

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src);
        });

        // 渲染数据表格
        table.render({
            elem: '#table-lists'
            ,url: '{:url("Model1Found/detail")}?found_id={$found_id}'
            ,cols: [[
                {field: 'userInfo', title: '参与人',width:280, templet: '#table-user'}
                ,{field: 'relation', title: '关系',width:80, align: 'center'}
                ,{field: 'order_sn', title: '订单号', width:180, align: 'center'}
                ,{field: 'goods', title: '商品信息',width:250, templet: '#table-goods'}
                ,{field: 'create_time', title: '下单时间', width:160, align: 'center'}
                ,{field: 'total_amount', title: '下单金额', width:100, align: 'center'}
                ,{field: 'order_status', title: '订单状态',width:100, align: 'center'}
            ]]
            ,page:true
            ,text: {none: '暂无数据！'}
            ,parseData: function(res){
                return {
                    "code":res.code,
                    "msg":res.msg,
                    "count": res.data.count,
                    "data": res.data.lists
                };
            }
            ,done: function(res, curr, count){
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });
    });
</script>