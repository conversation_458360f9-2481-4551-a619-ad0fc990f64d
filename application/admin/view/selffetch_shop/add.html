{layout name="layout2" /}
<style>
    .map-container {
        width: 600px;
        height: 400px;
        margin-left:100px;
        margin-top:20px;
    }
</style>
<link rel="stylesheet" href="/static/admin/css/goods.css" media="all">

<div class="layui-form" lay-filter="layuiadmin-form-cate" id="layuiadmin-form-cate" style="padding: 20px 30px 0 0;">

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>门店名称：</label>
        <div class="layui-input-inline">
            <input type="text" name="name" lay-verify="required" lay-verType="tips"  placeholder="" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>门店LOGO：</label>
        <div class="layui-input-inline">
            <div class="" style="height:80px;line-height:80px">
                <input name="image" lay-verify="required" type="hidden" value="">
                <div class="goods-img-add"></div>
            </div>
            <div class=" layui-form-mid layui-word-aux"  style="white-space: nowrap">建议尺寸：100*100像素，jpg，jpeg，png图片类型</div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>联系人：</label>
        <div class="layui-input-inline">
            <input type="text" name="contact" lay-verify="required" lay-verType="tips"  placeholder="" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>联系电话：</label>
        <div class="layui-input-inline">
            <input type="text" name="mobile" lay-verify="required" lay-verType="tips"  placeholder="" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>门店地址：</label>
        <div class="layui-input-inline">
            <select name="province" id="province" lay-filter="province" lay-verify="required"
                    lay-verType="tips" switch-tab="0">
            </select>
        </div>
        <div class="layui-input-inline">
            <select name="city" id="city" lay-filter="city" lay-verify="required">
            </select>
        </div>
        <div class="layui-input-inline">
            <select name="district" id="district" lay-filter="district" lay-verify="required">
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>详细地址：</label>
        <div class="layui-input-inline">
            <input type="text" name="address" lay-verify="required" lay-verType="tips"  placeholder="" autocomplete="off" class="layui-input">
        </div>
        <div class="layui-input-inline">
            <button class="layui-btn layui-btn-normal" id="search_map">搜索地图</button>
        </div>
    </div>

    <div class="layui-form-item" style="display:none;">
        <div class="layui-input-inline " id ='position_id' style="width: 800px;">
            <label class="layui-form-label" style="right:10px">地图坐标:</label>
            <div class="layui-input-inline" style="width:200px;">
                <input type="text" name="longitude" value="" class="layui-input">
            </div>
            <div class=" layui-form-mid">经度</div>
            <div class="layui-input-inline" style="width:200px;">
                <input type="text" name="latitude" value="" class="layui-input">
            </div>
            <div class=" layui-form-mid">纬度</div>
        </div>
    </div>

    <div class="layui-form-item ">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>地图定位：</label>
        <div class="layui-input-block" style="margin-left:10px;">
            <div class="map-container" id="map-container"></div>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>营业周天：</label>
        <div class="layui-input-block">
            <input type="checkbox" name="weekdays[]" value="1" lay-skin="primary" title="周一">
            <input type="checkbox" name="weekdays[]" value="2" lay-skin="primary" title="周二">
            <input type="checkbox" name="weekdays[]" value="3" lay-skin="primary" title="周三">
            <input type="checkbox" name="weekdays[]" value="4" lay-skin="primary" title="周四">
            <input type="checkbox" name="weekdays[]" value="5" lay-skin="primary" title="周五">
            <input type="checkbox" name="weekdays[]" value="6" lay-skin="primary" title="周六">
            <input type="checkbox" name="weekdays[]" value="7" lay-skin="primary" title="周天">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>营业时段：</label>
        <div class="layui-input-inline">
            <div class="layui-input-inline">
                <input type="text" class="layui-input" id="business_start_time" name="business_start_time" lay-verify="required">
            </div>
        </div>
        <div class="layui-input-inline" style="margin-right: 5px;width: 20px;">
            <label class="layui-form-mid">—</label>
        </div>
        <div class="layui-input-inline">
            <input type="text" class="layui-input" id="business_end_time" name="business_end_time" lay-verify="required">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">门店简介：</label>
        <div class="layui-input-block" style="margin-left: 110px">
            <textarea name="remark" lay-verify="remark" style="width: 30%;" class="layui-textarea"></textarea>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><span class="form-label-asterisk">*</span>门店状态：</label>
        <div class="layui-input-block">
            <input type="radio" name="status" value="0" title="停用">
            <input type="radio" name="status" value="1" title="启用">
        </div>
    </div>


    <div class="layui-form-item layui-hide">
        <input type="button" lay-submit lay-filter="add-cate-submit" id="add-cate-submit" value="确认">
    </div>
</div>

<!-- 替换旧版腾讯地图JS API为新版GL -->
<script charset="utf-8" src="https://map.qq.com/api/gljs?v=1.exp&key={$tx_map_key}"></script>
<script>
    layui.config({
        version:"{$front_version}",
        base: '/static/plug/layui-admin/dist/layuiadmin/'
    }).extend({
        index: 'lib/index'
    }).use(['index', 'form','like','laydate'], function(){
        var $ = layui.$
            ,form = layui.form
            ,like = layui.like
            ,laydate = layui.laydate;


        /*************************图片处理**********************************************/
        //上传图片
        like.imageUpload('.goods-img-add', function (uri, element) {
            if(uri.length>1){
                layer.msg('最多最能选中1张图片');
                return;
            }
            var html = '<div class="goods-li">\n' +
                '<img class="goods-img" ' +
                'src="' + uri[0] + '">' +
                '<a class="goods-img-del-x" style="display: none">x</a>' +
                '</div>';
            element.prev().val(like.getUrlFileName(uri[0], '{$storageUrl}'));
            element.parent().append(html);
            element.css('display','none');
        }, true);
        //删除图片
        $(document).on('click', '.goods-img-del-x', function () {
            $(this).parent().siblings('input').val('');
            $(this).parent().prev().css('display','block');
            $(this).parent().remove();
        });
        //显示图片
        $(document).on('click', '.goods-img', function () {
            var image = $(this).attr('src');
            like.showImg(image,600);
        });
        //  删除按钮的显示与隐藏
        $(document).on('mouseover', '.goods-img', function () {
            $(this).next().show();
        });
        $(document).on('mouseout', '.goods-img', function () {
            $(this).next().hide();
        });
        $(document).on('mouseover', '.goods-img-del-x', function () {
            $(this).show();
        });
        $(document).on('mouseout', '.goods-img-del-x', function () {
            $(this).hide();
        });


        /*************************省市区三级联动**********************************************/
        var area = JSON.parse('{$area_lists|json_encode|raw}');

        setSelectFirst();

        function setSelectFirst(default_id) {
            var category_select_html = '';
            for (var i in area) {
                if (area[i]['level'] == 1) {
                    category_select_html += '<option value="' + area[i]['id'] + '">' + area[i]['name'] + '</option>';
                }
            }
            $('select[name="province"]').html(category_select_html);
            $('select[name="province"]').val(default_id);
            form.render('select');
        }
        function setSelectSecond(default_id, pid) {
            pid = pid === undefined ? $('select[name="province"]').val() : pid;
            $('select[name="city"]').html('');
            $('select[name="district"]').html('');
            var category_select_html = '';
            for (var i in area) {
                if (area[i]['pid'] == pid) {
                    category_select_html += '<option value="' + area[i]['id'] + '">' + area[i]['name'] + '</option>';
                }
            }
            $('select[name="city"]').html(category_select_html);
            $('select[name="city"]').val(default_id);
            form.render('select');
        }
        function setSelectThird(default_id, pid) {
            pid = pid === undefined ? $('select[name="city"]').val() : pid;
            $('select[name="district"]').html('');
            var province = $('select[name="province"]').val();
            var category_select_html = '';
            for (var i in area) {
                if (area[i]['pid'] == pid) {
                    category_select_html += '<option value="' + area[i]['id'] + '">' + area[i]['name'] + '</option>';
                }
            }
            $('select[name="district"]').html(category_select_html);
            $('select[name="district"]').val(default_id);
            form.render('select');
        }

        form.on('select(province)', function (data) {
            setSelectSecond('', data.value);
        });
        form.on('select(city)', function (data) {
            setSelectThird('', data.value);
        });


        /*************************日期时间**********************************************/
        laydate.render({
            elem: '#business_start_time'
            ,type: 'time'
            ,trigger: 'click'
        });

        laydate.render({
            elem: '#business_end_time'
            ,type: 'time'
            ,trigger: 'click'
        });


        /*************************地图**********************************************/
        // 新版腾讯地图GL API初始化
        var map, marker;
        function initMap() {
            map = new TMap.Map("map-container", {
                center: new TMap.LatLng(39.98412, 116.307484), // 默认中心点
                zoom: 16
            });
            // 初始化marker为空
            window._marker = new TMap.MultiMarker({
                map: map,
                geometries: []
            });
        }
        initMap();

        // 搜索地图按钮事件
        $('#search_map').on('click', function(e){
            e.preventDefault();
            var province = $('#province option:selected').text();
            var city = $('#city option:selected').text();
            var district = $('#district option:selected').text();
            var address = $('input[name="address"]').val();
            var fullAddress = province + city + district + address;
            if(!address){
                layer.msg('请填写详细地址');
                return;
            }
            // 腾讯地图WebService API地址解析
            $.get('https://apis.map.qq.com/ws/geocoder/v1/', {
                address: fullAddress,
                key: '{$tx_map_key}'
            }, function(res){
                if(res.status === 0){
                    var location = res.result.location;
                    var latlng = new TMap.LatLng(location.lat, location.lng);
                    map.setCenter(latlng);
                    // 直接更新marker点
                    window._marker.setGeometries([{
                        id: 'marker',
                        position: latlng
                    }]);
                    // 设置经纬度到表单
                    $('input[name="longitude"]').val(location.lng);
                    $('input[name="latitude"]').val(location.lat);
                    $('#position_id').show();
                }else{
                    layer.msg('地址解析失败');
                }
            }, 'json');
        });
    });
</script>