<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\validate;

use app\common\model\AfterSale as CommonAfterSale;
use app\common\model\OrderGoods;
use app\common\model\Team;
use think\Db;
use think\Validate;

class Order extends Validate
{
    protected $rule = [
        'order_id|参数缺失' => 'require',
        'send_type|请选择配送方式' => 'require',//配送方式
        'shipping_id|请选择快递' => 'requireIf:send_type,1',//物流公司
        'invoice_no|快递单号' => 'requireIf:send_type,1|alphaNum',//单号
    ];

    protected $message = [
        'order_id.require' => '参数缺失',
        'invoice_no.alphaNum' => '请填写正确订单号',
    ];


    public function sceneCancel()
    {
        $this->only(['order_id'])
            ->append('order_id', 'checkCancel');
    }

    public function sceneDel()
    {
        $this->only(['order_id'])
            ->append('order_id', 'checkDel');
    }

    public function sceneDelivery()
    {
        $this->only(['order_id','send_type','shipping_id','invoice_no'])
            ->append('order_id','checkDelivery');
    }

    public function sceneConfirm()
    {
        $this->only(['order_id']);
    }

    protected function checkCancel($value, $reule, $data)
    {
        $order = Db::name('order')->where(['id' => $value, 'del' => 0])->find();

        if (!$order) {
            return '订单失效';
        }

        if ($order['order_status'] > \app\common\model\Order::STATUS_WAIT_DELIVERY) {
            return '此订单不可取消';
        }
    
        $where = [
            [ 'order_id', '=', $order['id'] ],
            [ 'status', 'in', CommonAfterSale::CanNotVerificationStatusArr() ],
            [ 'del', '=', 0 ],
        ];
    
        $after_sale = CommonAfterSale::where($where)->findOrEmpty();
    
        if (! $after_sale->isEmpty()) {
            return "订单存在售后，不能取消";
        }

        if ($order['order_type'] == \app\common\model\Order::TEAM_ORDER) {
            $found = Db::name('team_found')->where(['id' => $order['team_found_id']])->find();
            if ($found['status'] == Team::STATUS_WAIT_SUCCESS){
                return '已支付的拼团订单需要有拼团结果才可以取消';
            }
        }

        return true;
    }

    protected function checkDel($value, $reule, $data)
    {
        $order = Db::name('order')->where(['id' => $value])->find();

        if (!$order) {
            return '订单失效';
        }

        if ($order['del'] == 1){
            return '订单已删除';
        }

        if ($order['order_status'] != \app\common\model\Order::STATUS_CLOSE) {
            return '此订单不可删除';
        }
        return true;
    }

    protected function checkDelivery($value, $reule, $data)
    {
        $order = Db::name('order')->where(['id' => $value])->find();

        if (!$order) {
            return '订单失效';
        }

        if ($order['del'] == 1){
            return '订单已删除';
        }
        
        if ($order['order_status'] == \app\common\model\Order::STATUS_CLOSE) {
            return '订单已关闭，无法再发货';
        }

        if ($order['shipping_status'] == 1) {
            return '此订单已发货';
        }
        
        if ($order['order_type'] == \app\common\model\Order::TEAM_ORDER){
            $found = Db::name('team_found')->where(['id' => $order['team_found_id']])->find();
            if ($found['status'] != Team::STATUS_SUCCESS){
                return '已支付的拼团订单需要等待拼团成功后才能发货';
            }
        }
        
        $order_goods = OrderGoods::where('order_id', $value)->select()->toArray();
    
        foreach ($order_goods as $goods) {
            $where = [
                [ 'order_goods_id', '=', $goods['id'] ],
                [ 'order_id', '=', $goods['order_id'] ],
                [ 'status', 'in', CommonAfterSale::CanNotVerificationStatusArr() ],
                [ 'del', '=', 0 ],
            ];
        
            $after_sale = CommonAfterSale::where($where)->findOrEmpty();
        
            if (! $after_sale->isEmpty()) {
                return "商品：{$goods['goods_name']} 当前处于售后中，不能发货";
            }
        }

        return true;
    }
}