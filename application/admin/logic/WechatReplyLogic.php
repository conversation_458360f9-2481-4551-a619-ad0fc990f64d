<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------
namespace app\admin\logic;
use app\common\model\WeChat;
use think\Db;

class WechatReplyLogic{
    public static function lists($get){
        $where[] = ['del','=',0];
        if(isset($get['type'])){
            $where[] = ['reply_type','=',$get['type']];
        }
        $count = Db::name('wechat_reply')
                    ->where($where)
                    ->count();
        $list = Db::name('wechat_reply')
                    ->where($where)
                    ->page($get['page'],$get['limit'])
                    ->select();
        foreach ($list as $key =>  $reply) {
            $reply['content_type'] && $list[$key]['content_type'] = '文本';
            switch ($reply['matching_type']){
                case 1:
                    $list[$key]['matching_type'] = '全匹配';
                    break;
                case 2:
                    $list[$key]['matching_type'] = '模糊匹配';
                    break;
            }
        }
        return ['count'=>$count,'list'=>$list];


    }
    /**
     * note 添加微信回复
     * create_time 2020/12/5 15:24
     */
    public static function add($post){
        $post['create_time'] = time();
        $post['del'] = 0;
        if($post['reply_type'] !== WeChat::msg_type_text && $post['status']){
            Db::name('wechat_reply')->where(['reply_type'=>$post['reply_type']])->update(['update_time'=>time(),'status'=>0]);
        }
        return Db::name('wechat_reply')->insert($post);
    }

    /**
     * note 编辑微信回复
     * create_time 2020/12/5 15:24
     */
    public static function edit($post){
        $post['update_time'] = time();
        if($post['reply_type'] !== WeChat::msg_type_text && $post['status']){
            Db::name('wechat_reply')->where(['reply_type'=>$post['reply_type']])->update(['update_time'=>time(),'status'=>0]);
        }
        return Db::name('wechat_reply')->where(['id'=>$post['id']])->update($post);
    }

    /**
     * note 删除微信回复
     * create_time 2020/12/5 15:25
     */
    public static function del($id){

        return Db::name('wechat_reply')->where(['id'=>$id])->update(['update_time'=>time(),'del'=>1]);

    }


    /**
     * note 获取微信回复
     * create_time 2020/12/5 15:25
     */
    public static function getReply($id){
        return Db::name('wechat_reply')->where(['id'=>$id])->find();
    }

    /**
     * note
     * create_time 2020/12/5 18:56
     */
    public static function changeFields($id,$field,$field_value,$reply_type){
        if( 'status' === $field && $field_value && $reply_type !== WeChat::msg_type_text){
            Db::name('wechat_reply')->where(['reply_type'=>$reply_type])->update(['update_time'=>time(),'status'=>0]);
        }
        return Db::name('wechat_reply')->where(['id'=>$id])->update(['update_time'=>time(),$field=>$field_value]);
    }
}