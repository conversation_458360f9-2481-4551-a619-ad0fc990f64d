<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\logic;

use app\common\model\DistributionOrder;
use app\common\model\OrderGoods;
use app\common\model\Pay;
use app\common\model\Withdraw;
use think\Db;

class FinanceLogic
{
    public static function lists()
    {
        //本月订单金额
        $month_order_amount = Db::name('order')
            ->where(['pay_status' => Pay::ISPAID, 'refund_status' => OrderGoods::REFUND_STATUS_NO])
            ->whereTime('create_time', 'month')
            ->sum('order_amount');

        //订单总金额
        $order = Db::name('order')
            ->field('sum(order_amount) as amount, count(id) as num')
            ->where('pay_status' , 'in', [Pay::ISPAID, Pay::REFUNDED])
            ->find();

        //退款订单
        $refund_order = Db::name('order_refund')
            ->field('sum(refund_amount) as amount, count(id) as num')
            ->where(['refund_status' => 1])
            ->find();

        //会员相关
        $user = Db::name('user')
            ->field('sum(user_money) as money, sum(user_integral) as integral, sum(earnings) as earnings')
            ->where(['del' => 0])
            ->find();
        //统计所有user_money
        $total_zong_money = Db::name('user')
            ->where(['del' => 0])
            ->sum('user_money');

        //已提现佣金
        $have_withdraw_earnings = Db::name('withdraw_apply')
            ->where(['status' => Withdraw::STATUS_SUCCESS])
            ->sum('money');

        //提现中
        $wait_withdraw_earnings = Db::name('withdraw_apply')
            ->where(['status' => Withdraw::STATUS_ING])
            ->sum('money');


        //本月分销佣金金额
        $month_earnings = Db::name('distribution_order_goods')
            ->where('status', '<>', DistributionOrder::STATUS_ERROR)
            ->whereTime('create_time', 'month')
            ->sum('money');

        //分销佣金总佣金
        $distribution_earnings = Db::name('distribution_order_goods')
            ->where('status', '<>', DistributionOrder::STATUS_ERROR)
            ->sum('money');
        //积分使用总额
        $integral_total = Db::name('score_log')
            ->where(['change_type' => 2])
            ->sum('change_amount');
        //充值总额
        $recharge_total = Db::name('account_log')
            ->where(['change_type' => 1,'source_type'=>100,'source_id'=>0,'source_sn'=>''])
            ->sum('change_amount');
        //复购总额
        $rebuy_total = Db::name('account_log')
            ->where(['change_type' => 1,'source_type'=>502,'remark'=>'完成开团，单飞获得200%'])
            ->sum('change_amount');
        //奖金重复下单总额
        $bonus_total = Db::name('account_log')
            ->where(['change_type' => 2,'source_type'=>103,'remark'=>'完成开团，单飞后重新下单'])
            ->sum('change_amount');
        //微信支付成功的订单
        $wx_month_order_amount = Db::name('order')
            ->where(['pay_status' => Pay::ISPAID])
            ->where('pay_way', Pay::WECHAT_PAY)
            ->where('refund_status', OrderGoods::REFUND_STATUS_NO)
            ->sum('order_amount');

        return [
            'month_order_amount' => round($month_order_amount, 2),
            'total_amount' => round($order['amount'], 2),
            'order_num' => $order['num'] ?? 0,
            'refund_amount' => round($refund_order['amount'], 2),
            'refund_num' => $refund_order['num'] ?? 0,

            'total_user_money' => round($total_zong_money,2),//round($user['money'], 2),
            'total_user_integral' => $user['integral'] ?? 0,
            'able_earnings' => round($user['earnings'], 2),
            'have_withdraw_earnings' => round($have_withdraw_earnings, 2),

            'month_earnings' => round($month_earnings, 2),
            'distribution_earnings' => round($distribution_earnings, 2),
            'wait_earnings' => round($wait_withdraw_earnings, 2),

            'integral_total' => $integral_total,//积分使用总额
            'recharge_total' => $recharge_total,//充值总额
            'rebuy_total'=> $rebuy_total,//复购总额
            'bonus_total'=>$bonus_total,//奖金重复下单总额
            'wx_month_order_amount'=>$wx_month_order_amount
        ];
    }

}