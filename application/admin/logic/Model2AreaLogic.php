<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------
namespace app\admin\logic;
use app\common\server\UrlServer;
use think\Db;

class Model2AreaLogic{
    /**
     * note 活动专区列表
     * create_time 2020/11/24 11:23
     */
    public static function areaLists($get){
        $count = Db::name('model2_area')
                ->where(['del'=>0])
                ->count();

        $lists = Db::name('model2_area')
                ->where(['del'=>0])
                ->page($get['page'], $get['limit'])
                ->select();
        foreach ($lists as &$item){
            $item['status_desc'] = '下架';
            $item['status'] && $item['status_desc'] = '上架';
            $item['image'] = UrlServer::getFileUrl($item['image']);
        }

        return ['count'=>$count,'lists'=>$lists];

    }
    /**
     * note 活动专区商品
     * create_time 2020/11/24 11:24
     */
    public static function goodsLists($get){
        $where[] = ['AG.del','=',0];
        $where[] = ['AA.del','=',0];
        if(isset($get['name']) && $get['name']){
            $where[] = ['G.name','like','%'.$get['name'].'%'];
        }
        if(isset($get['model2_id']) && $get['model2_id']){
            $where[] = ['AA.id','=',$get['model2_id']];
        }

        $count = Db::name('model2_goods')->alias('AG')
                ->join('model2_area AA','AG.model2_id = AA.id')
                ->join('goods G','AG.Goods_id = G.id')
                ->where($where)
                ->count();


        $lists =  Db::name('model2_goods')->alias('AG')
                ->join('model2_area AA','AG.model2_id = AA.id')
                ->join('goods G','AG.Goods_id = G.id')
                ->where($where)
                ->field('AG.id,AG.goods_id,AG.model2_id,AA.name,AA.status,G.id,G.name,G.image')
                ->page($get['page'], $get['limit'])
                ->order('AG.id desc')
                ->select();
        $model2_lisst = Db::name('model2_area')
                    ->where(['del'=>0])
                    ->column('name,status','id');
        foreach ($lists as &$item){
            $item['model2_name'] = '';
            $item['status_desc'] = '下架';

            if(isset($model2_lisst[$item['model2_id']])){
                $item['model2_name'] = $model2_lisst[$item['model2_id']]['name'];
                $model2_lisst[$item['model2_id']]['status'] && $item['status_desc'] = '上架';
            }

        }

        return ['count'=>$count,'lists'=>$lists];
    }

    /**
     * note 获取全部的活动专区
     * create_time 2020/11/24 12:01
     */
    public static function getModel2List(){
        return Db::name('model2_area')
                ->where(['del'=>0])
                ->column('name','id');

    }
    /**
     * note 添加活动专区
     * create_time 2020/11/24 12:01
     */
    public static function addModel2($post){
        $data = [
            'name'          => $post['name'],
            'title'         => $post['title'],
            'image'         => $post['image'],
            'status'        => $post['status'],
            'del'           => 0,
            'create_time'   => time(),
        ];
        return Db::name('model2_area')->insert($data);
    }
    /**
     * note 编辑活动专区
     * create_time 2020/11/24 12:02
     */
    public static function editModel2($post){
        $data = [
            'name'          => $post['name'],
            'title'         => $post['title'],
            'image'         => $post['image'],
            'status'        => $post['status'],
            'update_time'   => time(),
        ];
        return Db::name('model2_area')->where(['id'=>$post['id']])->update($data);
    }
    /**
     * note 删除活动专区
     * create_time 2020/11/24 12:02
     */
    public static function delModel2($id){
        Db::name('model2_area')->where(['id'=>$id])->update(['update_time'=>time(),'del'=>1]);
        Db::name('model2_goods')->where(['model2_id'=>$id])->update(['update_time'=>time(),'del'=>1]);
        return true;

    }

    /**
     * note 获取活动商品详情
     * create_time 2020/11/25 10:40
     */
    public static function getModel2($id){
        $model2 = Db::name('model2_area')->where(['id'=>$id])->find();
        $model2['image'] = UrlServer::getFileUrl($model2['image']);
        return $model2;
    }

    /**
     * note 添加活动商品
     * create_time 2020/11/25 10:40
     */
    public static function addGoods($post)
    {
        return Db::name('model2_goods')->insert([
            'model2_id'   => $post['model2_id'],
            'goods_id'      => $post['goods_id'][0],
            'del'           => 0,
            'create_time'   => time(),
        ]);
    }

    /**
     * note 编辑活动商品
     * create_time 2020/11/25 10:40
     */
    public static function editGoods($post){
        $new = time();
        $update_data = [
            'model2_id'       => $post['model2_id'],
            'update_time'       => $new,
        ];

        return Db::name('model2_goods')
                ->where(['id'=>$post['id'],'model2_id'=>$post['model2_id']])
                ->update($update_data);

    }

    /**
     * note 删除活动商品
     * create_time 2020/11/25 10:40
     */
    public static function delGoods($goods_id,$model2_id){
        $update_data = [
            'update_time'   => time(),
            'del'           => 1,
        ];
        return Db::name('model2_goods')->where(['del'=>0,'goods_id'=>$goods_id,'model2_id'=>$model2_id])->update($update_data);
    }

    /**
     * note 获取活动商品详情
     * create_time 2020/11/25 10:41
     */
    public static function getModel2Goods($goods_id,$model2_id){
        $model2_list =  Db::name('model2_goods')->alias('AG')
                ->join('goods_item GI','AG.item_id = GI.id')
                ->where(['model2_id'=>$model2_id,'AG.goods_id'=>$goods_id])
                ->field('AG.*,GI.price,GI.spec_value_str,GI.image,GI.price')
                ->select();

        $goods_id = $model2_list[0]['goods_id'];
        $goods = Db::name('goods')->where(['del'=>0,'id'=>$goods_id])->field('image,name')->find();

        foreach ($model2_list as &$item){
            $item['name'] = $goods['name'];
            if(empty($item['image'])){
                $item['image'] = $goods['image'];
            }
        }
        return $model2_list;
    }
}