(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-webview-webview"],{"039f":function(n,r,e){"use strict";e("6a54"),Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;r.default={data:function(){return{url:""}},components:{},props:{},onLoad:function(n){this.url=n.url},methods:{}}},"34e4":function(n,r,e){"use strict";var t=e("7ea5"),a=e.n(t);a.a},"74d7":function(n,r,e){"use strict";e.d(r,"b",(function(){return t})),e.d(r,"c",(function(){return a})),e.d(r,"a",(function(){}));var t=function(){var n=this.$createElement,r=this._self._c||n;return r("v-uni-view",{staticClass:"page-body"},[r("v-uni-view",{staticClass:"page-section page-section-gap"},[r("v-uni-web-view",{attrs:{src:this.url}})],1)],1)},a=[]},"7ea5":function(n,r,e){var t=e("a9b7");t.__esModule&&(t=t.default),"string"===typeof t&&(t=[[n.i,t,""]]),t.locals&&(n.exports=t.locals);var a=e("967d").default;a("43f46f3a",t,!0,{sourceMap:!1,shadowMode:!1})},a9b7:function(n,r,e){var t=e("c86c");r=t(!1),r.push([n.i,"\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n/* pages/webview/webview.wxss */.page-section-gap[data-v-4f4a8f85]{box-sizing:border-box;padding:0 %?30?%}",""]),n.exports=r},d2f0:function(n,r,e){"use strict";e.r(r);var t=e("039f"),a=e.n(t);for(var i in t)["default"].indexOf(i)<0&&function(n){e.d(r,n,(function(){return t[n]}))}(i);r["default"]=a.a},da5d:function(n,r,e){"use strict";e.r(r);var t=e("74d7"),a=e("d2f0");for(var i in a)["default"].indexOf(i)<0&&function(n){e.d(r,n,(function(){return a[n]}))}(i);e("34e4");var u=e("828b"),o=Object(u["a"])(a["default"],t["b"],t["c"],!1,null,"4f4a8f85",null,!1,t["a"],void 0);r["default"]=o.exports}}]);