(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user_vip-user_vip"],{"11cd":function(t,e,i){"use strict";var a=i("83dd"),n=i.n(a);n.a},"200a":function(t,e,i){"use strict";i.r(e);var a=i("5229"),n=i("b7e4");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("a3d8");var o=i("828b"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"02ab9a7e",null,!1,a["a"],void 0);e["default"]=s.exports},"25f4":function(t,e,i){"use strict";i.r(e);var a=i("dc8e"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"2cab":function(t,e,i){"use strict";var a=i("885c"),n=i.n(a);n.a},"33fd":function(t,e,i){"use strict";i.r(e);var a=i("5530"),n=i("25f4");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("a84e");var o=i("828b"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"07bfc4b2",null,!1,a["a"],void 0);e["default"]=s.exports},"34d8":function(t,e,i){"use strict";i.r(e);var a=i("e3fa"),n=i("46f8");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("2cab");var o=i("828b"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"13a5beb4",null,!1,a["a"],void 0);e["default"]=s.exports},"46f8":function(t,e,i){"use strict";i.r(e);var a=i("c73c"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},5229:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={loading:i("34d8").default},n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:"loading "+("flex"==this.type?"flex":""),style:{backgroundColor:this.backgroundColor}},[e("loading",{attrs:{color:this.color,size:this.size}})],1)},r=[]},"527c":function(t,e,i){"use strict";i.r(e);var a=i("c8e5"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},5530:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uIcon:i("5b98").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{class:{"custom-image":!0,"image-round":t.round},style:[t.viewStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.error?t._e():i("v-uni-image",{staticClass:"image",attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoaded.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrored.apply(void 0,arguments)}}}),t.loading&&t.showLoading?i("v-uni-view",{staticClass:"loading-wrap image"},[t.useLoadingSlot?t._t("loading"):i("u-icon",{attrs:{color:"#aaa",name:"photo-fill",size:"45"}})],2):t._e(),t.error&&t.showError?i("v-uni-view",{staticClass:"error-wrap image"},[t.useErrorSlot?t._t("error"):i("u-icon",{attrs:{color:"#aaa",name:"error-circle-fill",size:"45"}}),i("v-uni-text",{staticClass:"sm"},[t._v("加载失败")])],2):t._e()],1)},r=[]},"83dd":function(t,e,i){var a=i("c567");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("de1fec04",a,!0,{sourceMap:!1,shadowMode:!1})},"881e":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'[data-v-13a5beb4]:host{font-size:0;line-height:1}.loading[data-v-13a5beb4]{display:inline-flex;align-items:center;justify-content:center;color:#c8c9cc}.loading__spinner[data-v-13a5beb4]{position:relative;box-sizing:border-box;width:%?45?%;max-width:100%;max-height:100%;height:%?45?%;-webkit-animation:rotate-data-v-13a5beb4 .8s linear infinite;animation:rotate-data-v-13a5beb4 .8s linear infinite}.loading__spinner--spinner[data-v-13a5beb4]{-webkit-animation-timing-function:steps(12);animation-timing-function:steps(12)}.loading__spinner--circular[data-v-13a5beb4]{border:%?2?% solid transparent;border-top-color:initial;border-radius:100%}.loading__text[data-v-13a5beb4]{margin-left:%?16?%;color:#969799;font-size:%?28?%;line-height:%?40?%}.loading__text[data-v-13a5beb4]:empty{display:none}.loading--vertical[data-v-13a5beb4]{-webkit-flex-direction:column;flex-direction:column}.loading--vertical .loading__text[data-v-13a5beb4]{margin:%?16?% 0 0}.loading__dot[data-v-13a5beb4]{position:absolute;top:0;left:0;width:100%;height:100%}.loading__dot[data-v-13a5beb4]:before{display:block;width:%?4?%;height:25%;margin:0 auto;background-color:currentColor;border-radius:40%;content:" "}.loading__dot[data-v-13a5beb4]:first-of-type{-webkit-transform:rotate(30deg);transform:rotate(30deg);opacity:1}.loading__dot[data-v-13a5beb4]:nth-of-type(2){-webkit-transform:rotate(60deg);transform:rotate(60deg);opacity:.9375}.loading__dot[data-v-13a5beb4]:nth-of-type(3){-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:.875}.loading__dot[data-v-13a5beb4]:nth-of-type(4){-webkit-transform:rotate(120deg);transform:rotate(120deg);opacity:.8125}.loading__dot[data-v-13a5beb4]:nth-of-type(5){-webkit-transform:rotate(150deg);transform:rotate(150deg);opacity:.75}.loading__dot[data-v-13a5beb4]:nth-of-type(6){-webkit-transform:rotate(180deg);transform:rotate(180deg);opacity:.6875}.loading__dot[data-v-13a5beb4]:nth-of-type(7){-webkit-transform:rotate(210deg);transform:rotate(210deg);opacity:.625}.loading__dot[data-v-13a5beb4]:nth-of-type(8){-webkit-transform:rotate(240deg);transform:rotate(240deg);opacity:.5625}.loading__dot[data-v-13a5beb4]:nth-of-type(9){-webkit-transform:rotate(270deg);transform:rotate(270deg);opacity:.5}.loading__dot[data-v-13a5beb4]:nth-of-type(10){-webkit-transform:rotate(300deg);transform:rotate(300deg);opacity:.4375}.loading__dot[data-v-13a5beb4]:nth-of-type(11){-webkit-transform:rotate(330deg);transform:rotate(330deg);opacity:.375}.loading__dot[data-v-13a5beb4]:nth-of-type(12){-webkit-transform:rotate(1turn);transform:rotate(1turn);opacity:.3125}@-webkit-keyframes rotate-data-v-13a5beb4{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes rotate-data-v-13a5beb4{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},"885c":function(t,e,i){var a=i("881e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("262c9b88",a,!0,{sourceMap:!1,shadowMode:!1})},a3d8:function(t,e,i){"use strict";var a=i("c471"),n=i.n(a);n.a},a84e:function(t,e,i){"use strict";var a=i("fbc3"),n=i.n(a);n.a},b119:function(t,e,i){"use strict";i.r(e);var a=i("ff077"),n=i("527c");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("11cd");var o=i("828b"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"78d4357c",null,!1,a["a"],void 0);e["default"]=s.exports},b7e4:function(t,e,i){"use strict";i.r(e);var a=i("ea18"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},b97e:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".loading[data-v-02ab9a7e]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;display:flex;justify-content:center;align-items:center}.loading.flex[data-v-02ab9a7e]{position:static;flex:1;width:100%}",""]),t.exports=e},c471:function(t,e,i){var a=i("b97e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("73347308",a,!0,{sourceMap:!1,shadowMode:!1})},c567:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body[data-v-78d4357c]{background-color:#fff}body.?%PAGE?%[data-v-78d4357c]{background-color:#fff}uni-page-body .user-vip .header[data-v-78d4357c]{background-image:url(https://www.xinjiuhui.cn/image/vip_grade_bg.png);padding-top:%?30?%;background-size:100% 100%;height:%?382?%}uni-page-body .user-vip .header .user-vip-info[data-v-78d4357c]{padding-left:%?30?%}uni-page-body .user-vip .header .user-vip-info .user-level[data-v-78d4357c]{border:1px solid #fff;border-radius:%?100?%;padding:%?4?% %?20?%;line-height:%?30?%}uni-page-body .user-vip .header .user-vip-info .user-text[data-v-78d4357c]{line-height:%?50?%;font-weight:700}uni-page-body .user-vip .content[data-v-78d4357c]{margin-top:%?-200?%}uni-page-body .user-vip .content .vip-card-item[data-v-78d4357c]{height:%?320?%;width:%?600?%;position:relative;background-size:100% 100%}uni-page-body .user-vip .content .vip-card-item .grade[data-v-78d4357c]{line-height:%?36?%;background-color:rgba(0,0,0,.5);border-top-right-radius:%?100?%;border-bottom-right-radius:%?100?%;height:%?50?%;padding:0 %?28?%}uni-page-body .user-vip .content .vip-card-item .user-grade[data-v-78d4357c]{line-height:%?36?%;margin-left:%?30?%}uni-page-body .user-vip .content .vip-card-item .grade-icon[data-v-78d4357c]{width:%?120?%;height:%?100?%}uni-page-body .user-vip .content .vip-card-item .vip-name[data-v-78d4357c]{padding:%?10?% %?30?%;font-size:%?46?%;text-align:center;align-items:flex-end;margin-bottom:%?30?%}uni-page-body .user-vip .content .vip-card-item .vip-progress[data-v-78d4357c]{height:%?8?%;width:%?540?%}uni-page-body .user-vip .content .vip-card-item .vip-progress .vip-progress-bar[data-v-78d4357c]{background-color:#f8d07c;height:100%}uni-page-body .user-vip .content .vip-grade-rule[data-v-78d4357c]{margin:%?24?% %?40?%}uni-page-body .user-vip .content .vip-grade-rule .title .line[data-v-78d4357c]{width:%?8?%;height:%?34?%;background-color:#f79c0c}uni-page-body .user-vip .content .vip-rights[data-v-78d4357c]{margin:%?24?% %?40?%}uni-page-body .user-vip .content .vip-rights .title[data-v-78d4357c]{padding:%?28?% 0}uni-page-body .user-vip .content .vip-rights .title .line[data-v-78d4357c]{width:%?8?%;height:%?34?%;background-color:#f79c0c}uni-page-body .user-vip .content .vip-rights .rights-item[data-v-78d4357c]{width:25%;padding-bottom:%?30?%}uni-page-body .user-vip .content .vip-rights .rights-item uni-image[data-v-78d4357c]{width:%?82?%;height:%?82?%}',""]),t.exports=e},c73c:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("08eb"),i("18f7");var a={props:{color:String,vertical:Boolean,type:{type:String,default:"spinner"},size:{type:Number,default:40},textSize:String},data:function(){return{array12:Array.from({length:12})}}};e.default=a},c8e5:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bd06");var a=i("695c"),n={data:function(){return{userInfo:{},currentIndex:0,levelList:[],growthRule:"",privilegeList:[]}},components:{},props:{},onLoad:function(t){this.getLevelListFun()},methods:{bindchange:function(t){var e=t.detail.current,i=this.levelList[e];this.currentIndex=e,this.privilegeList=i.level_privilege},getLevelListFun:function(){var t=this;(0,a.getLevelList)().then((function(e){var i=e.code,a=e.data;if(1==i){var n=a.user,r=a.growth_rule,o=a.level_list,s=o.findIndex((function(t){return 1==t.current_level_status}));-1==s&&(s=0),t.userInfo=n,t.growthRule=r,t.levelList=o,t.currentIndex=s,t.privilegeList=o[s].level_privilege}}))}}};e.default=n},dc8e:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{src:{type:String},round:Boolean,width:{type:null},height:{type:null},radius:null,lazyLoad:{type:Boolean,default:!0},useErrorSlot:Boolean,useLoadingSlot:Boolean,showMenuByLongpress:Boolean,mode:{type:String,default:"scaleToFill"},showError:{type:Boolean,default:!0},showLoading:{type:Boolean,default:!0},customStyle:{type:Object,default:function(){}}},data:function(){return{error:!1,loading:!0,viewStyle:{}}},created:function(){this.setStyle()},methods:{setStyle:function(){var t=this.width,e=this.height,i=this.radius,a={};t&&(a.width=t),e&&(a.height=e),i&&(a["overflow"]="hidden",a["border-radius"]=i),this.viewStyle=a,this.customStyle&&(this.viewStyle=Object.assign(this.viewStyle,this.customStyle))},onLoaded:function(t){this.loading=!1,this.$emit("load",t.detail)},onErrored:function(t){this.error=!1,this.loading=!0,this.$emit("error",t.detail)},onClick:function(t){this.$emit("click",t.detail)}},watch:{src:function(){this.error=!1,this.loading=!0},width:function(){this.setStyle()},height:function(){this.setStyle()}}};e.default=a},e3fa:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{class:"loading "+(t.vertical?"loading--vertical":"")},[i("v-uni-view",{class:"loading__spinner loading__spinner--"+t.type,style:{color:t.color,width:t.size+"rpx",height:t.size+"rpx"}},t._l(t.array12,(function(e,a){return"spinner"===t.type?i("v-uni-view",{key:a,staticClass:"loading__dot"}):t._e()})),1),i("v-uni-view",{staticClass:"loading__text",style:{"font-size":t.textSize+"rpx",color:t.color}},[t._t("default")],2)],1)},n=[]},ea18:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={data:function(){return{}},props:{type:{type:String,default:"fixed"},backgroundColor:{type:String,default:"#fff"},color:{type:String},size:{type:Number,default:40}},methods:{}};e.default=a},faf8:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.custom-image[data-v-07bfc4b2]{position:relative;display:block;width:100%;height:100%}.custom-image.image-round[data-v-07bfc4b2]{overflow:hidden;border-radius:50%}.custom-image .image[data-v-07bfc4b2]{display:block;width:100%;height:100%}.custom-image .loading-wrap[data-v-07bfc4b2],\n.custom-image .error-wrap[data-v-07bfc4b2]{position:absolute;top:0;left:0;display:flex;flex-direction:column;align-items:center;justify-content:center;color:#969799;font-size:%?28?%;background-color:#f7f8fa}',""]),t.exports=e},fbc3:function(t,e,i){var a=i("faf8");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("f8b03a56",a,!0,{sourceMap:!1,shadowMode:!1})},ff077:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={customImage:i("33fd").default,loadingView:i("200a").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"user-vip"},[i("v-uni-view",{staticClass:"header"},[i("v-uni-view",{staticClass:"user-vip-info row"},[i("custom-image",{attrs:{width:"110rpx",height:"110rpx",round:!0,src:t.userInfo.avatar}}),i("v-uni-view",{staticClass:"ml20 column"},[i("v-uni-view",{staticClass:"user-text white xxl row"},[t._v(t._s(t.userInfo.nickname))]),i("v-uni-view",{staticClass:"user-level white xs row-center"},[t._v("当前等级："+t._s(t.userInfo.level_name||"无"))])],1)],1)],1),i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{staticClass:"vip-swiper-container"},[i("v-uni-swiper",{staticClass:"swiper",staticStyle:{height:"320rpx"},attrs:{"previous-margin":"60rpx","next-margin":"60rpx","display-multiple-items":"1",current:t.currentIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.bindchange.apply(void 0,arguments)}}},t._l(t.levelList,(function(e,a){return i("v-uni-swiper-item",{key:a},[i("v-uni-view",{staticClass:"vip-card-item",style:"background-image: url("+e.background_image+");"},[i("v-uni-view",{staticClass:"row-between"},[i("v-uni-view",[1==e.current_level_status?i("v-uni-view",{staticClass:"row grade white sm"},[t._v("当前等级")]):-1==e.current_level_status?i("v-uni-view",{staticClass:"row white sm ml20"},[t._v("未解锁")]):0==e.current_level_status?i("v-uni-view",{staticClass:"row white sm ml20"},[t._v("已解锁")]):t._e()],1),i("v-uni-image",{staticClass:"grade-icon",attrs:{src:e.image}})],1),i("v-uni-view",{staticClass:"row-between vip-name white"},[i("v-uni-view",{staticClass:"bold"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"sm"},[t._v(t._s(e.next_level))])],1),e.diff_growth_percent?i("v-uni-view",{staticClass:"row-center"},[i("v-uni-view",{staticClass:"vip-progress bg-white row"},[i("v-uni-view",{staticClass:"vip-progress-bar",style:"width: "+100*e.diff_growth_percent+"%"})],1)],1):t._e(),i("v-uni-view",{staticClass:"row-between mt20",staticStyle:{padding:"0 30rpx"}},[i("v-uni-navigator",{staticClass:"row",attrs:{"hover-class":"none"}},[i("v-uni-view",{staticClass:"sm white",staticStyle:{"line-height":"36rpx"}},[t._v(t._s(0==e.current_level_status?"当前高于该等级":e.current_growth_tips))])],1),i("v-uni-view",{staticClass:"white"},[t._v(t._s(e.diff_growth_tips))])],1)],1)],1)})),1)],1),i("v-uni-view",{staticClass:"vip-grade-rule"},[i("v-uni-view",{staticClass:"title row"},[i("v-uni-view",{staticClass:"line br60"}),i("v-uni-view",{staticClass:"xl ml20 bold"},[t._v("成长值规则")])],1),i("v-uni-text",{staticClass:"rule-content column lighter ml20"},[t._v(t._s(t.growthRule))])],1),i("v-uni-view",{staticClass:"vip-rights"},[i("v-uni-view",{staticClass:"title row"},[i("v-uni-view",{staticClass:"line br60"}),i("v-uni-view",{staticClass:"xl ml20 bold"},[t._v("会员权益")])],1),i("v-uni-view",{staticClass:"rights-list row"},t._l(t.privilegeList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"rights-item column-center"},[i("v-uni-image",{staticClass:"mb10",attrs:{src:e.image}}),i("v-uni-view",{staticClass:"sm"},[t._v(t._s(e.name))])],1)})),1)],1)],1)],1),t.userInfo.nickname?t._e():i("loading-view")],1)},r=[]}}]);