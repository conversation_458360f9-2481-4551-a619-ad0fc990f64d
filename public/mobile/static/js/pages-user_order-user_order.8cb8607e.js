(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user_order-user_order","bundle-pages-goods_reviews-goods_reviews~bundle-pages-model-model1_group~bundle-pages-user_group-use~09b5339f"],{"0a7a":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={navbar:i("17e4").default,tabs:i("cfa3").default,tab:i("74fc").default,orderList:i("7d13").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"user-order"},[i("navbar",{attrs:{title:"我的订单"}}),i("tabs",{attrs:{active:t.active,config:{itemWidth:150}},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeShow.apply(void 0,arguments)}}},t._l(t.order,(function(e,n){return i("tab",{key:n,attrs:{title:e.name,name:e.type}},[e.isShow?i("order-list",{ref:"order"+e.type,refInFor:!0,attrs:{"order-type":e.type}}):t._e()],1)})),1)],1)},o=[]},"0b2f":function(t,e,i){"use strict";i.r(e);var n=i("252d"),r=i("9202");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"37d6ead7",null,!1,n["a"],void 0);e["default"]=s.exports},"0dbf":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n={data:function(){return{}},components:{},props:{list:{type:Array,default:function(){return[]}},link:{type:Boolean,default:!1},team:{type:[Object,Array],default:function(){return{}}},delivery:{type:Number,default:1},mode:{type:String,default:"order"},order_type:{type:Number,default:0},isModel:{type:Boolean,default:!1}},methods:{toGoods:function(t){if(this.link){var e="/pages/goods_details/goods_details?id=".concat(t);this.isModel&&(e="/pages/goods_details/model_details?id=".concat(t)),uni.navigateTo({url:e})}}}};e.default=n},"0de7":function(t,e,i){"use strict";var n=i("d00a"),r=i.n(n);r.a},"0fec":function(t,e,i){var n=i("4bd2");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("967d").default;r("1ec3ae33",n,!0,{sourceMap:!1,shadowMode:!1})},1569:function(t,e,i){"use strict";i.r(e);var n=i("e62a"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"171e":function(t,e,i){"use strict";(function(t){i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bd06");var n=i("1782"),r={data:function(){return{active:n.orderType.ALL,order:[{name:"全部",type:n.orderType.ALL,isShow:!1},{name:"待付款",type:n.orderType.PAY,isShow:!1},{name:"待收货",type:n.orderType.DELIVERY,isShow:!1},{name:"已完成",type:n.orderType.FINISH,isShow:!1},{name:"已关闭",type:n.orderType.CLOSE,isShow:!1}]}},components:{},props:{},onLoad:function(t){var e=this.order,i=t.type||n.orderType.ALL,r=e.findIndex((function(t){return t.type==i}));this.changeShow(r)},onPullDownRefresh:function(){var e=this.active,i=this.order;t.log(this.$refs["order"+i[e].type]),this.$refs["order"+i[e].type][0].reflesh()},onReachBottom:function(){var e=this.active,i=this.order;t.log(this.$refs["order"+i[e].type]),this.$refs["order"+i[e].type][0].getOrderListFun()},methods:{changeShow:function(t){-1!=t&&(this.active=t,this.order[t].isShow=!0)}}};e.default=r}).call(this,i("ba7c")["default"])},"17e4":function(t,e,i){"use strict";i.r(e);var n=i("ea82"),r=i("91b6");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("fb92");var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"05668d7e",null,!1,n["a"],void 0);e["default"]=s.exports},1976:function(t,e,i){"use strict";i.r(e);var n=i("749b"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"1f01":function(t,e,i){var n=i("c73d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("967d").default;r("0f94b586",n,!0,{sourceMap:!1,shadowMode:!1})},"1fd4":function(t,e,i){var n=i("78df");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("967d").default;r("906232cc",n,!0,{sourceMap:!1,shadowMode:!1})},"200a":function(t,e,i){"use strict";i.r(e);var n=i("5229"),r=i("b7e4");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("a3d8");var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"02ab9a7e",null,!1,n["a"],void 0);e["default"]=s.exports},2106:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-countdown"},[t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?i("v-uni-view",{style:{fontSize:t.fontSize+"rpx"}},[t._v(t._s(t.d))]):t._e(),t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?i("v-uni-view",{style:{fontSize:t.separatorSize+"rpx","margin-right":"6rpx"}},[t._v("天")]):t._e(),t.showHours?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.h))])],1):t._e(),t.showHours?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"时"))]):t._e(),t.showMinutes?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.i))])],1):t._e(),t.showMinutes?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"分"))]):t._e(),t.showSeconds?i("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[i("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.s))])],1):t._e(),t.showSeconds&&"zh"==t.separator?i("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v("秒")]):t._e()],1)},r=[]},"252d":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uModal:i("db13").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-modal",{attrs:{"show-cancel-button":!0,content:t.getTipsText,"confirm-color":"#ff2c3c"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}})},o=[]},"303a":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */._tab-box[data-v-6cfd186a]{width:100%;font-size:%?26?%;position:relative;z-index:10}._tab-box .scroll-view-h[data-v-6cfd186a]{height:%?80?%;line-height:%?80?%;white-space:nowrap;width:100%;box-sizing:border-box}._tab-box .scroll-view-h ._scroll-content[data-v-6cfd186a]{width:100%;height:100%;position:relative;display:inline-block}._tab-box .scroll-view-h ._scroll-content ._tab-item-box[data-v-6cfd186a]{height:100%;display:inline-block}._tab-box .scroll-view-h ._scroll-content ._tab-item-box._flex[data-v-6cfd186a]{display:flex}._tab-box .scroll-view-h ._scroll-content ._tab-item-box._flex ._item[data-v-6cfd186a]{flex:1;padding:0 %?20?%}._tab-box .scroll-view-h ._scroll-content ._tab-item-box._clamp ._item[data-v-6cfd186a]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}._tab-box .scroll-view-h ._scroll-content ._tab-item-box ._item[data-v-6cfd186a]{height:100%;display:inline-block;text-align:center;position:relative;text-align:center;color:#333}._tab-box .scroll-view-h ._scroll-content ._tab-item-box ._item._active[data-v-6cfd186a]{color:#e54d42}._tab-box .scroll-view-h ._scroll-content ._underline[data-v-6cfd186a]{height:%?4?%;background-color:#e54d42;border-radius:%?6?%;transition:-webkit-transform .5s;transition:transform .5s;transition:transform .5s,-webkit-transform .5s;position:absolute;bottom:0}',""]),t.exports=e},"324f":function(t,e,i){"use strict";i.r(e);var n=i("44ea"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"3c5b":function(t,e,i){"use strict";i.r(e);var n=i("0a7a"),r=i("7a96");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"774d04d4",null,!1,n["a"],void 0);e["default"]=s.exports},"3ea3":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(i("2634")),o=n(i("2fdc"));i("64aa");var a=i("416e"),s={props:{type:Number,orderId:[Number,String]},data:function(){return{show:!1}},methods:{open:function(){this.show=!0},close:function(){this.show=!1},onConfirm:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var i,n,o;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=t.type,n=t.orderId,o=null,e.t0=i,e.next=0===e.t0?5:1===e.t0?9:2===e.t0?13:17;break;case 5:return e.next=7,(0,a.cancelOrder)(n);case 7:return o=e.sent,e.abrupt("break",17);case 9:return e.next=11,(0,a.delOrder)(n);case 11:return o=e.sent,e.abrupt("break",17);case 13:return e.next=15,(0,a.confirmOrder)(n);case 15:return o=e.sent,e.abrupt("break",17);case 17:1==o.code&&(t.close(),t.$emit("refresh"),t.$toast({title:o.msg}));case 18:case"end":return e.stop()}}),e)})))()}},computed:{getTipsText:function(){var t=this.type;switch(t){case 0:return"确认取消订单吗？";case 1:return"确认删除订单吗?";case 2:return"确认收货吗?"}}}};e.default=s},"416e":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.cancelOrder=function(t){return r.default.post("order/cancel",{id:t})},e.confirmOrder=function(t){return r.default.post("order/confirm",{id:t})},e.delOrder=function(t){return r.default.post("order/del",{id:t})},e.getDelivery=function(){return r.default.get("order/getDeliveryType")},e.getOrderCoupon=function(t){return r.default.post("coupon/orderCoupon",t)},e.getOrderDetail=function(t,e){return r.default.get("order/detail",{params:{id:t,follow_id:e}})},e.getOrderList=function(t){return r.default.get("order/lists",{params:t})},e.getVerifyLists=function(t){return r.default.get("order/verificationLists",{params:t})},e.getwechatSyncCheck=function(t){return r.default.get("order/wechatSyncCheck",{params:t})},e.getwxReceiveDetail=function(t){return r.default.get("order/wxReceiveDetail",{params:t})},e.orderBuy=function(t){return r.default.post("order/buy",t)},e.orderTraces=function(t){return r.default.get("order/orderTraces",{params:{id:t}})},e.verification=function(t){return r.default.post("order/verification",t)},e.verificationConfirm=function(t){return r.default.post("order/verificationConfirm",t)};var r=n(i("35af"));i("73e7")},"44ea":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n={name:"u-tag",props:{type:{type:String,default:"primary"},disabled:{type:[Boolean,String],default:!1},size:{type:String,default:"default"},shape:{type:String,default:"square"},text:{type:[String,Number],default:""},bgColor:{type:String,default:""},color:{type:String,default:""},borderColor:{type:String,default:""},closeColor:{type:String,default:""},index:{type:[Number,String],default:""},mode:{type:String,default:"light"},closeable:{type:Boolean,default:!1},show:{type:Boolean,default:!0}},data:function(){return{}},computed:{customStyle:function(){var t={};return this.color&&(t.color=this.color),this.bgColor&&(t.backgroundColor=this.bgColor),"plain"==this.mode&&this.color&&!this.borderColor?t.borderColor=this.color:t.borderColor=this.borderColor,t},iconStyle:function(){if(this.closeable){var t={};return"mini"==this.size?t.fontSize="20rpx":t.fontSize="22rpx","plain"==this.mode||"light"==this.mode?t.color=this.type:"dark"==this.mode&&(t.color="#ffffff"),this.closeColor&&(t.color=this.closeColor),t}},closeIconColor:function(){return this.closeColor?this.closeColor:this.color?this.color:"dark"==this.mode?"#ffffff":this.type}},methods:{clickTag:function(){this.disabled||this.$emit("click",this.index)},close:function(){this.$emit("close",this.index)}}};e.default=n},"4bd2":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.navbar .navbar-left[data-v-05668d7e]{display:flex;padding:%?12?% %?25?%;border-radius:30px;background:hsla(0,0%,100%,.3);border:%?1?% solid rgba(0,0,0,.1)}.navbar .navbar-left .line[data-v-05668d7e]{width:1px;height:%?36?%;background:rgba(0,0,0,.2);margin:0 %?25?%}.navbar .navbar-left .navbar-lists[data-v-05668d7e]{display:flex;justify-content:center;position:relative}.navbar .navbar-left .navbar-lists .navbar-float[data-v-05668d7e]{position:absolute;top:40px;width:%?258?%;padding:0 %?24?%;background:#fff;border-radius:%?14?%;box-shadow:0 3px 6px rgba(0,0,0,.06)}.navbar .navbar-left .navbar-lists .navbar-float[data-v-05668d7e]::before{content:"";display:block;position:absolute;left:50%;width:0;height:0;border:%?14?% solid transparent;border-bottom-color:#fff;-webkit-transform:translate(-50%,-100%);transform:translate(-50%,-100%)}.navbar .navbar-left .navbar-lists .navbar-float .float-item[data-v-05668d7e]{padding:%?20?% 0;display:flex;align-items:center}.navbar .navbar-left .navbar-lists .navbar-float .float-item[data-v-05668d7e]:not(:last-of-type){border-bottom:1px solid #e5e5e5}.navbar .mask[data-v-05668d7e]{position:fixed;top:0;left:0;width:100%;height:100%;z-index:1}',""]),t.exports=e},"4f20":function(t,e,i){var n=i("fdc0");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("967d").default;r("3859ced6",n,!0,{sourceMap:!1,shadowMode:!1})},"500a":function(t,e,i){"use strict";var n=i("9991"),r=i.n(n);r.a},5229:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={loading:i("34d8").default},r=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:"loading "+("flex"==this.type?"flex":""),style:{backgroundColor:this.backgroundColor}},[e("loading",{attrs:{color:this.color,size:this.size}})],1)},o=[]},5453:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.order-list[data-v-3d18c672]{padding:0 %?20?%;overflow:hidden}.order-list .order-item[data-v-3d18c672]{border-radius:%?10?%}.order-list .order-item .order-header[data-v-3d18c672]{height:%?80?%;padding:0 %?24?%;border-bottom:1px dotted #e5e5e5}.order-list .order-item .all-price[data-v-3d18c672]{text-align:right;padding:0 %?24?% %?20?%}.order-list .order-item .order-footer[data-v-3d18c672]{height:%?100?%;border-top:1px solid #e5e5e5;padding:0 %?24?%}.order-list .order-item .order-footer .plain[data-v-3d18c672]{border:1px solid #bbb}.order-list .order-item .order-footer .plain.red[data-v-3d18c672]{border-color:#ff2c3c}',""]),t.exports=e},"5e27":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-countdown[data-v-36f3fbea]{display:inline-flex;align-items:center}.u-countdown-item[data-v-36f3fbea]{display:flex;flex-direction:row;align-items:center;justify-content:center;padding:%?2?%;border-radius:%?6?%;white-space:nowrap;-webkit-transform:translateZ(0);transform:translateZ(0)}.u-countdown-time[data-v-36f3fbea]{margin:0;padding:0}.u-countdown-colon[data-v-36f3fbea]{display:flex;flex-direction:row;justify-content:center;padding:0 %?5?%;line-height:1;align-items:center;padding-bottom:%?4?%}.u-countdown-scale[data-v-36f3fbea]{-webkit-transform:scale(.9);transform:scale(.9);-webkit-transform-origin:center center;transform-origin:center center}',""]),t.exports=e},"627d":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={customImage:i("33fd").default,uTag:i("7a4b").default,priceFormat:i("8718").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"order-goods bg-white"},t._l(t.list,(function(e,n){return i("v-uni-view",{key:n,staticClass:"item-wrap"},[i("v-uni-view",{staticClass:"item row",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toGoods(e.goods_id)}}},[i("v-uni-view",{staticClass:"goods-img"},[i("custom-image",{attrs:{width:"180rpx",radius:"10rpx",height:"180rpx","lazy-load":!0,src:e.image_str||e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20 flex1"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t.team.need?i("u-tag",{staticClass:"mr10",attrs:{text:t.team.need+"人团",size:"mini",type:"primary",mode:"plain"}}):t._e(),t._v(t._s(e.goods_name||e.name))],1),i("v-uni-view",{staticClass:"goods-spec xs muted mb20"},[t._v(t._s(e.spec_value_str||e.spec_value))]),i("v-uni-view",{staticClass:"row-between"},[i("v-uni-view",{staticClass:"goods-price row"},[i("v-uni-view",{staticClass:"primary"},[e.is_member||0!==t.order_type||e.score?t._e():i("price-format",{attrs:{weight:500,"subscript-size":24,"first-size":34,"second-size":24,price:e.original_price||e.goods_price}}),e.score?i("v-uni-view",{},[t._v(t._s(e.score)+"贡献值")]):t._e()],1),e.is_member&&0===t.order_type?i("v-uni-view",{staticClass:"vip-price row"},[i("v-uni-view",{staticClass:"price-name xxs"},[t._v("会员价")]),i("v-uni-view",{staticStyle:{padding:"0 10rpx"}},[i("price-format",{attrs:{price:e.goods_price,"first-size":22,"second-size":22,"subscript-size":22,weight:500,color:"#7B3200"}})],1)],1):t._e(),1===t.order_type||2===t.order_type||3===t.order_type?i("v-uni-view",{staticClass:"vip-price row"},[i("v-uni-view",{staticClass:"price-name xxs",staticStyle:{"background-color":"#e74346"}},[1===t.order_type?i("v-uni-text",[t._v("秒杀价")]):t._e(),2===t.order_type?i("v-uni-text",[t._v("拼团价")]):t._e(),3===t.order_type?i("v-uni-text",[t._v("砍价")]):t._e()],1),i("v-uni-view",{staticStyle:{padding:"0 10rpx"}},[i("price-format",{attrs:{price:e.goods_price,"first-size":22,"second-size":22,"subscript-size":22,weight:500,color:"#7B3200"}})],1)],1):t._e()],1),i("v-uni-view",{staticClass:"goods-num sm"},[t._v("x"+t._s(e.goods_num))])],1)],1)],1),"comfirm"===t.mode?[1!==t.delivery||e.is_express?t._e():i("v-uni-view",{staticClass:"delivery"},[t._v("该商品不支持快递配送")]),2!==t.delivery||e.is_selffetch?t._e():i("v-uni-view",{staticClass:"delivery"},[t._v("该商品不支持门店自提")])]:t._e(),t.link?i("v-uni-view",{staticClass:"goods-footer row"},[i("v-uni-view",{staticStyle:{flex:"1"}}),e.comment_btn?i("v-uni-navigator",{staticClass:"mr20",attrs:{"hover-class":"none",url:"/bundle/pages/goods_reviews/goods_reviews?id="+e.id}},[i("v-uni-button",{staticClass:"plain br60",attrs:{size:"xs","hover-class":"none"}},[t._v("评价晒图")])],1):t._e(),e.refund_btn?i("v-uni-navigator",{attrs:{"hover-class":"none",url:"/bundle/pages/apply_refund/apply_refund?order_id="+e.order_id+"&item_id="+e.item_id}},[6!=t.order_type?i("v-uni-button",{staticClass:"plain br60",attrs:{size:"xs","hover-class":"none"}},[t._v("申请退款")]):t._e()],1):t._e(),e.after_status_desc?i("v-uni-view",{staticStyle:{color:"orange"}},[t._v(t._s(e.after_status_desc))]):t._e()],1):t._e()],2)})),1)},o=[]},"644c":function(t,e,i){"use strict";(function(t){i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n={name:"u-count-down",props:{timestamp:{type:[Number,String],default:0},autoplay:{type:Boolean,default:!0},separator:{type:String,default:"colon"},separatorSize:{type:[Number,String],default:30},separatorColor:{type:String,default:"#303133"},color:{type:String,default:"#303133"},fontSize:{type:[Number,String],default:30},bgColor:{type:String,default:"#fff"},height:{type:[Number,String],default:"auto"},showBorder:{type:Boolean,default:!1},borderColor:{type:String,default:"#303133"},showSeconds:{type:Boolean,default:!0},showMinutes:{type:Boolean,default:!0},showHours:{type:Boolean,default:!0},showDays:{type:Boolean,default:!0},hideZeroDay:{type:Boolean,default:!1}},watch:{timestamp:function(t,e){this.clearTimer(),this.start()}},data:function(){return{d:"00",h:"00",i:"00",s:"00",timer:null,seconds:0}},computed:{itemStyle:function(){var t={};return this.height&&(t.height=this.height+"rpx",t.width=this.height+"rpx"),this.showBorder&&(t.borderStyle="solid",t.borderColor=this.borderColor,t.borderWidth="1px"),this.bgColor&&(t.backgroundColor=this.bgColor),t},letterStyle:function(){var t={};return this.fontSize&&(t.fontSize=this.fontSize+"rpx"),this.color&&(t.color=this.color),t}},mounted:function(){this.autoplay&&this.timestamp&&this.start()},methods:{start:function(){var t=this;this.clearTimer(),this.timestamp<=0||(this.seconds=Number(this.timestamp),this.formatTime(this.seconds),this.timer=setInterval((function(){if(t.seconds--,t.$emit("change",t.seconds),t.seconds<0)return t.end();t.formatTime(t.seconds)}),1e3))},formatTime:function(t){t<=0&&this.end();var e,i=0,n=0,r=0;i=Math.floor(t/86400),e=Math.floor(t/3600)-24*i;var o=null;o=this.showDays?e:Math.floor(t/3600),n=Math.floor(t/60)-60*e-24*i*60,r=Math.floor(t)-24*i*60*60-60*e*60-60*n,o=o<10?"0"+o:o,n=n<10?"0"+n:n,r=r<10?"0"+r:r,i=i<10?"0"+i:i,this.d=i,this.h=o,this.i=n,this.s=r},end:function(){this.clearTimer(),this.$emit("end",{})},reset:function(){this.clearTimer(),this.seconds=Number(this.timestamp),this.s=this.timestamp,t.log(this.s)},clearTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}},beforeDestroy:function(){clearInterval(this.timer),this.timer=null}};e.default=n}).call(this,i("ba7c")["default"])},"6b39":function(t,e,i){"use strict";var n=i("1f01"),r=i.n(n);r.a},"749b":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={data:function(){return{}},components:{},props:{status:{type:String,default:"loading"},errorText:{type:String,default:"加载失败，点击重新加载"},loadingText:{type:String,default:"加载中..."},finishedText:{type:String,default:"我可是有底线的～"},slotEmpty:{type:Boolean,default:!1},color:{type:String,default:"#666"}},methods:{onRefresh:function(){this.$emit("refresh")}}};e.default=n},"74fc":function(t,e,i){"use strict";i.r(e);var n=i("9f4a"),r=i("8ed1");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("fd7e");var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"469015b5",null,!1,n["a"],void 0);e["default"]=s.exports},"78df":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,".tab.active[data-v-469015b5]{height:auto}.tab.inactive[data-v-469015b5]{height:0;overflow:visible}",""]),t.exports=e},7931:function(t,e,i){"use strict";var n=i("4f20"),r=i.n(n);r.a},"7a4b":function(t,e,i){"use strict";i.r(e);var n=i("baaf"),r=i("324f");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("0de7");var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"1cd62f78",null,!1,n["a"],void 0);e["default"]=s.exports},"7a96":function(t,e,i){"use strict";i.r(e);var n=i("171e"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"7d13":function(t,e,i){"use strict";i.r(e);var n=i("ec37"),r=i("cc4c");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("f435");var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"3d18c672",null,!1,n["a"],void 0);e["default"]=s.exports},"7e19":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-tag[data-v-1cd62f78]{box-sizing:border-box;align-items:center;border-radius:%?6?%;display:inline-block}.u-size-default[data-v-1cd62f78]{font-size:%?22?%;padding:%?6?% %?12?%}.u-size-mini[data-v-1cd62f78]{font-size:%?20?%;padding:%?1?% %?6?%}.u-mode-light-primary[data-v-1cd62f78]{background-color:#ecf5ff;color:#ff2c3c;border:1px solid #a0cfff}.u-mode-light-success[data-v-1cd62f78]{background-color:#dbf1e1;color:#19be6b;border:1px solid #71d5a1}.u-mode-light-error[data-v-1cd62f78]{background-color:#fef0f0;color:#fa3534;border:1px solid #fab6b6}.u-mode-light-warning[data-v-1cd62f78]{background-color:#fdf6ec;color:#f90;border:1px solid #fcbd71}.u-mode-light-info[data-v-1cd62f78]{background-color:#f4f4f5;color:#909399;border:1px solid #c8c9cc}.u-mode-dark-primary[data-v-1cd62f78]{background-color:#ff2c3c;color:#fff}.u-mode-dark-success[data-v-1cd62f78]{background-color:#19be6b;color:#fff}.u-mode-dark-error[data-v-1cd62f78]{background-color:#fa3534;color:#fff}.u-mode-dark-warning[data-v-1cd62f78]{background-color:#f90;color:#fff}.u-mode-dark-info[data-v-1cd62f78]{background-color:#909399;color:#fff}.u-mode-plain-primary[data-v-1cd62f78]{background-color:#fff;color:#ff2c3c;border:1px solid #ff2c3c}.u-mode-plain-success[data-v-1cd62f78]{background-color:#fff;color:#19be6b;border:1px solid #19be6b}.u-mode-plain-error[data-v-1cd62f78]{background-color:#fff;color:#fa3534;border:1px solid #fa3534}.u-mode-plain-warning[data-v-1cd62f78]{background-color:#fff;color:#f90;border:1px solid #f90}.u-mode-plain-info[data-v-1cd62f78]{background-color:#fff;color:#909399;border:1px solid #909399}.u-disabled[data-v-1cd62f78]{opacity:.55}.u-shape-circle[data-v-1cd62f78]{border-radius:%?100?%}.u-shape-circleRight[data-v-1cd62f78]{border-radius:0 %?100?% %?100?% 0}.u-shape-circleLeft[data-v-1cd62f78]{border-radius:%?100?% 0 0 %?100?%}.u-close-icon[data-v-1cd62f78]{margin-left:%?14?%;font-size:%?22?%;color:#19be6b}.u-icon-wrap[data-v-1cd62f78]{display:inline-flex;-webkit-transform:scale(.86);transform:scale(.86)}',""]),t.exports=e},"85cb":function(t,e,i){"use strict";var n=i("9a1f"),r=i.n(n);r.a},"8ed1":function(t,e,i){"use strict";i.r(e);var n=i("e064"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"91b6":function(t,e,i){"use strict";i.r(e);var n=i("9afc"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},9202:function(t,e,i){"use strict";i.r(e);var n=i("3ea3"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},9991:function(t,e,i){var n=i("303a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("967d").default;r("adbff8ba",n,!0,{sourceMap:!1,shadowMode:!1})},"9a1f":function(t,e,i){var n=i("5e27");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("967d").default;r("6327d3fe",n,!0,{sourceMap:!1,shadowMode:!1})},"9afc":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{title:String,titleColor:{type:String,default:"#000000"},background:{type:Object,default:function(){return{background:"#ffffff"}}},borderBottom:{type:Boolean,default:!1},immersive:{type:Boolean,default:!1}},data:function(){return{baseUrl:this.baseUrl,isIndex:!1,navLists:[{url:"/pages/index/index",name:"首页",icon:this.baseUrl+"/image/icon_home.png",type:"switchTab"},{url:"/pages/goods_search/goods_search",name:"搜索",icon:this.baseUrl+"/image/icon_search.png",type:"navigate"},{url:"/pages/shop_cart/shop_cart",name:"购物车",icon:this.baseUrl+"/image/icon_carts.png",type:"switchTab"},{url:"/pages/user/user",name:"个人中心",icon:this.baseUrl+"/image/icon_user.png",type:"switchTab"}],showFloat:!1}},methods:{goBack:function(){this.isIndex?uni.switchTab({url:"/pages/index/index"}):uni.navigateBack()}},computed:{backIcon:function(){var t=this.isIndex?"icon_home":"icon_back";return this.baseUrl+"/image/".concat(t,".png")}},created:function(){var t=this;setTimeout((function(){var e=getCurrentPages();1==e.length&&(t.isIndex=!0)}))}};e.default=n},"9f4a":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:{active:this.active,inactive:!this.active,tab:!0},style:this.shouldShow?"":"display: none;"},[this.shouldRender?this._t("default"):this._e()],2)},r=[]},"9fd1":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={loading:i("34d8").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"loading-footer row-center",style:"color: "+t.color},["loading"===t.status?i("v-uni-view",{staticClass:"loading row"},[i("loading",{staticClass:"mr20",attrs:{color:t.color}}),i("v-uni-text",{style:"color: "+t.color},[t._v(t._s(t.loadingText))])],1):t._e(),"finished"===t.status?i("v-uni-view",{staticClass:"finished"},[t._v(t._s(t.finishedText))]):t._e(),"error"===t.status?i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onRefresh.apply(void 0,arguments)}}},[t._v(t._s(t.errorText))]):t._e(),"empty"===t.status?i("v-uni-view",{staticClass:"empty"},[t.slotEmpty?t._t("empty"):i("v-uni-text",[t._v("暂无数据")])],2):t._e()],1)},o=[]},a3d8:function(t,e,i){"use strict";var n=i("c471"),r=i.n(n);r.a},af5b:function(t,e,i){"use strict";i.r(e);var n=i("644c"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},b7e4:function(t,e,i){"use strict";i.r(e);var n=i("ea18"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},b97e:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,".loading[data-v-02ab9a7e]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;display:flex;justify-content:center;align-items:center}.loading.flex[data-v-02ab9a7e]{position:static;flex:1;width:100%}",""]),t.exports=e},baaf:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uIcon:i("5b98").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.show?i("v-uni-view",{staticClass:"u-tag",class:[t.disabled?"u-disabled":"","u-size-"+t.size,"u-shape-"+t.shape,"u-mode-"+t.mode+"-"+t.type],style:[t.customStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickTag.apply(void 0,arguments)}}},[t._v(t._s(t.text)),i("v-uni-view",{staticClass:"u-icon-wrap",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[t.closeable?i("u-icon",{staticClass:"u-close-icon",style:[t.iconStyle],attrs:{size:"22",color:t.closeIconColor,name:"close"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}}):t._e()],1)],1):t._e()},o=[]},bcab:function(t,e,i){"use strict";i.r(e);var n=i("627d"),r=i("d743");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("7931");var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"4587133f",null,!1,n["a"],void 0);e["default"]=s.exports},be61:function(t,e,i){"use strict";(function(t){i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.alipay=function(e){var i=document.createElement("div");return t.log(e),i.innerHTML=e,document.body.appendChild(i),void document.forms[0].submit()},e.wxpay=function(e){if((0,o.isWeixinClient)())return r.default.wxPay(e);t.log(e),location.href=e};var r=n(i("efe5f")),o=i("73e7")}).call(this,i("ba7c")["default"])},c471:function(t,e,i){var n=i("b97e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("967d").default;r("73347308",n,!0,{sourceMap:!1,shadowMode:!1})},c73d:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,".loading-footer[data-v-6dc30303]{padding:%?30?% 0;color:#666}",""]),t.exports=e},cc4c:function(t,e,i){"use strict";i.r(e);var n=i("d4ef"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},ce025:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"_tab-box",style:{fontSize:t.defaultConfig.fontSize+"rpx",color:t.defaultConfig.color}},[i("v-uni-scroll-view",{staticClass:"scroll-view-h",style:{backgroundColor:t.defaultConfig.bgColor},attrs:{id:"_scroll","scroll-x":!0,"scroll-with-animation":!0,"scroll-left":t.slider.scrollLeft}},[i("v-uni-view",{staticClass:"_scroll-content"},[i("v-uni-view",{staticClass:"_tab-item-box",class:[t.defaultConfig.itemWidth?"_clamp":"_flex"]},[t._l(t.tabList,(function(e,n){return[i("v-uni-view",{key:n+"_0",staticClass:"_item",class:{_active:t.tagIndex===n},style:{color:t.tagIndex==n?t.defaultConfig.activeColor:t.defaultConfig.color,width:t.defaultConfig.itemWidth?t.defaultConfig.itemWidth+"rpx":""},attrs:{id:"_tab_"+n},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.tabClick(n)}}},[t._v(t._s(e.title))])]}))],2),i("v-uni-view",{staticClass:"_underline",style:{transform:"translateX("+t.slider.left+"px)",width:t.slider.width+"px",height:t.defaultConfig.underLineHeight+"rpx",backgroundColor:t.defaultConfig.underLineColor}})],1)],1),i("v-uni-view",{staticClass:"tab-content"},[i("v-uni-view",[t._t("default")],2)],1)],1)},r=[]},cfa3:function(t,e,i){"use strict";i.r(e);var n=i("ce025"),r=i("1569");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("500a");var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"6cfd186a",null,!1,n["a"],void 0);e["default"]=s.exports},d00a:function(t,e,i){var n=i("7e19");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("967d").default;r("f9da873c",n,!0,{sourceMap:!1,shadowMode:!1})},d4ef:function(t,e,i){"use strict";(function(t){i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(i("2634")),o=n(i("2fdc"));i("bf0f"),i("c223"),i("2797");var a=i("416e"),s=i("73e7"),d=(i("d8e1"),i("1782")),c=(i("be61"),{data:function(){return{page:1,orderList:[],status:d.loadingType.LOADING,showCancel:!1,type:0,orderId:"",showLoading:!1,pay_way:""}},components:{},props:{orderType:{type:String}},created:function(){var t=this;uni.$on("refreshorder",(function(){t.reflesh()})),uni.$on("payment",(function(e){e.result&&(t.reflesh(),uni.navigateBack(),setTimeout((function(){return t.$toast({title:"支付成功"})}),500))}))},beforeMount:function(){this.getOrderListFun()},destroyed:function(){uni.$off(["payment","refreshorder"])},methods:{reflesh:function(){this.page=1,this.orderList=[],this.status=d.loadingType.LOADING,this.type=0,this.getOrderListFun()},reload:function(){this.status=d.loadingType.LOADING,this.getOrderListFun()},orderDialog:function(){this.$refs.orderDialog.open()},delOrder:function(t){var e=this;this.orderId=t,this.type=1,this.$nextTick((function(){e.orderDialog()}))},comfirmReceive:function(t){return new Promise((function(e,i){wx.openBusinessView({businessType:"weappOrderConfirm",extraData:{transaction_id:t},success:function(t){var i=t.extraData;"success"==i.status?e("确认收货"):e("取消收货")},fail:function(t){i(t)}})}))},querycomfirmReceive:function(t){return new Promise((function(e,i){(0,a.getwechatSyncCheck)({id:t}).then((function(t){var n=t.data;4===n.order.order_state?e("已确认收货"):i("未确认收货")})).catch((function(t){i(t)}))}))},comfirmOrder:function(t,e){var i=this;this.orderId=t,this.pay_way=e,this.type=2,this.$nextTick((0,o.default)((0,r.default)().mark((function t(){return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:i.orderDialog();case 1:case"end":return t.stop()}}),t)}))))},cancelOrder:function(t){var e=this;this.orderId=t,this.type=0,this.$nextTick((function(){e.orderDialog()}))},payNow:function(t,e){var i="/pages/payment/payment?from=".concat("order","&order_id=",t);6==e&&(i="/pages/payment/payment?from=".concat("order","&order_id=",t,"&order_type=").concat(e)),uni.navigateTo({url:i})},getOrderListFun:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var i,n,o,d,c;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i=t.page,n=t.orderType,o=t.orderList,d=t.status,e.next=3,(0,s.loadingFun)(a.getOrderList,i,o,d,{type:n});case 3:if(c=e.sent,c){e.next=6;break}return e.abrupt("return");case 6:t.page=c.page,t.orderList=c.dataList,t.status=c.status;case 9:case"end":return e.stop()}}),e)})))()},goPage:function(t){uni.navigateTo({url:t})},goodCount:function(e){t.log(e);var i=0;return e.forEach((function(t){i+=t.goods_num})),i}},computed:{getOrderStatus:function(){return function(t){var e="";switch(t){case 0:e="待支付";break;case 1:e="待发货";break;case 2:e="待收货";break;case 3:e="已完成";break;case 4:e="订单已关闭";break}return e}},getCancelTime:function(){return function(t){return t-Date.now()/1e3}}}});e.default=c}).call(this,i("ba7c")["default"])},d743:function(t,e,i){"use strict";i.r(e);var n=i("0dbf"),r=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},d77c:function(t,e,i){"use strict";i.r(e);var n=i("9fd1"),r=i("1976");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("6b39");var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"6dc30303",null,!1,n["a"],void 0);e["default"]=s.exports},dc89f:function(t,e,i){"use strict";i.r(e);var n=i("2106"),r=i("af5b");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("85cb");var a=i("828b"),s=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,"36f3fbea",null,!1,n["a"],void 0);e["default"]=s.exports},e064:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("aa9c");var n={props:{dot:{type:Boolean},info:{type:null},title:{type:String},titleStyle:{type:String},name:{type:[Number,String],value:""}},inject:["tabs"],data:function(){return{active:!1,shouldShow:!1,shouldRender:!1}},created:function(){this.tabs.childrens.push(this)},mounted:function(){this.update()},methods:{getComputedName:function(){return""!==this.data.name?this.data.name:this.index},updateRender:function(t,e){this.inited=this.inited||t,this.active=t,this.shouldRender=this.inited,this.shouldShow=t},update:function(){this.tabs&&this.tabs.updateTabs()}},computed:{changeData:function(){var t=this.dot,e=this.info,i=this.title,n=this.titleStyle;return{dot:t,info:e,title:i,titleStyle:n}}},watch:{changeData:function(t){this.update()}}};e.default=n},e62a:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("fd3c"),i("5c47"),i("bf0f"),i("2797");var n={name:"tabs",props:{active:{type:Number,default:0},config:{type:Object,default:function(){return{}}}},provide:function(){return{tabs:this}},data:function(){return{tabList:[],tagIndex:0,slider:{left:0,width:0,scrollLeft:0},scorll:{},defaultConfig:{bgColor:"#fff",fontSize:26,color:"#333",activeColor:"#FF2C3C",itemWidth:0,underLinePadding:10,underLineWidth:0,underLineHeight:4,underLineColor:"#FF2C3C"}}},watch:{},created:function(){this.childrens=[]},mounted:function(){this.updateTabs()},methods:{updateTabs:function(){var t=this;this.tabList=this.childrens.map((function(t){var e=t.title,i=t.info,n=t.name,r=t.dot,o=t.titleStyle,a=t.active,s=t.updateRender;return{title:e,info:i,name:n,dot:r,titleStyle:o,active:a,updateRender:s}})),this.updateConfig(),this.tagIndex=this.active,this.$nextTick((function(){t.calcScrollPosition()}))},updateConfig:function(){this.defaultConfig=Object.assign(this.defaultConfig,this.config)},calcScrollPosition:function(){var t=this,e=uni.createSelectorQuery().in(this);e.select("#_scroll").boundingClientRect((function(e){t.scorll=e,t.updateTabWidth()})).exec()},updateTabWidth:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,i=this.tabList;if(0==i.length)return!1;var n=uni.createSelectorQuery().in(this);n.select("#_tab_"+e).boundingClientRect((function(n){i[e]._slider={width:n.width,left:n.left,scrollLeft:n.left-(i[e-1]?i[e-1]._slider.width:0)},t.tagIndex==e&&t.tabToIndex(t.tagIndex),e++,i.length>e&&t.updateTabWidth(e)})).exec()},tabToIndex:function(t){var e=this,i=this.tabList[t]._slider,n=uni.upx2px(this.defaultConfig.underLineWidth);n||(n=this.defaultConfig.itemWidth?uni.upx2px(this.defaultConfig.itemWidth)/3:this.tabList[t]["title"].length*uni.upx2px(this.defaultConfig.fontSize),n+=2*uni.upx2px(this.defaultConfig.underLinePadding)),this.childrens.forEach((function(i,n){var r=n===t;r===i.active&&i.inited||i.updateRender(r,e)}));var r=this.scorll.left||0;this.slider={left:i.left-r+(i.width-n)/2,width:n,scrollLeft:i.scrollLeft-r}},tabClick:function(t){this.tagIndex=t,this.tabToIndex(t),this.$emit("change",t)}}};e.default=n},ea18:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n={data:function(){return{}},props:{type:{type:String,default:"fixed"},backgroundColor:{type:String,default:"#fff"},color:{type:String},size:{type:Number,default:40}},methods:{}};e.default=n},ea82:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uNavbar:i("3558").default,uIcon:i("5b98").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"navbar",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.showFloat=!1}}},[i("u-navbar",{attrs:{background:t.background,title:t.title,"title-color":t.titleColor,"border-bottom":t.borderBottom,immersive:t.immersive,"title-bold":!0,"is-back":!1}},[i("v-uni-view",{staticClass:"navbar-left",attrs:{slot:"left"},slot:"left"},[i("u-icon",{attrs:{name:t.backIcon,size:36},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"line"}),i("v-uni-view",{staticClass:"navbar-lists",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.showFloat=!t.showFloat}}},[i("u-icon",{attrs:{name:t.baseUrl+"/image/icon_list.png",size:32}}),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showFloat,expression:"showFloat"}],staticClass:"navbar-float"},t._l(t.navLists,(function(e,n){return i("v-uni-navigator",{key:n,staticClass:"float-item",attrs:{url:e.url,"open-type":e.type,"hover-class":"none"}},[i("u-icon",{attrs:{name:e.icon,size:44}}),i("v-uni-text",{staticClass:"ml20"},[t._v(t._s(e.name))])],1)})),1)],1)],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showFloat,expression:"showFloat"}],staticClass:"mask",on:{touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.showFloat=!1}}})],1)},o=[]},ec37:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uTag:i("7a4b").default,orderGoods:i("bcab").default,priceFormat:i("8718").default,uCountDown:i("dc89f").default,loadingFooter:i("d77c").default,orderDialog:i("0b2f").default,loadingView:i("200a").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"order-list"},[t._l(t.orderList,(function(e,n){return i("v-uni-navigator",{key:n,staticClass:"order-item bg-white mt20",attrs:{"hover-class":"none",url:"/pages/order_details/order_details?id="+e.id}},[i("v-uni-view",{staticClass:"order-header row-between"},[i("v-uni-view",{staticClass:"row"},[2==e.delivery_type?i("v-uni-view",{staticClass:"mr10"},[i("u-tag",{attrs:{text:"自提",size:"mini",type:"primary",mode:"dark","bg-color":"#0cc21e"}})],1):t._e(),1==e.order_type?i("v-uni-view",{staticClass:"mr10"},[i("u-tag",{attrs:{text:"秒杀",size:"mini",type:"primary",mode:"plain"}})],1):t._e(),2==e.order_type?i("v-uni-view",{staticClass:"mr10"},[i("u-tag",{attrs:{text:"拼团",size:"mini",type:"primary",mode:"plain"}})],1):t._e(),3==e.order_type?i("v-uni-view",{staticClass:"mr10"},[i("u-tag",{attrs:{text:"砍价",size:"mini",type:"primary",mode:"plain"}})],1):t._e(),t._v("订单编号："+t._s(e.order_sn))],1),i("v-uni-view",{class:4==e.order_status?"muted":"primary"},[t._v(t._s(e.order_status_desc))])],1),i("v-uni-view",{staticClass:"order-con"},[i("order-goods",{attrs:{list:e.order_goods,order_type:e.order_type}}),i("v-uni-view",{staticClass:"all-price row-end"},[6!=e.order_type?i("v-uni-text",{staticClass:"muted xs"},[t._v("共"+t._s(t.goodCount(e.order_goods))+"件商品，总金额：")]):t._e(),6==e.order_type?i("v-uni-text",{staticClass:"muted xs"},[t._v("共"+t._s(t.goodCount(e.order_goods))+"件商品，总贡献值：")]):t._e(),6==e.order_type?i("v-uni-view",{staticClass:"primary",staticStyle:{"font-size":"30rpx"}},[t._v(t._s(e.order_amount))]):t._e(),6!=e.order_type?i("price-format",{attrs:{"subscript-size":30,"first-size":30,"second-size":30,price:e.order_amount}}):t._e()],1)],1),e.pickup_btn||e.cancel_btn||e.delivery_btn||e.take_btn||e.del_btn||e.pay_btn||e.comment_btn?i("v-uni-view",{staticClass:"order-footer row"},[i("v-uni-view",{staticStyle:{flex:"1"}},[t.getCancelTime(e.order_cancel_time)>0?i("v-uni-view",{staticClass:"primary sm row",staticStyle:{"line-height":"26rpx"}},[i("u-count-down",{attrs:{separator:"zh",timestamp:t.getCancelTime(e.order_cancel_time),"separator-color":"#FF2C3C",color:"#FF2C3C","separator-size":26,"font-size":26,"bg-color":"transparent"},on:{end:function(e){arguments[0]=e=t.$handleEvent(e),t.reflesh.apply(void 0,arguments)}}})],1):t._e()],1),e.cancel_btn?i("v-uni-view",[i("v-uni-button",{staticClass:"plain br60 lighter",attrs:{size:"sm","hover-class":"none"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.cancelOrder(e.id)}}},[t._v("取消订单")])],1):t._e(),e.delivery_btn?i("v-uni-view",{on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.goPage("/bundle/pages/goods_logistics/goods_logistics?id="+e.id)}}},[i("v-uni-button",{staticClass:"btn plain br60 lighter",attrs:{size:"sm","hover-class":"none"}},[t._v("查看物流")])],1):t._e(),e.del_btn?i("v-uni-view",[i("v-uni-button",{staticClass:"btn plain br60 lighter",attrs:{size:"sm","hover-class":"none"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.delOrder(e.id)}}},[t._v("删除订单")])],1):t._e(),e.pay_btn?i("v-uni-view",{staticClass:"ml20"},[i("v-uni-button",{staticClass:"btn bg-primary br60 white",attrs:{size:"sm"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.payNow(e.id,e.order_type)}}},[t._v("立即付款")])],1):t._e(),e.comment_btn?i("v-uni-view",{staticClass:"ml20"},[i("v-uni-button",{staticClass:"btn plain btn br60 primary red",attrs:{size:"sm","hover-class":"none"}},[t._v("去评价")])],1):t._e(),e.pickup_btn?i("v-uni-view",{staticClass:"ml20"},[i("v-uni-button",{staticClass:"btn plain btn br60 primary red",attrs:{size:"sm","hover-class":"none"}},[t._v("查看提货码")])],1):t._e(),e.take_btn?i("v-uni-view",{staticClass:"ml20"},[i("v-uni-button",{staticClass:"btn plain br60 primary red",attrs:{size:"sm","hover-class":"none"},on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.comfirmOrder(e.id,e.pay_way)}}},[t._v("确认收货")])],1):t._e()],1):t._e()],1)})),i("loading-footer",{attrs:{status:t.status,"slot-empty":!0},on:{refresh:function(e){arguments[0]=e=t.$handleEvent(e),t.reload.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"column-center",staticStyle:{"padding-top":"200rpx"},attrs:{slot:"empty"},slot:"empty"},[i("v-uni-image",{staticClass:"img-null",attrs:{src:"/static/images/goods_null.png"}}),i("v-uni-text",{staticClass:"lighter"},[t._v("暂无订单")])],1)],1)],2),i("order-dialog",{ref:"orderDialog",attrs:{"order-id":t.orderId,type:t.type},on:{refresh:function(e){arguments[0]=e=t.$handleEvent(e),t.reflesh.apply(void 0,arguments)}}}),t.showLoading?i("loading-view",{attrs:{"background-color":"transparent",size:50}}):t._e()],1)},o=[]},eeec:function(t,e,i){var n=i("5453");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("967d").default;r("6fa2ec19",n,!0,{sourceMap:!1,shadowMode:!1})},f435:function(t,e,i){"use strict";var n=i("eeec"),r=i.n(n);r.a},fb92:function(t,e,i){"use strict";var n=i("0fec"),r=i.n(n);r.a},fd7e:function(t,e,i){"use strict";var n=i("1fd4"),r=i.n(n);r.a},fdc0:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.order-goods .item[data-v-4587133f]{padding:%?20?% %?24?%}.order-goods .item .vip-price[data-v-4587133f]{background-color:#ffe9ba;line-height:%?30?%;border-radius:%?6?%;overflow:hidden}.order-goods .item .vip-price .price-name[data-v-4587133f]{background-color:#101010;padding:%?3?% %?10?%;color:#ffd4b7;position:relative;overflow:hidden}.order-goods .item .vip-price .price-name[data-v-4587133f]::after{content:"";display:block;width:%?20?%;height:%?20?%;position:absolute;right:%?-15?%;background-color:#ffe9ba;border-radius:50%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);box-sizing:border-box}.order-goods .goods-footer[data-v-4587133f]{height:%?70?%;align-items:flex-start;padding:0 %?24?%}.order-goods .goods-footer .plain[data-v-4587133f]{border:1px solid #999;height:%?52?%;line-height:%?52?%;font-size:%?26?%}.order-goods .delivery[data-v-4587133f]{display:inline-block;margin-left:%?220?%;padding:%?4?% %?15?%;border-radius:60px;font-size:%?20?%;background-color:#f4f4f4;color:#999}',""]),t.exports=e}}]);