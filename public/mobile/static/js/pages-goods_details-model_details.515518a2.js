(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods_details-model_details","bundle-pages-goods_logistics-goods_logistics~pages-shop_cart-shop_cart"],{"03bb":function(t,e,i){"use strict";i.r(e);var a=i("50b7"),n=i("40bd");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("a1fa");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"6db6c076",null,!1,a["a"],void 0);e["default"]=r.exports},"048c":function(t,e,i){"use strict";var a=i("746c"),n=i.n(a);n.a},"0523":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={goodsList:i("2a9e").default},n=function(){var t=this.$createElement,e=this._self._c||t;return this.goodsList.length?e("v-uni-view",{staticClass:"recommend"},[e("v-uni-view",{staticClass:"goods-title row-center"},[e("v-uni-text",{staticClass:"line"}),e("v-uni-view",{staticClass:"row"},[e("v-uni-image",{staticClass:"mr10",attrs:{src:"/static/images/icon_like.png"}}),e("v-uni-text",{staticClass:"bold xxl"},[this._v("好物优选")])],1),e("v-uni-text",{staticClass:"line"})],1),e("goods-list",{attrs:{list:this.goodsList}})],1):this._e()},s=[]},"0784":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{type:{type:String,default:"double"},list:{type:Array,default:function(){return[]}},isBargain:{type:Boolean,default:!1}},data:function(){return{baseUrl:this.baseUrl}}};e.default=a},"0a6a":function(t,e,i){"use strict";i.r(e);var a=i("67cb"),n=i("cb81");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"db072a46",null,!1,a["a"],void 0);e["default"]=r.exports},"0a77":function(t,e,i){"use strict";var a=i("9200"),n=i.n(a);n.a},"0de7":function(t,e,i){"use strict";var a=i("d00a"),n=i.n(a);n.a},"0fb6":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"u-badge",props:{type:{type:String,default:"error"},size:{type:String,default:"default"},isDot:{type:Boolean,default:!1},count:{type:[Number,String]},overflowCount:{type:Number,default:99},showZero:{type:Boolean,default:!1},offset:{type:Array,default:function(){return[20,20]}},absolute:{type:Boolean,default:!0},fontSize:{type:[String,Number],default:"24"},color:{type:String,default:"#ffffff"},bgColor:{type:String,default:""},isCenter:{type:Boolean,default:!1}},computed:{boxStyle:function(){var t={};return this.isCenter?(t.top=0,t.right=0,t.transform="translateY(-50%) translateX(50%)"):(t.top=this.offset[0]+"rpx",t.right=this.offset[1]+"rpx",t.transform="translateY(0) translateX(0)"),"mini"==this.size&&(t.transform=t.transform+" scale(0.8)"),t},showText:function(){return this.isDot?"":this.count>this.overflowCount?"".concat(this.overflowCount,"+"):this.count},show:function(){return 0!=this.count||0!=this.showZero}}};e.default=a},"0fec":function(t,e,i){var a=i("4bd2");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("1ec3ae33",a,!0,{sourceMap:!1,shadowMode:!1})},"113c":function(t,e,i){"use strict";i.r(e);var a=i("59a0"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},1651:function(t,e,i){"use strict";var a=i("5fe5"),n=i.n(a);n.a},"17e4":function(t,e,i){"use strict";i.r(e);var a=i("ea82"),n=i("91b6");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("fb92");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"05668d7e",null,!1,a["a"],void 0);e["default"]=r.exports},1850:function(t,e,i){"use strict";var a=i("46bc"),n=i.n(a);n.a},"1a67":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,"@-webkit-keyframes _show-data-v-6d8b3c66{0%{opacity:0}100%{opacity:1}}@keyframes _show-data-v-6d8b3c66{0%{opacity:0}100%{opacity:1}}\n\n\n\n",""]),t.exports=e},"210e":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("39d8"));i("64aa"),i("fd3c");var s,o=i("695c"),r=(s={data:function(){return{showTips:[]}},components:{},props:{list:{type:Array,default:function(){return[]}},btnType:{type:Number}},watch:{list:{handler:function(t){var e=t.map((function(t){return 0}));this.showTips=e},immediate:!0,deep:!0}},computed:{}},(0,n.default)(s,"computed",{getBtn:function(){var t="";switch(this.btnType){case 0:t="去使用";break;case 1:t="已使用";break;case 2:t="已过期";break;case 3:t="领取";break}return t}}),(0,n.default)(s,"methods",{onHandle:function(t){this.id=t;var e=this.btnType;switch(e){case 0:uni.switchTab({url:"/pages/index/index"});break;case 1:break;case 2:break;case 3:this.getCouponFun();break}},onShowTips:function(t){var e=this.showTips;this.showTips[t]=e[t]?0:1,this.showTips=Object.assign([],this.showTips)},getCouponFun:function(){var t=this;(0,o.getCoupon)(this.id).then((function(e){1==e.code&&(t.$toast({title:e.msg}),t.$emit("reflash"))}))}}),s);e.default=r},2281:function(t,e,i){"use strict";i.r(e);var a=i("2fdf"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},2544:function(t,e,i){"use strict";i.r(e);var a=i("7efd"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},2870:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uImage:i("4c0e").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"swiper-wrap"},[i("v-uni-swiper",{ref:"swiper",staticClass:"swiper",attrs:{autoplay:t.autoplay,circular:t.circular,interval:t.interval,duration:t.duration},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},[t._l(t.urls,(function(e,a){return[i("v-uni-swiper-item",{key:a+"_0",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewImage(a)}}},["video"==e.type?i("v-uni-view",{staticClass:"video-wrap"},[i("v-uni-video",{staticClass:"my-video",attrs:{id:"myVideo","enable-progress-gesture":!1,controls:t.showControls,"show-progress":!0,"show-fullscreen-btn":t.showControls,src:e.url,"show-center-play-btn":!1,muted:!1,"show-mute-btn":"true","show-play-btn":t.showControls},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.videoErrorCallback.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.play.apply(void 0,arguments)},ended:function(e){arguments[0]=e=t.$handleEvent(e),t.playEnd.apply(void 0,arguments)},fullscreenchange:function(e){arguments[0]=e=t.$handleEvent(e),t.fullscreenchange.apply(void 0,arguments)}}}),i("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:t.showPlay,expression:"showPlay"}],staticClass:"icon-play",attrs:{src:"/static/images/icon_play.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.play.apply(void 0,arguments)}}})],1):i("u-image",{attrs:{width:"750rpx",height:"750rpx",src:e.url,mode:"aspectFill"}})],1)]}))],2),i("v-uni-view",{staticClass:"dots black sm bg-white br60",attrs:{id:"bottom"}},[t._v(t._s(t.currentSwiper+1)+"/"+t._s(t.urls.length))])],1)},s=[]},"2a9e":function(t,e,i){"use strict";i.r(e);var a=i("f159"),n=i("bff5");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("3c79");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"c7e3d2d2",null,!1,a["a"],void 0);e["default"]=r.exports},"2b95":function(t,e,i){"use strict";(function(t){i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("2634")),s=a(i("2fdc"));i("64aa"),i("c223"),i("bf0f");i("8f59");var o=i("d8e1"),r=i("e847"),c=a(i("0a6a")),l={components:{poster:c.default},props:{value:{type:Boolean},shareId:{type:[String,Number],default:""},config:{type:Object,default:function(){return{}}},invite_id:{type:Number,default:0},pagePath:{type:String,default:""},type:{type:[String,Number],default:1}},data:function(){return{poster:"",enablePoster:!1,showPoster:!1,showTips:!1,mnpQrcode:"",bargainShare:{share_title:"",share_intro:""}}},computed:{getLink:function(){var t="".concat(r.baseURL).concat(r.basePath,"/").concat(this.pagePath,"?id=").concat(this.shareId,"&invite_code=").concat(this.$store.getters.inviteCode);return this.invite_id&&(t=t+"&invite_id="+this.invite_id),t},showshare:{get:function(){return this.value},set:function(t){this.$emit("input",t)}}},watch:{showPoster:function(t){t||(this.enablePoster=!1)}},methods:{getPoster:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.isLogin){e.next=2;break}return e.abrupt("return",uni.navigateTo({url:"/pages/login/login"}));case 2:uni.showLoading({title:"正在生成中"}),t.enablePoster=!0;case 4:case"end":return e.stop()}}),e)})))()},getMnpQrcode:function(){var e=this;return new Promise((function(i,a){(0,o.getShareMnQrcode)({id:e.shareId,url:e.pagePath,type:e.type}).then((function(e){t.log("shareRes",e),i(e)})).catch((function(){a()}))}))},handleSuccess:function(t){this.poster=t,uni.hideLoading(),this.showPoster=!0,this.showshare=!1},handleFail:function(){var t=this;uni.hideLoading({success:function(){t.$toast({title:"生成失败"})}})},shareWx:function(){this.showTips=!0,this.showshare=!1},savePoster:function(){var e=this;return(0,s.default)((0,n.default)().mark((function i(){return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:uni.saveImageToPhotosAlbum({filePath:e.poster,success:function(t){e.showPoster=!1,e.$toast({title:"保存成功",icon:"success"})},fail:function(i){e.$toast({title:"保存失败"}),t.log(i)}});case 1:case"end":return i.stop()}}),i)})))()}}};e.default=l}).call(this,i("ba7c")["default"])},"2bb0":function(t,e,i){"use strict";i.r(e);var a=i("7283"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"2cec":function(t,e,i){"use strict";var a=i("8a8f"),n=i.n(a);n.a},"2fdf":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"u-image",props:{src:{type:String,default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!0},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"#f3f4f6"}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:{wrapStyle:function(){var t={};return t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t}},methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=1,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=a},3208:function(t,e,i){"use strict";i.r(e);var a=i("d985"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"324f":function(t,e,i){"use strict";i.r(e);var a=i("44ea"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},3293:function(t,e,i){"use strict";i.r(e);var a=i("d7a8"),n=i("b0d3");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("1850");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"b8cea202",null,!1,a["a"],void 0);e["default"]=r.exports},3435:function(t,e,i){var a=i("cbde");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("b2b3afce",a,!0,{sourceMap:!1,shadowMode:!1})},3497:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".cu-progress[data-v-e51d8c58]{background-color:#e5e5e5;height:%?6?%;width:%?90?%;position:relative}.cu-progress .cu-progress-bar[data-v-e51d8c58]{height:100%;width:%?30?%;position:absolute;left:0}",""]),t.exports=e},3529:function(t,e,i){"use strict";i.r(e);var a=i("5775"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},3832:function(t,e,i){"use strict";i.r(e);var a=i("a325"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"389d":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-badge[data-v-629aeef1]{display:inline-flex;justify-content:center;align-items:center;line-height:%?24?%;padding:%?4?% %?8?%;border-radius:%?100?%;z-index:9}.u-badge--bg--primary[data-v-629aeef1]{background-color:#ff2c3c}.u-badge--bg--error[data-v-629aeef1]{background-color:#fa3534}.u-badge--bg--success[data-v-629aeef1]{background-color:#19be6b}.u-badge--bg--info[data-v-629aeef1]{background-color:#909399}.u-badge--bg--warning[data-v-629aeef1]{background-color:#f90}.u-badge-dot[data-v-629aeef1]{height:%?16?%;width:%?16?%;border-radius:%?100?%;line-height:1}.u-badge-mini[data-v-629aeef1]{-webkit-transform:scale(.8);transform:scale(.8);-webkit-transform-origin:center center;transform-origin:center center}.u-info[data-v-629aeef1]{background-color:#909399;color:#fff}',""]),t.exports=e},"3c79":function(t,e,i){"use strict";var a=i("3435"),n=i.n(a);n.a},"40b4":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uIcon:i("5b98").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-numberbox"},[i("v-uni-view",{staticClass:"u-icon-minus",class:{"u-icon-disabled":t.disabled||t.inputVal<=t.min},style:{background:t.bgColor,height:t.inputHeight+"rpx",color:t.color},on:{touchstart:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.btnTouchStart("minus")},touchend:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.clearTimer.apply(void 0,arguments)}}},[i("u-icon",{attrs:{name:"minus",size:t.size}})],1),i("v-uni-input",{staticClass:"u-number-input",class:{"u-input-disabled":t.disabled},style:{color:t.color,fontSize:t.size+"rpx",background:t.bgColor,height:t.inputHeight+"rpx",width:t.inputWidth+"rpx"},attrs:{disabled:t.disabledInput||t.disabled,"cursor-spacing":t.getCursorSpacing,type:"number"},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.onBlur.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.onFocus.apply(void 0,arguments)}},model:{value:t.inputVal,callback:function(e){t.inputVal=e},expression:"inputVal"}}),i("v-uni-view",{staticClass:"u-icon-plus",class:{"u-icon-disabled":t.disabled||t.inputVal>=t.max},style:{background:t.bgColor,height:t.inputHeight+"rpx",color:t.color},on:{touchstart:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.btnTouchStart("plus")},touchend:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.clearTimer.apply(void 0,arguments)}}},[i("u-icon",{attrs:{name:"plus",size:t.size}})],1)],1)},s=[]},"40bd":function(t,e,i){"use strict";i.r(e);var a=i("c1d1"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"441d":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-details[data-v-6db6c076]{padding-bottom:calc(%?120?% + env(safe-area-inset-bottom))}.goods-details .seckill[data-v-6db6c076]{height:%?100?%;background:#ffd4d8}.goods-details .seckill .price[data-v-6db6c076]{width:%?504?%;height:100%;background:url(https://www.xinjiuhui.cn/image/bg_seckill.png) no-repeat;background-size:100%}.goods-details .seckill .down[data-v-6db6c076]{flex:1}.goods-details .group[data-v-6db6c076]{height:%?100?%;width:100%;background-image:url(https://www.xinjiuhui.cn/image/pintuan_bg.png);background-size:100%}.goods-details .group .group-num[data-v-6db6c076]{border:1px solid #fff;border-radius:%?4?%}.goods-details .group .group-num .group-icon[data-v-6db6c076]{background:#fff;padding:%?3?% %?7?%}.goods-details .group .down[data-v-6db6c076]{height:100%;background-color:#fff5e1;padding:0 %?20?%}.goods-details .goods-info[data-v-6db6c076]{position:relative}.goods-details .goods-info .info-header[data-v-6db6c076]{padding:%?20?% 0 %?0?% %?24?%}.goods-details .goods-info .info-header .price[data-v-6db6c076]{align-items:baseline}.goods-details .goods-info .vip-price[data-v-6db6c076]{margin:0 %?24?%;background-color:#ffe9ba;color:#ffd4b7;line-height:%?36?%;border-radius:%?6?%;overflow:hidden}.goods-details .goods-info .vip-price .price-name[data-v-6db6c076]{background-color:#101010;padding:%?3?% %?12?%;position:relative;overflow:hidden}.goods-details .goods-info .vip-price .price-name[data-v-6db6c076]::after{content:"";display:block;width:%?20?%;height:%?20?%;position:absolute;right:%?-15?%;background-color:#ffe9ba;border-radius:50%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);box-sizing:border-box}.goods-details .goods-info .name[data-v-6db6c076]{padding:%?20?% %?24?%;flex:1}.goods-details .goods-info .icon-share[data-v-6db6c076]{width:%?134?%;height:%?60?%}.goods-details .discount[data-v-6db6c076]{padding:%?24?%}.goods-details .discount .text[data-v-6db6c076]{width:%?100?%;flex:none}.goods-details .discount .con[data-v-6db6c076]{width:%?400?%}.goods-details .discount .coupons-item[data-v-6db6c076]{overflow:hidden}.goods-details .discount .coupons-item > uni-view[data-v-6db6c076]{position:relative;height:%?40?%;line-height:%?40?%;padding:0 %?18?%;border-radius:%?6?%;box-sizing:border-box;background-color:#ff2c3c;color:#fff;white-space:nowrap;overflow:hidden}.goods-details .discount .coupons-item > uni-view[data-v-6db6c076]::after, .goods-details .discount .coupons-item > uni-view[data-v-6db6c076]::before{content:"";display:block;width:%?20?%;height:%?20?%;position:absolute;left:%?-14?%;background-color:#fff;border-radius:50%;border:1px solid currentColor;box-sizing:border-box}.goods-details .discount .coupons-item > uni-view[data-v-6db6c076]::after{right:%?-14?%;left:auto}.goods-details .details-null[data-v-6db6c076]{padding-top:%?140?%;margin-bottom:%?100?%}.goods-details .spec[data-v-6db6c076]{padding:%?24?%}.goods-details .spec .text[data-v-6db6c076]{width:%?100?%}.goods-details .evaluation .title[data-v-6db6c076]{height:%?100?%;border-bottom:1px solid #e5e5e5;padding:0 %?24?%}.goods-details .evaluation .con[data-v-6db6c076]{padding:%?30?% %?24?%}.goods-details .evaluation .user-info .avatar[data-v-6db6c076]{width:%?60?%;height:%?60?%;border-radius:50%}.goods-details .details .title[data-v-6db6c076]{line-height:%?88?%;text-align:center}.goods-details .details > .content[data-v-6db6c076]{padding:0 %?20?% %?20?%;overflow:hidden}.goods-details .details > .content[data-v-6db6c076]  uni-image{vertical-align:middle}.goods-details .details > .content[data-v-6db6c076]  img{vertical-align:middle}.goods-details .footer[data-v-6db6c076]{height:%?100?%;position:fixed;bottom:0;left:0;right:0;box-sizing:initial;padding-bottom:env(safe-area-inset-bottom);z-index:2}.goods-details .footer .btn[data-v-6db6c076]{width:%?100?%;height:%?100?%;position:relative;line-height:1.3}.goods-details .footer .cart-num[data-v-6db6c076]{position:absolute;left:%?60?%;top:%?6?%}.goods-details .footer .add-cart[data-v-6db6c076],\n.goods-details .footer .right-buy[data-v-6db6c076]{flex:1;text-align:center;padding:%?16?% 0}.goods-details .footer .add-cart[data-v-6db6c076]{background-color:#ffa630}.goods-details .footer .right-buy[data-v-6db6c076]{background-color:#ff2c3c}.goods-details .footer .right-buy.bg-gray[data-v-6db6c076]{background-color:#ccc}.goods-details .group-play .title[data-v-6db6c076]{padding:%?20?% %?28?%;border-bottom:1px solid #e5e5e5}.goods-details .group-play .steps[data-v-6db6c076]{padding:%?20?% %?28?%\n  /* 通用节点样式 */\n  /* 各级节点容器样式 */\n  /* 子节点容器 */\n  /* 连接线通用样式 */\n  /* 一级到二级的连接线 */\n  /* 二级到三级的连接线 */\n  /* 三级节点之间的水平连接线 */}.goods-details .group-play .steps .step[data-v-6db6c076]{flex:none}.goods-details .group-play .steps .line[data-v-6db6c076]{flex:1;border:1px dashed #999;margin:0 %?20?%}.goods-details .group-play .steps .number[data-v-6db6c076]{border:%?1?% solid #707070;width:%?28?%;height:%?28?%;border-radius:50%;line-height:%?28?%;text-align:center;margin-right:%?6?%}.goods-details .group-play .steps .container[data-v-6db6c076]{padding:20px}.goods-details .group-play .steps .tree-container[data-v-6db6c076]{width:100%}.goods-details .group-play .steps .tree[data-v-6db6c076]{display:inline-flex;padding:%?40?% 0;min-width:%?1088?%}.goods-details .group-play .steps .node[data-v-6db6c076]{background:#fff;border:%?4?% solid #409eff;border-radius:%?8?%;padding:%?16?% %?32?%;text-align:center;position:relative;z-index:1}.goods-details .group-play .steps .node-level-1[data-v-6db6c076]{display:flex;flex-direction:column;align-items:center;margin:0 %?40?%}.goods-details .group-play .steps .node-level-2[data-v-6db6c076]{display:flex;flex-direction:column;align-items:center;margin:0 %?20?%}.goods-details .group-play .steps .node-level-3[data-v-6db6c076]{margin:%?40?% %?10?% 0}.goods-details .group-play .steps .children-level-2[data-v-6db6c076],\n.goods-details .group-play .steps .children-level-3[data-v-6db6c076]{display:flex;justify-content:center}.goods-details .group-play .steps .line[data-v-6db6c076]{position:absolute;background:#409eff;z-index:0}.goods-details .group-play .steps .line-1-2[data-v-6db6c076]{width:%?4?%;height:%?40?%;bottom:%?-40?%;left:45%;-webkit-transform:translateX(%?-2?%);transform:translateX(%?-2?%)}.goods-details .group-play .steps .line-2-3[data-v-6db6c076]{width:%?4?%;height:%?40?%;bottom:%?-40?%;left:50%;-webkit-transform:translateX(%?-2?%);transform:translateX(%?-2?%)}.goods-details .group-play .steps .line-2-3.top[data-v-6db6c076]{top:%?-40?%;bottom:auto}.goods-details .group-play .steps .node-box[data-v-6db6c076]{position:relative;display:flex;flex-direction:column;align-items:center}.goods-details .group-play .steps .children-level-2[data-v-6db6c076]{position:relative;margin-top:%?80?%}.goods-details .group-play .steps .children-level-2[data-v-6db6c076]::before{content:"";position:absolute;top:%?-40?%;left:10%;right:0;height:%?4?%;width:80%;background:#409eff}.goods-details .group-play .steps .children-level-3[data-v-6db6c076]{position:relative;margin-top:%?40?%}.goods-details .group-play .steps .children-level-3[data-v-6db6c076]::before{content:"";position:absolute;left:10%;right:0;height:%?4?%;width:80%;background:#409eff}.goods-details .group-play .steps .line-3-3[data-v-6db6c076]{width:%?4?%;height:%?40?%;top:%?-40?%;left:40%;-webkit-transform:translateX(%?-2?%);transform:translateX(%?-2?%)}.goods-details .group-list .group-item[data-v-6db6c076]{padding:%?20?% %?24?%}.goods-details .group-list .group-item[data-v-6db6c076]:not(:last-of-type){border-bottom:1px solid #e5e5e5}.goods-details .group-list .group-item .group-btn[data-v-6db6c076]{background:linear-gradient(90deg,#f95f2f,#ff2c3c);height:%?58?%;padding-left:%?28?%;padding-right:%?28?%;margin-left:%?30?%;box-shadow:0 %?6?% %?12?% rgba(249,47,138,.4)}.goods-details .share-money[data-v-6db6c076]{position:fixed;left:%?20?%;bottom:calc(%?130?% + env(safe-area-inset-bottom));-webkit-transform:scale(0);transform:scale(0);transition:all .3s}.goods-details .share-money.show[data-v-6db6c076]{-webkit-transform:scale(1);transform:scale(1)}.goods-details .share-money .share-close[data-v-6db6c076]{width:%?34?%;height:%?34?%;background:#a7a7a7;border-radius:50%}.goods-details .share-money .share-con[data-v-6db6c076]{background:url(https://www.xinjiuhui.cn/image/bg_packet_img.png);width:%?241?%;height:%?208?%;background-size:100%;padding-top:%?20?%;text-align:center}',""]),t.exports=e},"44ea":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"u-tag",props:{type:{type:String,default:"primary"},disabled:{type:[Boolean,String],default:!1},size:{type:String,default:"default"},shape:{type:String,default:"square"},text:{type:[String,Number],default:""},bgColor:{type:String,default:""},color:{type:String,default:""},borderColor:{type:String,default:""},closeColor:{type:String,default:""},index:{type:[Number,String],default:""},mode:{type:String,default:"light"},closeable:{type:Boolean,default:!1},show:{type:Boolean,default:!0}},data:function(){return{}},computed:{customStyle:function(){var t={};return this.color&&(t.color=this.color),this.bgColor&&(t.backgroundColor=this.bgColor),"plain"==this.mode&&this.color&&!this.borderColor?t.borderColor=this.color:t.borderColor=this.borderColor,t},iconStyle:function(){if(this.closeable){var t={};return"mini"==this.size?t.fontSize="20rpx":t.fontSize="22rpx","plain"==this.mode||"light"==this.mode?t.color=this.type:"dark"==this.mode&&(t.color="#ffffff"),this.closeColor&&(t.color=this.closeColor),t}},closeIconColor:function(){return this.closeColor?this.closeColor:this.color?this.color:"dark"==this.mode?"#ffffff":this.type}},methods:{clickTag:function(){this.disabled||this.$emit("click",this.index)},close:function(){this.$emit("close",this.index)}}};e.default=a},4555:function(t,e,i){var a=i("1a67");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("64ab9787",a,!0,{sourceMap:!1,shadowMode:!1})},4590:function(t,e,i){"use strict";i.r(e);var a=i("dd40"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"46bc":function(t,e,i){var a=i("e6e2");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("d46ec326",a,!0,{sourceMap:!1,shadowMode:!1})},"48af":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-like .title[data-v-177b39ce]{height:%?100?%;padding:0 %?20?%}.goods-like .title .line[data-v-177b39ce]{width:%?58?%;height:1px;background-color:#ccc;margin:0 %?22?%}.goods-like .title uni-image[data-v-177b39ce]{width:%?38?%;height:%?38?%}.goods-like .title .like[data-v-177b39ce]{padding:%?1?% %?11?%;background:linear-gradient(180deg,#ff2c3c,#f95f2f);border-radius:100px 100px 100px 0}.goods-like .goods[data-v-177b39ce]{padding-bottom:%?10?%}.goods-like .goods .goods-item[data-v-177b39ce]{width:%?240?%;margin-left:%?20?%;display:inline-block}.goods-like .goods .goods-item[data-v-177b39ce]:last-of-type{margin-right:%?20?%}',""]),t.exports=e},"4ae9":function(t,e,i){"use strict";i.r(e);var a=i("0523"),n=i("2bb0");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("2cec");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"5f921626",null,!1,a["a"],void 0);e["default"]=r.exports},"4bd2":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.navbar .navbar-left[data-v-05668d7e]{display:flex;padding:%?12?% %?25?%;border-radius:30px;background:hsla(0,0%,100%,.3);border:%?1?% solid rgba(0,0,0,.1)}.navbar .navbar-left .line[data-v-05668d7e]{width:1px;height:%?36?%;background:rgba(0,0,0,.2);margin:0 %?25?%}.navbar .navbar-left .navbar-lists[data-v-05668d7e]{display:flex;justify-content:center;position:relative}.navbar .navbar-left .navbar-lists .navbar-float[data-v-05668d7e]{position:absolute;top:40px;width:%?258?%;padding:0 %?24?%;background:#fff;border-radius:%?14?%;box-shadow:0 3px 6px rgba(0,0,0,.06)}.navbar .navbar-left .navbar-lists .navbar-float[data-v-05668d7e]::before{content:"";display:block;position:absolute;left:50%;width:0;height:0;border:%?14?% solid transparent;border-bottom-color:#fff;-webkit-transform:translate(-50%,-100%);transform:translate(-50%,-100%)}.navbar .navbar-left .navbar-lists .navbar-float .float-item[data-v-05668d7e]{padding:%?20?% 0;display:flex;align-items:center}.navbar .navbar-left .navbar-lists .navbar-float .float-item[data-v-05668d7e]:not(:last-of-type){border-bottom:1px solid #e5e5e5}.navbar .mask[data-v-05668d7e]{position:fixed;top:0;left:0;width:100%;height:100%;z-index:1}',""]),t.exports=e},"4c0e":function(t,e,i){"use strict";i.r(e);var a=i("51fd"),n=i("2281");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("e11a");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"45185faf",null,!1,a["a"],void 0);e["default"]=r.exports},"4e09":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("e838");var n=a(i("d4ab")),s=a(i("d24f")),o=a(i("17fd")),r=a(i("43ae")),c=a(i("e2a1")),l={name:"share-poster",components:{lPainter:n.default,lPainterImage:s.default,lPainterText:o.default,lPainterView:r.default,lPainterQrcode:c.default},props:{config:{type:Object,default:function(){return{}}},shareId:{type:[Number,String],default:""},qrcode:{type:[String],default:""},link:{type:String,default:""},type:{type:[String,Number],default:1},bShareTitle:{type:String,default:"我正在参与砍价 还差一步"},bShareIntro:{type:String,default:"帮忙砍一刀"}},data:function(){return{}},computed:{price:function(){var t=this.config.price;return void 0==t?{}:(t=String(parseFloat(t)).split("."),{prev:t[0],next:t[1]?".".concat(t[1]):""})},marketPrice:function(){return"￥".concat(parseFloat(this.config.marketPrice))}},methods:{handleSuccess:function(t){this.$emit("success",t)},handleFail:function(){this.$emit("fail")}}};e.default=l},5052:function(t,e,i){"use strict";var a=i("8d4f"),n=i.n(a);n.a},5062:function(t,e,i){var a=i("6908");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("22d854a7",a,!0,{sourceMap:!1,shadowMode:!1})},"50b7":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={navbar:i("17e4").default,downloadNav:i("d5d9").default,loadingView:i("200a").default,bubbleTips:i("8d8d").default,productSwiper:i("7a54").default,priceFormat:i("8718").default,uTag:i("7a4b").default,goodsLike:i("6328").default,uParse:i("ca96").default,uBadge:i("7601").default,recommend:i("4ae9").default,specPopup:i("e60f").default,sharePopup:i("8300").default,uPopup:i("c990").default,couponList:i("5dbb").default,uIcon:i("5b98").default,uBackTop:i("3293").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-details"},[i("navbar",{attrs:{title:"商品详情",background:{background:"rgba(256,256,256,"+t.percent+")"},titleColor:"rgba(0,0,0,"+t.percent+")",immersive:!0}}),t.showDownload?i("download-nav",{attrs:{top:44}}):t._e(),t.isFirstLoading?i("loading-view"):t._e(),t.isNull?i("v-uni-view",[i("v-uni-view",{staticClass:"details-null column-center"},[i("v-uni-image",{staticClass:"img-null",attrs:{src:t.baseUrl+"/image/goods_null.png"}}),i("v-uni-view",{staticClass:"xs muted"},[t._v("该商品已下架或不存在，去逛逛别的吧~")])],1),i("recommend")],1):i("v-uni-view",{staticClass:"contain"},[i("bubble-tips",{attrs:{top:"180rpx"}}),i("product-swiper",{attrs:{imgUrls:t.swiperList,video:t.goodsDetail.video}}),1==t.goodsType?i("v-uni-view",{staticClass:"seckill row-between"},[i("v-uni-view",{staticClass:"price row"},[i("v-uni-view",{staticClass:"row white info"},[i("v-uni-view",{staticClass:"row ml20",staticStyle:{"align-items":"baseline"}},[i("v-uni-view",{staticClass:"mr10"},[t._v("秒杀价")]),i("price-format",{attrs:{"first-size":46,"second-size":32,"subscript-size":32,price:t.goodsDetail.min_price,weight:500}}),t.goodsDetail.min_price!=t.goodsDetail.max_price?[i("v-uni-text",{staticStyle:{"font-size":"46rpx"}},[t._v("-")]),i("price-format",{attrs:{"first-size":46,"second-size":32,"subscript-size":32,"show-subscript":!1,price:t.goodsDetail.max_price,weight:500}})]:t._e(),i("v-uni-view",{staticClass:"ml10"},[i("price-format",{attrs:{"subscript-size":30,"line-through":!0,"first-size":30,"second-size":30,price:t.goodsDetail.market_price}})],1)],2)],1)],1)],1):t._e(),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:2==t.goodsType,expression:"goodsType == 2"}],staticClass:"group"},[i("v-uni-view",{staticClass:"row info",staticStyle:{height:"100%"}},[i("v-uni-view",{staticClass:"row-between ml20 white",staticStyle:{flex:"1"}},[i("v-uni-view",{staticClass:"row",staticStyle:{"align-items":"baseline"}},[i("v-uni-view",{staticClass:"mr10"},[t._v("活动价")]),i("price-format",{attrs:{"subscript-size":32,"first-size":46,"second-size":32,price:t.model1.model1_min_price,weight:500}}),i("v-uni-text",{staticClass:"xs"},[t._v("起")])],1),i("v-uni-view",{staticClass:"mr20 row group-num"},[i("v-uni-view",{staticClass:"group-icon"},[i("v-uni-image",{staticClass:"icon-sm",attrs:{src:t.baseUrl+"/image/icon_group.png"}})],1),i("v-uni-view",{staticClass:"xxs ml10 mr10"},[t._v(t._s(t.model1.people_num)+"人")])],1)],1)],1)],1),i("v-uni-view",{staticClass:"goods-info bg-white"},[1!=t.goodsType?i("v-uni-view",{staticClass:"info-header row"},[i("v-uni-view",{staticClass:"price row flex1"},[i("v-uni-view",{staticClass:"primary mr10"},[i("price-format",{attrs:{"first-size":46,"second-size":32,"subscript-size":32,price:t.goodsDetail.min_price,weight:500}}),t.goodsDetail.min_price!=t.goodsDetail.max_price?[i("v-uni-text",{staticStyle:{"font-size":"46rpx"}},[t._v("-")]),i("price-format",{attrs:{"first-size":46,"second-size":32,"subscript-size":32,"show-subscript":!1,price:t.goodsDetail.max_price,weight:500}})]:t._e()],2),i("v-uni-view",{staticClass:"line-through muted md"},[i("price-format",{attrs:{price:t.goodsDetail.market_price}})],1)],1),i("v-uni-image",{staticClass:"icon-share",attrs:{src:t.baseUrl+"/image/icon_share.png"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showShare.apply(void 0,arguments)}}})],1):t._e(),!t.goodsType&&t.goodsDetail.member_price?i("v-uni-view",{staticClass:"row"},[i("v-uni-view",{staticClass:"vip-price row"},[i("v-uni-view",{staticClass:"price-name xxs"},[t._v("会员价")]),i("v-uni-view",{staticStyle:{padding:"0 11rpx"}},[i("price-format",{attrs:{price:t.goodsDetail.member_price,"first-size":26,"second-size":26,"subscript-size":22,weight:500,color:"#7B3200"}}),i("v-uni-text",{staticClass:"xxs",staticStyle:{color:"#7B3200"}},[t._v("起")])],1)],1)],1):t._e(),i("v-uni-view",{staticClass:"row"},[i("v-uni-view",{staticClass:"name lg bold"},[t._v(t._s(t.goodsDetail.name))]),1==t.goodsType?i("v-uni-image",{staticClass:"icon-share",attrs:{src:t.baseUrl+"/image/icon_share.png"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showShare.apply(void 0,arguments)}}}):t._e()],1),i("v-uni-view",{staticClass:"row-between xs lighter",staticStyle:{padding:"0 24rpx 20rpx"}},[!0!==t.goodsDetail.stock?i("v-uni-text",[t._v("库存: "+t._s(t.goodsDetail.stock)+"件")]):t._e(),i("v-uni-text",[t._v("销量: "+t._s(t.goodsDetail.sales_sum)+"件")]),i("v-uni-text",[t._v("浏览量: "+t._s(t.goodsDetail.click_count)+"次")])],1)],1),2==t.goodsType?i("v-uni-view",{staticClass:"group-play bg-white mt20"},[i("v-uni-view",{staticClass:"title"},[t._v("活动玩法")]),i("v-uni-view",{staticClass:"steps row"},[i("v-uni-view",{staticClass:"row step"},[i("v-uni-view",{staticClass:"number xxs"},[t._v("1")]),i("v-uni-view",{staticClass:"sm"},[t._v("创建/参与")])],1),i("v-uni-view",{staticClass:"line"}),i("v-uni-view",{staticClass:"row step"},[i("v-uni-view",{staticClass:"number xxs"},[t._v("2")]),i("v-uni-view",{staticClass:"sm"},[t._v("团满即成新团")])],1),i("v-uni-view",{staticClass:"line"}),i("v-uni-view",{staticClass:"row step"},[i("v-uni-view",{staticClass:"number xxs"},[t._v("3")]),i("v-uni-view",{staticClass:"sm"},[t._v("满员发货")])],1)],1)],1):t._e(),2==t.goodsType&&t.treeData.length?i("v-uni-view",{staticClass:"group-play bg-white mt20"},[i("v-uni-view",{staticClass:"title"},[t._v("我的团队")]),i("v-uni-view",{staticClass:"steps"},[t.treeData.length?i("v-uni-scroll-view",{staticClass:"tree-container",attrs:{"scroll-x":"true"}},[i("v-uni-view",{staticClass:"tree"},[i("v-uni-view",{staticClass:"node-level-1"},[i("v-uni-view",{staticClass:"node-box"},[i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v(t._s(t.treeData[0].nickname||null))]),i("v-uni-view",{},[t._v("贡献度："+t._s(t.treeData[0].contribution_point))])],1),i("v-uni-view",{staticClass:"line line-1-2"})],1),i("v-uni-view",{staticClass:"children-level-2"},[i("v-uni-view",{staticClass:"node-level-2"},[i("v-uni-view",{staticClass:"node-box"},[i("v-uni-view",{staticClass:"line line-2-3 top"}),t.secData.length?i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v(t._s(t.secData[0].nickname||null))]),i("v-uni-view",{},[t._v("贡献度："+t._s(t.secData[0].contribution_point))])],1):i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v("空闲待邀请")]),i("v-uni-view",{},[t._v("贡献度：0")])],1),i("v-uni-view",{staticClass:"line line-2-3"})],1),i("v-uni-view",{staticClass:"children-level-3"},[i("v-uni-view",{staticClass:"node-level-3"},[i("v-uni-view",{staticClass:"node-box"},[i("v-uni-view",{staticClass:"line line-3-3 top"}),t.thirData.length?i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v(t._s(t.thirData[0].nickname||null))]),i("v-uni-view",{},[t._v("贡献度："+t._s(t.thirData[0].contribution_point))])],1):i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v("空闲待邀请")]),i("v-uni-view",{},[t._v("贡献度：0")])],1)],1)],1),i("v-uni-view",{staticClass:"node-level-3"},[i("v-uni-view",{staticClass:"node-box"},[i("v-uni-view",{staticClass:"line line-3-3 top"}),t.thirData.length>1?i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v(t._s(t.thirData[1].nickname||null))]),i("v-uni-view",{},[t._v("贡献度："+t._s(t.thirData[1].contribution_point))])],1):i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v("空闲待邀请")]),i("v-uni-view",{},[t._v("贡献度：0")])],1)],1)],1)],1)],1),i("v-uni-view",{staticClass:"node-level-2"},[i("v-uni-view",{staticClass:"node-box"},[i("v-uni-view",{staticClass:"line line-2-3 top"}),t.secData.length>1?i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v(t._s(t.secData[1].nickname||null))]),i("v-uni-view",{},[t._v("贡献度："+t._s(t.secData[1].contribution_point))])],1):i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v("空闲待邀请")]),i("v-uni-view",{},[t._v("贡献度：0")])],1),i("v-uni-view",{staticClass:"line line-2-3"})],1),i("v-uni-view",{staticClass:"children-level-3"},[t.forData.length?i("v-uni-view",{staticClass:"node-level-3"},[i("v-uni-view",{staticClass:"node-box"},[t.forData[0].nickname?i("v-uni-view",{staticClass:"line line-3-3 top"}):t._e(),i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v(t._s(t.forData[0].nickname||null))]),i("v-uni-view",{},[t._v("贡献度："+t._s(t.forData[0].contribution_point))])],1)],1)],1):i("v-uni-view",{staticClass:"node-level-3"},[i("v-uni-view",{staticClass:"node-box"},[i("v-uni-view",{staticClass:"line line-3-3 top"}),i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v("空闲待邀请")]),i("v-uni-view",{},[t._v("贡献度：0")])],1)],1)],1),t.forData.length>1?i("v-uni-view",{staticClass:"node-level-3"},[i("v-uni-view",{staticClass:"node-box"},[t.forData[1].nickname?i("v-uni-view",{staticClass:"line line-3-3 top"}):t._e(),i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v(t._s(t.forData[1].nickname||null))]),i("v-uni-view",{},[t._v("贡献度："+t._s(t.forData[1].contribution_point))])],1)],1)],1):i("v-uni-view",{staticClass:"node-level-3"},[i("v-uni-view",{staticClass:"node-box"},[i("v-uni-view",{staticClass:"line line-3-3 top"}),i("v-uni-view",{staticClass:"node"},[i("v-uni-view",{},[t._v("空闲待邀请")]),i("v-uni-view",{},[t._v("贡献度：0")])],1)],1)],1)],1)],1)],1)],1)],1)],1):t._e()],1)],1):t._e(),t.couponList.length||t.goodsDetail.order_give_integral?i("v-uni-view",{staticClass:"discount mt20 bg-white"},[i("v-uni-view",{staticClass:"row",staticStyle:{"align-items":"flex-start"}},[i("v-uni-view",{staticClass:"text muted"},[t._v("优惠")]),i("v-uni-view",{staticStyle:{flex:"1"}},[t.couponList.length?i("v-uni-view",{class:["row coupons",{mb30:t.goodsDetail.order_give_integral>0}],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCouponFun.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"flexnone"},[i("u-tag",{attrs:{text:"领券",size:"mini",type:"primary",mode:"plain"}})],1),i("v-uni-view",{staticClass:"con row ml20",staticStyle:{flex:"1"}},t._l(t.couponList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"coupons-item  mr20"},[a<2?i("v-uni-view",{staticClass:"row xs"},[i("v-uni-view",{staticClass:"line1"},[t._v(t._s(e.use_condition))])],1):t._e()],1)})),1),i("v-uni-image",{staticClass:"icon-sm",attrs:{src:t.baseUrl+"/images/arrow_right.png"}})],1):t._e(),t.goodsDetail.order_give_integral?i("v-uni-view",{staticClass:"row integral",staticStyle:{"align-items":"flex-start"}},[i("v-uni-view",{staticClass:"flexnone"},[i("u-tag",{attrs:{text:"贡献值",size:"mini",type:"primary",mode:"plain"}})],1),i("v-uni-view",{staticClass:"ml20"},[t._v("下单最多可获得"+t._s(t.goodsDetail.order_give_integral)+"贡献值")])],1):t._e()],1)],1)],1):t._e(),t.goodsType?t._e():i("v-uni-view",{staticClass:"spec row bg-white mt20",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSpecFun(0)}}},[i("v-uni-view",{staticClass:"text lighter"},[t._v("已选")]),i("v-uni-view",{staticClass:"line1 mr20",staticStyle:{flex:"1"}},[t._v(t._s(t.checkedGoods.spec_value_str||"默认"))]),i("v-uni-image",{staticClass:"icon-sm",attrs:{src:t.baseUrl+"/images/arrow_right.png"}})],1),i("v-uni-navigator",{staticClass:"mt20",attrs:{"hover-class":"none",url:"/bundle/pages/server_explan/server_explan?type=2"}},[i("v-uni-view",{staticClass:"row bg-white",staticStyle:{padding:"24rpx 24rpx"}},[i("v-uni-view",{staticClass:"text lighter flex1"},[t._v("售后保障")]),i("v-uni-image",{staticClass:"icon-sm",attrs:{src:t.baseUrl+"/images/arrow_right.png"}})],1)],1),i("v-uni-view",{staticClass:"evaluation bg-white mt20"},[i("v-uni-navigator",{staticClass:"title row-between",attrs:{"hover-class":"none",url:"/pages/all_comments/all_comments?id="+t.goodsDetail.id}},[i("v-uni-view",[i("v-uni-text",{staticClass:"balck md mr10"},[t._v("用户评价")]),i("v-uni-text",{staticClass:"primary sm"},[t._v("好评率"+t._s(t.comment.goods_rate||"0%"))])],1),i("v-uni-view",{staticClass:"row"},[i("v-uni-text",{staticClass:"lighter"},[t._v("查看全部")]),i("v-uni-image",{staticClass:"icon-sm",attrs:{src:t.baseUrl+"/images/arrow_right.png"}})],1)],1),t.comment.goods_rate?i("v-uni-view",{staticClass:"con"},[i("v-uni-view",{staticClass:"user-info row"},[i("v-uni-image",{staticClass:"avatar mr20",attrs:{src:t.comment.avatar}}),i("v-uni-view",{staticClass:"user-name md mr10"},[t._v(t._s(t.comment.nickname))])],1),i("v-uni-view",{staticClass:"muted xs mt10"},[i("v-uni-text",{staticClass:"mr20"},[t._v(t._s(t.comment.create_time))])],1),t.comment.comment?i("v-uni-view",{staticClass:"dec mt20"},[t._v(t._s(t.comment.comment))]):t._e()],1):i("v-uni-view",{staticClass:"con row-center muted"},[t._v("暂无评价")])],1),t.goodsLike.length?i("v-uni-view",{staticClass:"goods-like mt20 bg-white"},[i("goods-like",{attrs:{list:t.goodsLike}})],1):t._e(),i("v-uni-view",{staticClass:"details mt20 bg-white"},[i("v-uni-view",{staticClass:"title lg"},[t._v("商品详情")]),i("v-uni-view",{staticClass:"content"},[i("u-parse",{attrs:{html:t.goodsDetail.content,"lazy-load":!0,"show-with-animation":!0}})],1)],1),i("v-uni-view",{staticClass:"footer row bg-white fixed"},[i("v-uni-navigator",{staticClass:"btn column-center",attrs:{"hover-class":"none",url:"/bundle/pages/contact_offical/contact_offical"}},[i("v-uni-image",{staticClass:"icon-md",attrs:{src:t.baseUrl+"/image/icon_contact.png"}}),i("v-uni-text",{staticClass:"xxs lighter"},[t._v("客服")])],1),i("v-uni-button",{staticClass:"btn column-center",attrs:{"hover-class":"none"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.collectGoodsFun.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"icon-md",attrs:{src:1==t.goodsDetail.is_collect?t.baseUrl+"/image/icon_collection_s.png":t.baseUrl+"/image/icon_collection.png"}}),i("v-uni-text",{staticClass:"xxs lighter"},[t._v("收藏")])],1),i("v-uni-navigator",{staticClass:"btn cart column-center",attrs:{"hover-class":"none","open-type":"switchTab",url:"/pages/shop_cart/shop_cart"}},[i("v-uni-image",{staticClass:"icon-md",attrs:{src:t.baseUrl+"/image/icon_cart.png"}}),i("v-uni-text",{staticClass:"xxs lighter"},[t._v("购物车")]),t.cartNum?i("u-badge",{attrs:{bgColor:"#FF2C3C",offset:[8,10],count:t.cartNum}}):t._e()],1),t.btnText.yellow?i("v-uni-view",{staticClass:"add-cart br60 white mr10 md ml20",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSpecFun(1)}}},[t._v(t._s(t.btnText.yellow))]):t._e(),1!=t.hasgroup?i("v-uni-view",{staticClass:"right-buy br60 white mr20 ml10 md",class:t.cangroup?"":"bg-gray",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSpecFun(2)}}},[t._v(t._s(t.btnText.red))]):t._e(),1==t.hasgroup?i("v-uni-view",{staticClass:"right-buy br60 white mr20 ml10 md",class:t.cangroup?"":"bg-gray",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSpecFun(3)}}},[t._v("立即参与")]):t._e()],1)],1),i("spec-popup",{attrs:{show:t.showSpec,goods:t.goodsDetail,isModel:1,"is-seckill":1==t.goodsType,"show-add":1==t.popupType||0==t.popupType,"show-buy":2==t.popupType||0==t.popupType,showConfirm:3==t.popupType,group:Boolean(t.isGroup),"red-btn-text":t.btnText.red,"yellow-btn-text":t.btnText.yellow},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.showSpec=!1},buynow:function(e){arguments[0]=e=t.$handleEvent(e),t.onBuy.apply(void 0,arguments)},addcart:function(e){arguments[0]=e=t.$handleEvent(e),t.onAddCart.apply(void 0,arguments)},change:function(e){arguments[0]=e=t.$handleEvent(e),t.onChangeGoods.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}}}),i("share-popup",{attrs:{"share-id":t.id,pagePath:"pages/goods_details/model_details",type:1,config:{avatar:t.userInfo.avatar,nickname:t.userInfo.nickname,image:t.goodsDetail.poster||t.goodsDetail.image,price:t.goodsDetail.min_price,marketPrice:t.goodsDetail.market_price,name:t.goodsDetail.name}},model:{value:t.showShareBtn,callback:function(e){t.showShareBtn=e},expression:"showShareBtn"}}),i("u-popup",{attrs:{mode:"bottom","border-radius":"14"},model:{value:t.showCoupon,callback:function(e){t.showCoupon=e},expression:"showCoupon"}},[i("v-uni-view",[i("v-uni-view",{staticClass:"row-between",staticStyle:{padding:"30rpx"}},[i("v-uni-view",{staticClass:"title md bold"},[t._v("领券")]),i("v-uni-view",{staticClass:"close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCoupon=!1}}},[i("v-uni-image",{staticClass:"icon-lg",attrs:{src:t.baseUrl+"/image/icon_close.png"}})],1)],1),i("v-uni-view",{staticClass:"content bg-body"},[i("v-uni-scroll-view",{staticStyle:{height:"700rpx"},attrs:{"scroll-y":"true"}},[i("coupon-list",{attrs:{list:t.couponList,"btn-type":3},on:{reflash:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsCouponFun.apply(void 0,arguments)}}})],1)],1)],1)],1),i("v-uni-view",{staticClass:"share-money",class:{show:t.showCommission&&t.enableCommission}},[i("v-uni-view",{staticClass:"row-end"},[i("v-uni-view",{staticClass:"share-close row-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCommission=!1}}},[i("u-icon",{attrs:{name:"close",size:"16",color:"#fff"}})],1)],1),i("v-uni-view",{staticClass:"share-con mt10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showShareBtn=!0}}},[i("v-uni-view",{staticClass:"primary",staticStyle:{"font-size":"45rpx"}},[t._v(t._s(t.distribution.earnings)),i("v-uni-text",{staticClass:"xs"},[t._v("元")])],1),i("v-uni-view",{staticClass:"lighter xxs"},[t._v("好友下单最高可赚")])],1)],1),i("u-back-top",{attrs:{"scroll-top":t.scrollTop,top:1e3,customStyle:{backgroundColor:"#FFF",color:"#000",boxShadow:"0px 3px 6px rgba(0, 0, 0, 0.1)"}}})],1)},s=[]},"51fd":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uIcon:i("5b98").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-image",style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"u-image__image",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"u-image__loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[t.$slots.loading?t._t("loading"):i("u-icon",{attrs:{name:t.loadingIcon,width:t.width,height:t.height}})],2):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image__error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[t.$slots.error?t._t("error"):i("u-icon",{attrs:{name:t.errorIcon,width:t.width,height:t.height}})],2):t._e()],1)},s=[]},"53f7":function(t,e,i){"use strict";var a=i("7658"),n=i("57e7");a("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),n)},5775:function(t,e,i){"use strict";(function(t){i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("b7c7"));i("64aa"),i("bf0f"),i("2797"),i("c223"),i("f7a5"),i("8f71"),i("fd3c"),i("d4b5"),i("5ef2"),i("aa9c"),i("f3f7"),i("18f7"),i("de6c"),i("20f3");var s={data:function(){return{checkedGoods:{stock:0},outOfStock:[],specList:[],disable:[],goodsNum:1,showPop:!1}},components:{},props:{show:{type:Boolean},goods:{type:Object},showAdd:{type:Boolean,default:!0},showBuy:{type:Boolean,default:!0},showConfirm:{type:Boolean,default:!1},redBtnText:{type:String,default:"立即购买"},yellowBtnText:{type:String,default:"加入购物车"},group:{type:Boolean,default:!1},isSeckill:{type:Boolean,default:!1},showStock:{type:Boolean,default:!0},disabledNumberBox:{type:Boolean,default:!1},isBargain:{type:Boolean,default:!1},goodsType:{type:Number},isModel:{type:Number,default:0}},mounted:function(){},computed:{specValueText:function(){var t,e,i=this,a=null===(t=this.checkedGoods.spec_value_ids)||void 0===t?void 0:t.split(","),n="";return a&&a.forEach((function(t,e){""==t&&(n+=i.specList[e].name+",")})),0!=(null===(e=this.checkedGoods)||void 0===e?void 0:e.stock)&&""==n?"已选择 ".concat(this.checkedGoods.spec_value_str," ").concat(this.goodsNum," 件"):"请选择 ".concat(n.slice(0,n.length-1))}},watch:{goods:function(t){this.specList=t.goods_spec||[];var e=t.goods_item||[];this.outOfStock=e.filter((function(t){return 0==t.stock}));var i=e.filter((function(t){return 0!=t.stock}));0!=i.length?(i[0].spec_value_ids_arr=i[0].spec_value_ids.split(","),this.checkedGoods=i[0]):(e[0].spec_value_ids_arr=[],this.disable=e.map((function(t){return t.spec_value_ids.split(",")})),this.checkedGoods=e[0])},specList:function(e){var i=this;if(0!=this.checkedGoods.stock){var a=this.goods.goods_item.filter((function(t){return i.checkedGoods.spec_value_ids===t.spec_value_ids})),n=this.checkedGoods.spec_value_ids_arr,s=this.outOfStock,o=this.getArrResult(n,s);if(this.disable=this.getOutOfStockArr(o,n),0!=a.length){t.log(a,"-----");var r=JSON.parse(JSON.stringify(a[0]));r.spec_value_ids_arr=r.spec_value_ids.split(","),this.goodsNum>r.stock&&(this.goodsNum=r.stock),this.checkedGoods=r,this.$emit("change",{detail:this.checkedGoods})}}},show:function(t){this.showPop=t}},created:function(){t.log("spec")},methods:{isDisable:function(t){var e=this.disable.filter((function(e){return e==t}));return 0!=e.length},onClose:function(){this.$emit("close")},onClick:function(t){var e=this.checkedGoods,i=this.goodsNum;return-1!=this.specValueText.indexOf("请选择")?this.$toast({title:this.specValueText}):0==e.stock?this.$toast({title:"当前选择库存不足"}):(e.goodsNum=i,void this.$emit(t,{detail:e}))},choseSpecItem:function(t,e){var i=this.specList[t].spec_value[e].id,a=this.disable.filter((function(t){return t==i}));if(0==a.length){var s=this.checkedGoods.spec_value_ids_arr;i==s[t]?s[t]="":s[t]=i,this.checkedGoods.spec_value_ids_arr=s,this.checkedGoods.spec_value_ids=s.join(","),this.specList=(0,n.default)(this.specList)}},getOutOfStockArr:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return t.forEach((function(t){t.num>=e.length-1&&i.push.apply(i,(0,n.default)(t.different))})),i},getArrIdentical:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return t.forEach((function(t){e.forEach((function(e){t==e&&(a+=1,i.push(t))}))})),{num:a,different:this.getArrDifference((0,n.default)(new Set(i)).map(Number),e.map(Number)),identical:(0,n.default)(new Set(i))}},getArrResult:function(t,e){var i=this,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return e.forEach((function(e){var n=i.getArrIdentical(t,e.spec_value_ids.split(","));void 0!=n&&0!=n.length&&a.push(n)})),a},getArrDifference:function(t,e){return t.concat(e).filter((function(t,e,i){return i.indexOf(t)==i.lastIndexOf(t)}))},previewImage:function(t){uni.previewImage({current:t,urls:[t]})}}};e.default=s}).call(this,i("ba7c")["default"])},"57e7":function(t,e,i){"use strict";var a=i("e37c"),n=i("e4ca"),s=i("a74c"),o=i("ae5c"),r=i("b720"),c=i("1eb8"),l=i("50757"),d=i("0cc2"),u=i("97ed"),f=i("437f"),p=i("ab4a"),v=i("d0b1").fastKey,h=i("235c"),g=h.set,m=h.getterFor;t.exports={getConstructor:function(t,e,i,d){var u=t((function(t,n){r(t,f),g(t,{type:e,index:a(null),first:void 0,last:void 0,size:0}),p||(t.size=0),c(n)||l(n,t[d],{that:t,AS_ENTRIES:i})})),f=u.prototype,h=m(e),b=function(t,e,i){var a,n,s=h(t),o=w(t,e);return o?o.value=i:(s.last=o={index:n=v(e,!0),key:e,value:i,previous:a=s.last,next:void 0,removed:!1},s.first||(s.first=o),a&&(a.next=o),p?s.size++:t.size++,"F"!==n&&(s.index[n]=o)),t},w=function(t,e){var i,a=h(t),n=v(e);if("F"!==n)return a.index[n];for(i=a.first;i;i=i.next)if(i.key===e)return i};return s(f,{clear:function(){var t=h(this),e=t.first;while(e)e.removed=!0,e.previous&&(e.previous=e.previous.next=void 0),e=e.next;t.first=t.last=void 0,t.index=a(null),p?t.size=0:this.size=0},delete:function(t){var e=h(this),i=w(this,t);if(i){var a=i.next,n=i.previous;delete e.index[i.index],i.removed=!0,n&&(n.next=a),a&&(a.previous=n),e.first===i&&(e.first=a),e.last===i&&(e.last=n),p?e.size--:this.size--}return!!i},forEach:function(t){var e,i=h(this),a=o(t,arguments.length>1?arguments[1]:void 0);while(e=e?e.next:i.first){a(e.value,e.key,this);while(e&&e.removed)e=e.previous}},has:function(t){return!!w(this,t)}}),s(f,i?{get:function(t){var e=w(this,t);return e&&e.value},set:function(t,e){return b(this,0===t?0:t,e)}}:{add:function(t){return b(this,t=0===t?0:t,t)}}),p&&n(f,"size",{configurable:!0,get:function(){return h(this).size}}),u},setStrong:function(t,e,i){var a=e+" Iterator",n=m(e),s=m(a);d(t,e,(function(t,e){g(this,{type:a,target:t,state:n(t),kind:e,last:void 0})}),(function(){var t=s(this),e=t.kind,i=t.last;while(i&&i.removed)i=i.previous;return t.target&&(t.last=i=i?i.next:t.state.first)?u("keys"===e?i.key:"values"===e?i.value:[i.key,i.value],!1):(t.target=void 0,u(void 0,!0))}),i?"entries":"values",!i,!0),f(e)}}},"59a0":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={data:function(){return{currentSwiper:0,offset:0,barLeft:0}},components:{},props:{progressBarColor:{type:String,default:"#01B55B"},progressWidth:{type:Number,default:90},progressHeight:{type:Number,default:6},progressColor:{type:String,default:"#E5E5E5"},left:{type:Number,default:0},barWidth:{type:Number,default:30}},watch:{left:function(t){this.barLeft=t/100*this.offset}},beforeMount:function(){this.offset=this.progressWidth-this.barWidth},destroyed:function(){},methods:{}};e.default=a},"5cc4":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-numberbox[data-v-1d01409a]{display:inline-flex;align-items:center}.u-number-input[data-v-1d01409a]{position:relative;text-align:center;padding:0;margin:0 %?6?%;display:flex;flex-direction:row;align-items:center;justify-content:center}.u-icon-plus[data-v-1d01409a],\n.u-icon-minus[data-v-1d01409a]{width:%?60?%;display:flex;flex-direction:row;justify-content:center;align-items:center}.u-icon-plus[data-v-1d01409a]{border-radius:0 %?8?% %?8?% 0}.u-icon-minus[data-v-1d01409a]{border-radius:%?8?% 0 0 %?8?%}.u-icon-disabled[data-v-1d01409a]{color:#c8c9cc!important;background:#f7f8fa!important}.u-input-disabled[data-v-1d01409a]{color:#c8c9cc!important;background-color:#f2f3f5!important}',""]),t.exports=e},"5dbb":function(t,e,i){"use strict";i.r(e);var a=i("f17b"),n=i("fa7aa");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("63a7");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"47bab472",null,!1,a["a"],void 0);e["default"]=r.exports},"5f9f":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[this.nodes.length?this._e():this._t("default"),e("v-uni-view",{style:this.showAm+(this.selectable?";user-select:text;-webkit-user-select:text":""),attrs:{id:"_top"}},[e("div",{attrs:{id:"rtf"+this.uid}})])],2)},n=[]},"5fe5":function(t,e,i){var a=i("845b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("15c659c0",a,!0,{sourceMap:!1,shadowMode:!1})},6328:function(t,e,i){"use strict";i.r(e);var a=i("df1d"),n=i("2544");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("0a77");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"177b39ce",null,!1,a["a"],void 0);e["default"]=r.exports},"63a7":function(t,e,i){"use strict";var a=i("6d78"),n=i.n(a);n.a},6728:function(t,e,i){"use strict";i.r(e);var a=i("688d"),n=i("113c");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("b9d4");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"e51d8c58",null,!1,a["a"],void 0);e["default"]=r.exports},"67cb":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("l-painter",{attrs:{css:"width: 640rpx; padding-bottom: 35rpx; background-color: #ffffff; box-shadow: 0 20rpx 58rpx rgba(0,0,0,.15);border-radius: 10rpx;",isCanvasToTempFilePath:!0,"custom-style":"position: fixed; left: 200%"},on:{success:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSuccess.apply(void 0,arguments)},fail:function(e){arguments[0]=e=t.$handleEvent(e),t.handleFail.apply(void 0,arguments)}}},[i("l-painter-view",{attrs:{css:"padding-left: 40rpx;"}},[i("l-painter-image",{attrs:{src:t.config.avatar,css:"margin-top: 15rpx; width: 72rpx;  height: 72rpx; border-radius: 50%;"}}),i("l-painter-view",{attrs:{css:"margin-top: 30rpx; padding-left: 20rpx; display: inline-block"}},[i("l-painter-text",{attrs:{text:"来自"+t.config.nickname+"的分享",css:"display: block; padding-bottom: 10rpx; color: #333333; font-size: 28rpx;line-clamp:1;width: 100%;"}})],1),i("l-painter-image",{attrs:{src:t.config.image,css:"object-fit: cover; object-position: center; width: 560rpx; height: 560rpx;margin-top: 15rpx;"}}),i("l-painter-view",{attrs:{css:"margin-top: 30rpx;"}},[i("l-painter-view",{attrs:{css:"display: "+(1==t.type?"inline-block":"none")+"; width: 400rpx;"}},[i("l-painter-view",{attrs:{css:"vertical-align: bottom; color: #FF2C3C; font-size: 30rpx; line-height: 1em;"}},[i("l-painter-text",{attrs:{text:"￥",css:"vertical-align: bottom;font-size: 28rpx;"}}),i("l-painter-text",{attrs:{text:t.price.prev,css:"vertical-align: bottom; font-size: 38rpx;"}}),i("l-painter-text",{attrs:{text:t.price.next,css:"vertical-align: bottom; font-size: 30rpx;"}}),i("l-painter-text",{attrs:{text:t.marketPrice,css:"vertical-align: bottom; padding-left: 10rpx;font-size: 28rpx; font-weight: normal; text-decoration: line-through; color: #999999"}})],1),i("l-painter-view",{attrs:{css:"margin-top:30rpx;"}},[i("l-painter-text",{attrs:{css:"line-clamp: 2; color: #333333; line-height: 1.5em;font-size: 30rpx; width: 378rpx; padding-right:22rpx; box-sizing: border-box",text:t.config.name}})],1)],1),i("l-painter-view",{attrs:{css:"display: "+(2==t.type?"inline-block":"none")+"; width: 400rpx;"}},[i("l-painter-view",[i("l-painter-text",{attrs:{css:"line-clamp: 2; color: #FF2C3C; line-height: 1.5em;font-size: 32rpx; width: 375rpx; padding-right:22rpx; box-sizing: border-box",text:t.bShareTitle}})],1),i("l-painter-view",{attrs:{css:"margin-top:8rpx;"}},[i("l-painter-text",{attrs:{css:"line-clamp: 2; color: #F95F2F; line-height: 1.5em;font-size: 24rpx; width: 378rpx; padding-right:22rpx; box-sizing: border-box",text:t.bShareIntro}})],1),i("l-painter-view",{attrs:{css:"margin-top:8rpx;"}},[i("l-painter-text",{attrs:{css:"line-clamp: 2; color: #333333; line-height: 1.5em;font-size: 28rpx; width: 378rpx; padding-right:22rpx; box-sizing: border-box",text:t.config.name}})],1)],1),i("l-painter-view",{attrs:{css:"display: inline-block;"}},[i("l-painter-qrcode",{attrs:{css:"width: 168rpx; height: 168rpx;",text:t.link}}),i("l-painter-text",{attrs:{text:"长按识别二维码",css:"display: block; padding-top: 10rpx; color: #999999;font-size: 24rpx;"}})],1)],1)],1)],1)],1)},n=[]},"688d":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"cu-progress",style:"height: "+this.progressHeight+"; width: "+this.progressWidth+";background-color: "+this.progressColor},[e("v-uni-view",{staticClass:"cu-progress-bar",style:"background-color: "+this.progressBarColor+";left: "+this.barLeft+"rpx; width:"+this.barWidth+"rpx"})],1)},n=[]},6908:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.share-popup .share-tab[data-v-7a88e6ec]{margin:%?40?% 0 %?140?%}.share-popup .share-tab .share-icon[data-v-7a88e6ec]{width:%?100?%;height:%?100?%}.share-popup .cancel[data-v-7a88e6ec]{height:%?98?%}.share-poster .share-img[data-v-7a88e6ec]{width:%?640?%;border-radius:%?12?%}.share-poster .save-btn[data-v-7a88e6ec]{background-color:#ff2c3c;color:#fff;margin-top:%?20?%}.share-tips .share-arrow[data-v-7a88e6ec]{width:%?140?%;height:%?250?%;float:right;margin:%?15?% %?31?% 0 0}',""]),t.exports=e},"6d78":function(t,e,i){var a=i("a815");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("6ce388f4",a,!0,{sourceMap:!1,shadowMode:!1})},7283:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("695b"),s=a(i("2a9e")),o={data:function(){return{goodsList:[]}},components:{goodsList:s.default},props:{},beforeMount:function(){this.getBestListFun()},destroyed:function(){},methods:{getBestListFun:function(){var t=this;(0,n.getBestList)({page_no:1,page_size:6}).then((function(e){1==e.code&&(t.goodsList=e.data.list)}))}}};e.default=o},"746c":function(t,e,i){var a=i("edf0");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("6a319b24",a,!0,{sourceMap:!1,shadowMode:!1})},7601:function(t,e,i){"use strict";i.r(e);var a=i("ff79a"),n=i("82c5");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("d81a");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"629aeef1",null,!1,a["a"],void 0);e["default"]=r.exports},"7a4b":function(t,e,i){"use strict";i.r(e);var a=i("baaf"),n=i("324f");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("0de7");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"1cd62f78",null,!1,a["a"],void 0);e["default"]=r.exports},"7a54":function(t,e,i){"use strict";i.r(e);var a=i("2870"),n=i("3208");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("5052");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"eed7a8bc",null,!1,a["a"],void 0);e["default"]=r.exports},"7e19":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-tag[data-v-1cd62f78]{box-sizing:border-box;align-items:center;border-radius:%?6?%;display:inline-block}.u-size-default[data-v-1cd62f78]{font-size:%?22?%;padding:%?6?% %?12?%}.u-size-mini[data-v-1cd62f78]{font-size:%?20?%;padding:%?1?% %?6?%}.u-mode-light-primary[data-v-1cd62f78]{background-color:#ecf5ff;color:#ff2c3c;border:1px solid #a0cfff}.u-mode-light-success[data-v-1cd62f78]{background-color:#dbf1e1;color:#19be6b;border:1px solid #71d5a1}.u-mode-light-error[data-v-1cd62f78]{background-color:#fef0f0;color:#fa3534;border:1px solid #fab6b6}.u-mode-light-warning[data-v-1cd62f78]{background-color:#fdf6ec;color:#f90;border:1px solid #fcbd71}.u-mode-light-info[data-v-1cd62f78]{background-color:#f4f4f5;color:#909399;border:1px solid #c8c9cc}.u-mode-dark-primary[data-v-1cd62f78]{background-color:#ff2c3c;color:#fff}.u-mode-dark-success[data-v-1cd62f78]{background-color:#19be6b;color:#fff}.u-mode-dark-error[data-v-1cd62f78]{background-color:#fa3534;color:#fff}.u-mode-dark-warning[data-v-1cd62f78]{background-color:#f90;color:#fff}.u-mode-dark-info[data-v-1cd62f78]{background-color:#909399;color:#fff}.u-mode-plain-primary[data-v-1cd62f78]{background-color:#fff;color:#ff2c3c;border:1px solid #ff2c3c}.u-mode-plain-success[data-v-1cd62f78]{background-color:#fff;color:#19be6b;border:1px solid #19be6b}.u-mode-plain-error[data-v-1cd62f78]{background-color:#fff;color:#fa3534;border:1px solid #fa3534}.u-mode-plain-warning[data-v-1cd62f78]{background-color:#fff;color:#f90;border:1px solid #f90}.u-mode-plain-info[data-v-1cd62f78]{background-color:#fff;color:#909399;border:1px solid #909399}.u-disabled[data-v-1cd62f78]{opacity:.55}.u-shape-circle[data-v-1cd62f78]{border-radius:%?100?%}.u-shape-circleRight[data-v-1cd62f78]{border-radius:0 %?100?% %?100?% 0}.u-shape-circleLeft[data-v-1cd62f78]{border-radius:%?100?% 0 0 %?100?%}.u-close-icon[data-v-1cd62f78]{margin-left:%?14?%;font-size:%?22?%;color:#19be6b}.u-icon-wrap[data-v-1cd62f78]{display:inline-flex;-webkit-transform:scale(.86);transform:scale(.86)}',""]),t.exports=e},"7efd":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a=i("73e7"),n={data:function(){return{progressPer:0}},components:{},props:{list:{type:Array,default:function(){return[]}}},watch:{list:{handler:function(){this.$nextTick((function(){var t=this;(0,a.getRect)(".goods-like",!1,this).then((function(e){t.rectWidth=e.width}))}))},immediate:!0,deep:!0}},methods:{scrollBarChange:function(t){var e=this.progressPer,i=t.detail,a=i.scrollLeft,n=i.scrollWidth;e=a/(n-this.rectWidth)*100,e=Number(e.toFixed(0)),this.progressPer=e}}};e.default=n},8021:function(t,e,i){"use strict";i.r(e);var a=i("2b95"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"80fb":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* components/recommend/recommend.wxss */.recommend[data-v-5f921626]{padding-bottom:%?50?%}.recommend .header .title[data-v-5f921626]{width:%?468?%;height:%?45?%;margin:%?10?% 0}.recommend .goods-title[data-v-5f921626]{height:%?100?%}.recommend .goods-title .line[data-v-5f921626]{width:%?58?%;height:1px;background-color:#ccc;margin:0 %?22?%}.recommend .goods-title uni-image[data-v-5f921626]{width:%?38?%;height:%?38?%}",""]),t.exports=e},"82c5":function(t,e,i){"use strict";i.r(e);var a=i("0fb6"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},8300:function(t,e,i){"use strict";i.r(e);var a=i("cc19"),n=i("8021");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("8d06");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"7a88e6ec",null,!1,a["a"],void 0);e["default"]=r.exports},"845b":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.spec-contain[data-v-d04c0de2]{border-radius:%?10?% %?10?% 0 0;overflow:hidden;position:relative}.spec-contain .close[data-v-d04c0de2]{position:absolute;right:%?10?%;top:%?10?%}.spec-contain .spec-header[data-v-d04c0de2]{padding:%?30?% %?20?%;padding-right:%?70?%;align-items:flex-start;border:1px solid #e5e5e5}.spec-contain .spec-header .vip-price[data-v-d04c0de2]{margin:0 %?24?%;background-color:#ffe9ba;line-height:%?36?%;border-radius:%?6?%;overflow:hidden}.spec-contain .spec-header .vip-price .price-name[data-v-d04c0de2]{background-color:#101010;padding:%?3?% %?12?%;color:#ffd4b7;position:relative;overflow:hidden}.spec-contain .spec-header .vip-price .price-name[data-v-d04c0de2]::after{content:"";display:block;width:%?20?%;height:%?20?%;position:absolute;right:%?-15?%;background-color:#ffe9ba;border-radius:50%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);box-sizing:border-box}.spec-contain .spec-header .goods-img[data-v-d04c0de2]{width:%?180?%;height:%?180?%;flex:none}.spec-contain .spec-main .spec-list[data-v-d04c0de2]{padding:%?30?% %?20?%}.spec-contain .spec-main .spec-list .spec-item[data-v-d04c0de2]{line-height:%?52?%;padding:0 %?20?%;background-color:#f4f4f4;border-radius:%?60?%;margin:0 %?20?% %?20?% 0;border:1px solid #f4f4f4}.spec-contain .spec-main .spec-list .spec-item.checked[data-v-d04c0de2]{background-color:#ffe9eb;color:#ff2c3c;border-color:#ff2c3c}.spec-contain .spec-main .spec-list .spec-item.disabled[data-v-d04c0de2]{opacity:.5;text-decoration:line-through}.spec-contain .disabled[data-v-d04c0de2]{opacity:.4}.spec-contain .btns[data-v-d04c0de2]{height:%?120?%;padding:0 %?30?%;margin-top:%?80?%}.spec-contain .btns .add-cart[data-v-d04c0de2]{background-color:#ff9e1e}.spec-contain .btns .btn[data-v-d04c0de2]{margin:0 %?10?%;flex:1}',""]),t.exports=e},"84e0":function(t,e,i){"use strict";i.r(e);var a=i("bc21"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},8609:function(t,e,i){"use strict";i.r(e);var a=i("40b4"),n=i("4590");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("cf70");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"1d01409a",null,!1,a["a"],void 0);e["default"]=r.exports},"878e":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uIcon:i("5b98").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.showDownload?i("v-uni-view",{staticClass:"download-nav-container row-between",style:{top:t.top+"px"}},[i("v-uni-view",{staticClass:"sm white"},[t._v(t._s(t.appConfig.download_doc))]),i("v-uni-view",{staticClass:"row"},[i("v-uni-view",{staticClass:"white primary-btn row-center br60 xs",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.go2DownLoad.apply(void 0,arguments)}}},[t._v("下载APP")]),i("u-icon",{attrs:{name:"close",size:"32",color:"#fff"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showDownload=!1}}})],1)],1):t._e()},s=[]},"8a8f":function(t,e,i){var a=i("80fb");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("3b1b86e4",a,!0,{sourceMap:!1,shadowMode:!1})},"8d06":function(t,e,i){"use strict";var a=i("5062"),n=i.n(a);n.a},"8d4f":function(t,e,i){var a=i("b558");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("b03ccf66",a,!0,{sourceMap:!1,shadowMode:!1})},"8d8d":function(t,e,i){"use strict";i.r(e);var a=i("e7b8"),n=i("99ce");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("ea01");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"25efd58f",null,!1,a["a"],void 0);e["default"]=r.exports},"91b6":function(t,e,i){"use strict";i.r(e);var a=i("9afc"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},9200:function(t,e,i){var a=i("48af");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("3f9fa117",a,!0,{sourceMap:!1,shadowMode:!1})},"99ce":function(t,e,i){"use strict";i.r(e);var a=i("c8ce"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"9afc":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{title:String,titleColor:{type:String,default:"#000000"},background:{type:Object,default:function(){return{background:"#ffffff"}}},borderBottom:{type:Boolean,default:!1},immersive:{type:Boolean,default:!1}},data:function(){return{baseUrl:this.baseUrl,isIndex:!1,navLists:[{url:"/pages/index/index",name:"首页",icon:this.baseUrl+"/image/icon_home.png",type:"switchTab"},{url:"/pages/goods_search/goods_search",name:"搜索",icon:this.baseUrl+"/image/icon_search.png",type:"navigate"},{url:"/pages/shop_cart/shop_cart",name:"购物车",icon:this.baseUrl+"/image/icon_carts.png",type:"switchTab"},{url:"/pages/user/user",name:"个人中心",icon:this.baseUrl+"/image/icon_user.png",type:"switchTab"}],showFloat:!1}},methods:{goBack:function(){this.isIndex?uni.switchTab({url:"/pages/index/index"}):uni.navigateBack()}},computed:{backIcon:function(){var t=this.isIndex?"icon_home":"icon_back";return this.baseUrl+"/image/".concat(t,".png")}},created:function(){var t=this;setTimeout((function(){var e=getCurrentPages();1==e.length&&(t.isIndex=!0)}))}};e.default=a},a1fa:function(t,e,i){"use strict";var a=i("d6ce"),n=i.n(a);n.a},a325:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("3471"));i("5ef2"),i("4626"),i("5ac7"),i("5c47"),i("2c10"),i("af8f"),i("a1c1"),i("e966"),i("aa9c"),i("c223"),i("e838");var s=uni.getSystemInfoSync(),o=s.windowWidth,r=(s.platform,i("ca3b")),c={name:"parser",data:function(){return{uid:this._uid,showAm:"",nodes:[]}},props:{html:String,autopause:{type:Boolean,default:!0},autoscroll:Boolean,autosetTitle:{type:Boolean,default:!0},domain:String,lazyLoad:Boolean,selectable:Boolean,tagStyle:Object,showWithAnimation:Boolean,useAnchor:Boolean},watch:{html:function(t){this.setContent(t)}},created:function(){this.imgList=[],this.imgList.each=function(t){for(var e=0,i=this.length;e<i;e++)this.setItem(e,t(this[e],e,this))},this.imgList.setItem=function(t,e){if(void 0!=t&&e){if(0==e.indexOf("http")&&this.includes(e)){for(var i,a=e.split("://")[0],n=a.length;i=e[n];n++){if("/"==i&&"/"!=e[n-1]&&"/"!=e[n+1])break;a+=Math.random()>.5?i.toUpperCase():i}return a+=e.substr(n),this[t]=a}if(this[t]=e,e.includes("data:image")){var s=e.match(/data:image\/(\S+?);(\S+?),(.+)/);if(!s)return}}}},mounted:function(){this.document=document.getElementById("rtf"+this._uid),this.html&&this.setContent(this.html)},beforeDestroy:function(){this._observer&&this._observer.disconnect(),this.imgList.each((function(t){})),clearInterval(this._timer)},methods:{setContent:function(t,e){var i=this;if(t){var a=document.createElement("div");e?this.rtf?this.rtf.appendChild(a):this.rtf=a:(this.rtf&&this.rtf.parentNode.removeChild(this.rtf),this.rtf=a),a.innerHTML=this._handleHtml(t,e);for(var s,c=this.rtf.getElementsByTagName("style"),l=0;s=c[l++];)s.innerHTML=s.innerHTML.replace(/body/g,"#rtf"+this._uid),s.setAttribute("scoped","true");!this._observer&&this.lazyLoad&&IntersectionObserver&&(this._observer=new IntersectionObserver((function(t){for(var e,a=0;e=t[a++];)e.isIntersecting&&(e.target.src=e.target.getAttribute("data-src"),e.target.removeAttribute("data-src"),i._observer.unobserve(e.target))}),{rootMargin:"500px 0px 500px 0px"}));var d=this,u=this.rtf.getElementsByTagName("title");u.length&&this.autosetTitle&&uni.setNavigationBarTitle({title:u[0].innerText});var f=function(t){var e=t.getAttribute("src");i.domain&&e&&("/"==e[0]?"/"==e[1]?t.src=(i.domain.includes("://")?i.domain.split("://")[0]:"")+":"+e:t.src=i.domain+e:e.includes("://")||0==e.indexOf("data:")||(t.src=i.domain+"/"+e))};this.imgList.length=0;for(var p,v=this.rtf.getElementsByTagName("img"),h=0,g=0;p=v[h];h++)parseInt(p.style.width||p.getAttribute("width"))>o&&(p.style.height="auto"),f(p),p.hasAttribute("ignore")||"A"==p.parentElement.nodeName||(p.i=g++,d.imgList.push(p.getAttribute("original-src")||p.src||p.getAttribute("data-src")),p.onclick=function(t){t.stopPropagation();var e=!0;this.ignore=function(){return e=!1},d.$emit("imgtap",this),e&&uni.previewImage({current:this.i,urls:d.imgList})}),p.onerror=function(){r.errorImg&&(d.imgList[this.i]=this.src=r.errorImg),d.$emit("error",{source:"img",target:this})},d.lazyLoad&&this._observer&&p.src&&0!=p.i&&(p.setAttribute("data-src",p.src),p.removeAttribute("src"),this._observer.observe(p));var m,b=this.rtf.getElementsByTagName("a"),w=(0,n.default)(b);try{for(w.s();!(m=w.n()).done;){var x=m.value;x.onclick=function(t){t.stopPropagation();var e=!0,i=this.getAttribute("href");if(d.$emit("linkpress",{href:i,ignore:function(){return e=!1}}),e&&i)if("#"==i[0])d.useAnchor&&d.navigateTo({id:i.substr(1)});else{if(0==i.indexOf("http")||0==i.indexOf("//"))return!0;uni.navigateTo({url:i})}return!1}}}catch(L){w.e(L)}finally{w.f()}var _=this.rtf.getElementsByTagName("video");d.videoContexts=_;for(var y,C=0;y=_[C++];)f(y),y.style.maxWidth="100%",y.onerror=function(){d.$emit("error",{source:"video",target:this})},y.onplay=function(){if(d.autopause)for(var t,e=0;t=d.videoContexts[e++];)t!=this&&t.pause()};var k,S,T=this.rtf.getElementsByTagName("audio"),z=(0,n.default)(T);try{for(z.s();!(k=z.n()).done;){var $=k.value;f($),$.onerror=function(){d.$emit("error",{source:"audio",target:this})}}}catch(L){z.e(L)}finally{z.f()}if(this.autoscroll){var B,I=this.rtf.getElementsByTagName("table"),O=(0,n.default)(I);try{for(O.s();!(B=O.n()).done;){var E=B.value,F=document.createElement("div");F.style.overflow="scroll",E.parentNode.replaceChild(F,E),F.appendChild(E)}}catch(L){O.e(L)}finally{O.f()}}e||this.document.appendChild(this.rtf),this.$nextTick((function(){i.nodes=[1],i.$emit("load")})),setTimeout((function(){return i.showAm=""}),500),clearInterval(this._timer),this._timer=setInterval((function(){i.rect=i.rtf.getBoundingClientRect(),i.rect.height==S&&(i.$emit("ready",i.rect),clearInterval(i._timer)),S=i.rect.height}),350),this.showWithAnimation&&!e&&(this.showAm="animation:_show .5s")}else this.rtf&&!e&&this.rtf.parentNode.removeChild(this.rtf)},getText:function(){arguments.length>0&&void 0!==arguments[0]||this.nodes;var t="";return t=this.rtf.innerText,t},in:function(t){t.page&&t.selector&&t.scrollTop&&(this._in=t)},navigateTo:function(t){var e=this;if(!this.useAnchor)return t.fail&&t.fail("Anchor is disabled");var i=uni.createSelectorQuery().in(this._in?this._in.page:this).select((this._in?this._in.selector:"#_top")+(t.id?"".concat(" ","#").concat(t.id,",").concat(this._in?this._in.selector:"#_top").concat(" ",".").concat(t.id):"")).boundingClientRect();this._in?i.select(this._in.selector).scrollOffset().select(this._in.selector).boundingClientRect():i.selectViewport().scrollOffset(),i.exec((function(i){if(!i[0])return t.fail&&t.fail("Label not found");var a=i[1].scrollTop+i[0].top-(i[2]?i[2].top:0)+(t.offset||0);e._in?e._in.page[e._in.scrollTop]=a:uni.pageScrollTo({scrollTop:a,duration:300}),t.success&&t.success()}))},getVideoContext:function(t){if(!t)return this.videoContexts;for(var e=this.videoContexts.length;e--;)if(this.videoContexts[e].id==t)return this.videoContexts[e]},_handleHtml:function(t,e){if(!e){var i="<style scoped>@keyframes _show{0%{opacity:0}100%{opacity:1}}img{max-width:100%}";for(var a in r.userAgentStyles)i+="".concat(a,"{").concat(r.userAgentStyles[a],"}");for(a in this.tagStyle)i+="".concat(a,"{").concat(this.tagStyle[a],"}");i+="</style>",t=i+t}return t.includes("rpx")&&(t=t.replace(/[0-9.]+\s*rpx/g,(function(t){return parseFloat(t)*o/750+"px"}))),t}}};e.default=c},a815:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* components/coupon-list/coupon-list.wxss */.coupon-list[data-v-47bab472]{padding:%?20?% %?24?%}.coupon-list .coupon-item[data-v-47bab472]{position:relative;height:%?200?%;background-image:url(https://www.xinjiuhui.cn/image/coupon_bg.png);background-size:100% 100%}.coupon-list .coupon-item.gray[data-v-47bab472]{background-image:url(https://www.xinjiuhui.cn/image/coupon_bg_grey.png)}.coupon-list .coupon-item.gray .btn.plain[data-v-47bab472]{color:#ccc}.coupon-list .coupon-item .price[data-v-47bab472]{width:%?200?%}.coupon-list .coupon-item .btn[data-v-47bab472]{line-height:%?52?%;height:%?52?%;position:absolute;right:%?20?%;bottom:%?20?%;width:%?120?%;text-align:center;padding:0;text-align:center;box-sizing:border-box}.coupon-list .coupon-item .btn.plain[data-v-47bab472]{background-color:#fff;color:var(--primary-color);border:1px solid currentColor}.coupon-list .coupon-item .receive[data-v-47bab472]{position:absolute;right:%?30?%;top:%?0?%;width:%?99?%;height:%?77?%}.coupon-list .icon[data-v-47bab472]{transition:all .4s}.coupon-list .rotate[data-v-47bab472]{-webkit-transform:rotate(-180deg);transform:rotate(-180deg)}',""]),t.exports=e},a8d4:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-image[data-v-45185faf]{position:relative;transition:opacity .5s ease-in-out}.u-image__image[data-v-45185faf]{width:100%;height:100%}.u-image__loading[data-v-45185faf], .u-image__error[data-v-45185faf]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;background-color:#f3f4f6;color:#909399;font-size:%?46?%}',""]),t.exports=e},aa83:function(t,e,i){var a=i("389d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("16985672",a,!0,{sourceMap:!1,shadowMode:!1})},ada8:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.checkGroup=function(t){return n.default.post("Market/checkTeam",t)},e.checkGroupStatus=function(t){return n.default.post("Market/allocationPid",t)},e.closeBargainOrder=function(t){return n.default.get("bargain/closeBargain",{params:t})},e.getActivityGoodsLists=function(t){return n.default.get("activity_area/activityGoodsList",{params:t})},e.getBargainActivityDetail=function(t){return n.default.get("bargain/bargainDetail",{params:t})},e.getBargainActivityList=function(t){return n.default.get("bargain/orderList",{params:t})},e.getBargainDetail=function(t){return n.default.get("bargain/detail",{params:t})},e.getBargainList=function(t){return n.default.get("bargain/lists",{params:t})},e.getBargainNumber=function(){return n.default.get("bargain/barginNumber")},e.getBargainPost=function(t){return n.default.get("share/shareBargain",{params:t})},e.getCouponList=function(t){return n.default.get("coupon/couponList",{params:t})},e.getGoodsCoupon=function(t){return n.default.get("coupon/getGoodsCoupon",{params:t})},e.getGroupList=function(t){return n.default.get("team/teamGoodsList",{params:t})},e.getSeckillGoods=function(t){return n.default.get("seckill/seckillGoods",{params:t})},e.getSeckillTime=function(){return n.default.get("seckill/seckillTime")},e.getTeamInfo=function(t){return n.default.get("team/teamInfo",{params:t})},e.getUserGroup=function(t){return n.default.get("user/myTeam",{params:t})},e.helpBargain=function(t){return n.default.post("bargain/knife",t)},e.launchBargain=function(t){return n.default.post("bargain/sponsor",t)},e.teamBuy=function(t){return n.default.post("team/buy",t)},e.teamCheck=function(t){return n.default.post("team/check",t)};var n=a(i("35af"));i("73e7")},b0d3:function(t,e,i){"use strict";i.r(e);var a=i("bdac"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},b558:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.swiper-wrap[data-v-eed7a8bc]{width:100%;height:%?750?%;position:relative}.swiper-wrap .close[data-v-eed7a8bc]{width:%?250?%;height:%?250?%;top:%?230?%;right:%?50?%;z-index:1000;position:relative;position:fixed}.swiper-wrap .close .close-item[data-v-eed7a8bc]{top:%?0?%;right:%?0?%;position:absolute;z-index:1001}.swiper-wrap .swiper[data-v-eed7a8bc]{width:100%;height:100%;position:relative}.swiper-wrap .swiper .slide-image[data-v-eed7a8bc]{width:100%;height:100%}.swiper-wrap .dots[data-v-eed7a8bc]{position:absolute;right:%?24?%;bottom:%?24?%;display:flex;height:%?34?%;padding:0 %?15?%}.swiper-wrap .video-wrap[data-v-eed7a8bc]{width:100%;height:100%;position:relative;overflow:hidden}.swiper-wrap .my-video[data-v-eed7a8bc]{width:100%;height:100%}.swiper-wrap .icon-play[data-v-eed7a8bc]{width:%?90?%;height:%?90?%;position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);z-index:999}',""]),t.exports=e},b9d4:function(t,e,i){"use strict";var a=i("edac"),n=i.n(a);n.a},baaf:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uIcon:i("5b98").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.show?i("v-uni-view",{staticClass:"u-tag",class:[t.disabled?"u-disabled":"","u-size-"+t.size,"u-shape-"+t.shape,"u-mode-"+t.mode+"-"+t.type],style:[t.customStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickTag.apply(void 0,arguments)}}},[t._v(t._s(t.text)),i("v-uni-view",{staticClass:"u-icon-wrap",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[t.closeable?i("u-icon",{staticClass:"u-close-icon",style:[t.iconStyle],attrs:{size:"22",color:t.closeIconColor,name:"close"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}}):t._e()],1)],1):t._e()},s=[]},bc21:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("9b1b"));i("64aa");var s=i("8f59"),o={name:"download-nav",props:{top:{type:Number,default:0}},data:function(){return{showDownload:!0}},computed:(0,n.default)({},(0,s.mapGetters)(["appConfig"])),methods:{go2DownLoad:function(){var t=this;uni.getSystemInfo({success:function(e){"ios"==e.platform?window.open(t.appConfig.ios_download):window.open(t.appConfig.android_download)},fail:function(e){window.open(t.appConfig.android_download)}})}}};e.default=o},bcfd:function(t,e,i){var a=i("5cc4");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("38576310",a,!0,{sourceMap:!1,shadowMode:!1})},bdac:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"u-back-top",props:{mode:{type:String,default:"circle"},icon:{type:String,default:"arrow-upward"},tips:{type:String,default:""},duration:{type:[Number,String],default:100},scrollTop:{type:[Number,String],default:0},top:{type:[Number,String],default:400},bottom:{type:[Number,String],default:200},right:{type:[Number,String],default:40},zIndex:{type:[Number,String],default:"9"},iconStyle:{type:Object,default:function(){return{color:"#909399",fontSize:"38rpx"}}},customStyle:{type:Object,default:function(){return{}}}},watch:{showBackTop:function(t,e){t?(this.uZIndex=this.zIndex,this.opacity=1):(this.uZIndex=-1,this.opacity=0)}},computed:{showBackTop:function(){return this.scrollTop>uni.upx2px(this.top)}},data:function(){return{opacity:0,uZIndex:-1}},methods:{backToTop:function(){uni.pageScrollTo({scrollTop:0,duration:this.duration})}}};e.default=a},be9d:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.bubble-tips-container[data-v-25efd58f]{position:absolute;z-index:98}.bubble-tips-container .bubble-content[data-v-25efd58f]{padding:%?4?% %?20?% %?4?% %?10?%;background-color:rgba(0,0,0,.7);color:#fff;border-radius:%?120?%}.bubble-tips-container .bubble-content .bubble-img[data-v-25efd58f]{width:%?50?%;height:%?50?%;border-radius:50%;margin-right:%?10?%}',""]),t.exports=e},bff5:function(t,e,i){"use strict";i.r(e);var a=i("0784"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},c1d1:function(t,e,i){"use strict";(function(t){i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("aa9c"),i("4626"),i("d4b5");var n=a(i("2634")),s=a(i("2fdc")),o=a(i("9b1b")),r=i("695b"),c=i("695c"),l=i("ada8"),d=i("f222"),u=i("8f59"),f=i("73e7"),p=i("3313"),v=(a(i("5bad")),a(i("8300"))),h={components:{sharePopup:v.default},data:function(){return{baseUrl:this.baseUrl,scrollTop:0,percent:0,isFirstLoading:!0,isNull:!1,showSpec:!1,showCoupon:!1,showShareBtn:!1,showCommission:!0,popupType:"",swiperList:[],goodsDetail:{},goodsLike:[],goodsType:0,checkedGoods:{},couponList:[],comment:{},countTime:0,tagStyle:{img:"width:100%;"},model1:{},model1Found:[],isGroup:0,id:"",showDownload:!1,distribution:{},order_type:0,treeData:[],secData:[],thirData:[],forData:[],isBuy:!0,fid:"",hasgroup:0,cangroup:1,user_id:0}},onLoad:function(e){if(this.onPageScroll=(0,f.trottle)(this.onPageScroll,500,this),e&&e.scene){var i=(0,f.strToParams)(decodeURIComponent(e.scene));t.log(i,decodeURIComponent(e.scene)),e.id=i.id}if(e&&e.order_type&&(this.oprder_type=4),e&&1==e.isapp&&(this.showDownload=!0),!e.id)return this.$toast({title:"缺少参数，无法查看商品"},{tab:3});this.id=e.id,this.getGoodsCouponFun(),this.getCartNum(),this.user_id=uni.getStorageSync("userInfo").id},onShow:function(){this.getGoodsDetailFun(),this.checkBuy()},onPageScroll:function(t){var e=uni.upx2px(100),i=t.scrollTop;this.percent=i/e>1?1:i/e,this.scrollTop=i},methods:(0,o.default)((0,o.default)({},(0,u.mapActions)(["getCartNum"])),{},{showShare:function(){this.showShareBtn=!0},getPid:function(){var t=this;(0,d.getModelInfo)({user_id:uni.getStorageSync("userInfo").id,goods_id:this.id}).then(function(){var e=(0,s.default)((0,n.default)().mark((function e(i){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!i||1!=i.code){e.next=7;break}return t.hasgroup=i.data.hasgroup,i.data.hasgroup&&(t.fid=i.data.found_id),e.next=5,t.getModelOne();case 5:t.cangroup=i.data.cangroup,t.$forceUpdate();case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},checkBuy:function(){var t=this;(0,d.checkAllow)().then((function(e){1==e.code?t.isBuy=!0:t.isBuy=!1}))},getModelOne:function(){var e=this;return(0,s.default)((0,n.default)().mark((function i(){return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return t.log(e.fid,77777777),i.next=3,(0,d.detail)({model1_id:e.model1.model1_id,found_id:e.fid}).then((function(i){e.treeData=[];i.data.leader;var a=i.data.userlist;if(a.length&&(e.treeData.push(a[0]),a.length>1)){var n=[];n.push(a[1]),a.length>2&&n.push(a[2]),e.secData=n;var s=[];a.length>3&&(s.push(a[3]),a.length>4&&s.push(a[4])),e.thirData=s;var o=[];a.length>5&&(o.push(a[5]),a.length>6&&o.push(a[6])),e.forData=o}t.log(e.treeData,e.goodsType)}));case 3:case"end":return i.stop()}}),i)})))()},getGoodsDetailFun:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i,a,s,o,c,l,d,u,p,v,h,g,m;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,r.getGoodsDetail)({id:t.id,order_type:4});case 2:i=e.sent,a=i.data,s=i.code,1==s?(o=a.goods_image,a.content,c=a.comment,l=a.like,d=a.activity,u=a.distribution,p=d.info,v=d.model1,h=d.model1_found,g=p?p.end_time-Date.now()/1e3:v?v.end_time-Date.now()/1e3:0,h&&(h=(0,f.arraySlice)(h,[],2)),t.distribution=u||{},t.goodsDetail=a,t.swiperList=o,t.comment=c,t.goodsLike=l,t.countTime=g,t.goodsType=d.type||0,t.model1=v||{},t.model1Found=h||[],t.$nextTick((function(){t.isFirstLoading=!1})),m={shareTitle:a.name,shareLink:location.href+"&invite_code="+t.userInfo.sn,shareImage:a.image,shareDesc:a.remark},t.wxShare(m),t.getPid()):(t.isNull=!0,t.isFirstLoading=!1);case 6:case"end":return e.stop()}}),e)})))()},getGoodsCouponFun:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i,a,s;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,(0,l.getGoodsCoupon)({id:t.id});case 2:i=e.sent,a=i.data,s=i.code,1==s&&(t.couponList=a);case 6:case"end":return e.stop()}}),e)})))()},collectGoodsFun:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i,a,s;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.isLogin){e.next=2;break}return e.abrupt("return",(0,p.toLogin)());case 2:return i=t.goodsDetail.is_collect,e.next=5,(0,c.collectGoods)({is_collect:0==i?1:0,goods_id:t.id});case 5:a=e.sent,a.data,s=a.code,1==s&&(0==i?t.$toast({title:"收藏成功"}):t.$toast({title:"取消收藏"}),t.getGoodsDetailFun());case 9:case"end":return e.stop()}}),e)})))()},showCouponFun:function(){if(!this.isLogin)return(0,p.toLogin)();this.showCoupon=!0},onChangeGoods:function(e){t.log(e),this.checkedGoods=e.detail},showSpecFun:function(t,e){var i=this;return(0,s.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(i.isLogin){a.next=2;break}return a.abrupt("return",(0,p.toLogin)());case 2:return a.next=4,(0,d.getModelInfo)({user_id:uni.getStorageSync("userInfo").id,goods_id:i.id}).then(function(){var e=(0,s.default)((0,n.default)().mark((function e(a){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a&&1==a.code&&(i.hasgroup=a.data.hasgroup,a.data.hasgroup&&(i.fid=a.data.found_id,t=3),i.cangroup=a.data.cangroup,i.$forceUpdate());case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 4:if(i.cangroup){a.next=7;break}return uni.showToast({title:"您当前正在参与，请等待当前团结束在参加新的吧~",icon:"none"}),a.abrupt("return");case 7:2==i.goodsType&&[2,3].includes(t)?(i.isGroup=1,i.foundId=e):(i.isGroup=0,i.foundId=""),i.popupType=t,i.showSpec=!0;case 10:case"end":return a.stop()}}),a)})))()},onBuy:function(e){var i=e.detail,a=i.id,n=i.goodsNum,s=this.goodsType,o=this.model1,r=[{item_id:a,num:n,model1_id:o.model1_id,found_id:this.fid}],c={goods:r};this.showSpec=!1,2==s&&(c.model1Id=o.model1_id),this.foundId&&(c.foundId=31),uni.navigateTo({url:"/pages/confirm_order/confirm_order?data="+encodeURIComponent(JSON.stringify(c))}),t.log(1111)},onConfirm:function(t){var e=this,i=this.model1.model1_id;(0,d.teamCheck)({model1_id:i,found_id:this.foundId}).then((function(i){1==i.code&&e.onBuy(t)}))},onAddCart:function(t){var e=this;return(0,s.default)((0,n.default)().mark((function i(){var a,s,o,c,l,d,u;return(0,n.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(a=t.detail,s=a.id,o=a.goodsNum,2!=e.goodsType){i.next=5;break}return c=[{item_id:s,num:o}],uni.navigateTo({url:"/pages/confirm_order/confirm_order?data="+encodeURIComponent(JSON.stringify({goods:c}))}),i.abrupt("return");case 5:return i.next=7,(0,r.addCart)({item_id:s,goods_num:o});case 7:l=i.sent,d=l.code,l.data,u=l.msg,1==d&&(e.getCartNum(),e.$toast({title:u,icon:"success"}),e.showSpec=!1);case 12:case"end":return i.stop()}}),i)})))()}}),onShareAppMessage:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i,a,s;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i=t.goodsDetail,a=t.model1,s=t.userInfo,e.abrupt("return",{title:a.share_title||i.name,imageUrl:i.image,path:"/pages/goods_details/goods_details?id="+t.id+"&invite_code="+s.sn});case 2:case"end":return e.stop()}}),e)})))()},computed:(0,o.default)((0,o.default)({},(0,u.mapGetters)(["cartNum","userInfo"])),{},{btnText:function(){var t=this.goodsType;switch(t){case 1:return{red:"立即抢购",yellow:""};case 2:return{red:"立即创建"};default:return{red:"立即购买"}}},getmodel1CountTime:function(){return function(t){return t-Date.now()/1e3}},enableCommission:function(){var t=this.goodsType,e=this.distribution,i=e.earnings,a=e.is_show;return 0==t&&i>0&&1==a}})};e.default=h}).call(this,i("ba7c")["default"])},c8ce:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("d4b5"),i("f7a5");var n=i("d8e1"),s=a(i("5bad")),o={name:"BubbleTips",props:{discharge:{type:Boolean,default:!1},top:{type:String,default:"40rpx"},left:{type:String,default:"20rpx"},updateTime:{type:Number,default:3e5}},data:function(){return{index:s.default.get("currentIndex")||0,list:[],currentList:[],timer:null,showBubble:!1}},watch:{index:function(t,e){var i=this;if(!(this.index-this.list.length>=0))return this.timer&&(clearInterval(this.timer),this.timer=null),void this.fadeUpBubble();this.showBubble=!1;var a=setTimeout((function(){s.default.set("currentIndex",0),i.timer&&(clearInterval(i.timer),i.timer=null),i.fadeUpBubble(),clearTimeout(a)}),2e3)},discharge:function(){if(this.discharge)return s.default.set("currentIndex",this.index),clearInterval(this.timer),this.timer=null,!1;var t=s.default.get("currentInex")||this.list.length;t-this.list.length<0&&(this.timer&&(setInterval(this.timer),this.timer=null),this.fadeUpBubble())}},methods:{$getBubbleLists:function(){var t=this;(0,n.getBubbleLists)().then((function(e){if(1==e.code){t.list=e.data.lists;var i=1e3*e.data.time;s.default.set("bubbleList",JSON.stringify(t.list),300),s.default.set("requestTime",i),t.timer&&(clearInterval(t.timer),t.timer=null),t.fadeUpBubble()}}))},fadeUpBubble:function(){var t=this,e=s.default.get("requestTime"),i=new Date;if(this.showBubble=!0,this.index=s.default.get("currentIndex")||0,this.list=s.default.get("bubbleList")?JSON.parse(s.default.get("bubbleList")):[],i.getTime()-e>=this.updateTime)return this.$getBubbleLists(),void s.default.set("currentIndex",0,300);this.timer=setInterval((function(){t.currentList=t.list.slice(t.index,t.index+1),s.default.set("currentIndex",++t.index)}),4e3)}},created:function(){var t=s.default.get("currentIndex")||0,e=s.default.get("requestTime"),i=new Date,a=s.default.get("bubbleList")?JSON.parse(s.default.get("bubbleList")):[];a.length<=0?(this.$getBubbleLists(),s.default.set("currentIndex",0)):t-a.length>=0?(s.default.set("currentIndex",0),this.timer&&(clearInterval(this.timer),this.timer=null),this.fadeUpBubble()):i.getTime()-e>=this.updateTime?(this.$getBubbleLists(),s.default.set("currentIndex",0)):(this.timer&&(clearInterval(this.timer),this.timer=null),this.fadeUpBubble())},onLoad:function(){},destroyed:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}};e.default=o},ca3b:function(t,e){var i={errorImg:null,filter:null,highlight:null,onText:null,entities:{quot:'"',apos:"'",semi:";",nbsp:" ",ensp:" ",emsp:" ",ndash:"–",mdash:"—",middot:"·",lsquo:"‘",rsquo:"’",ldquo:"“",rdquo:"”",bull:"•",hellip:"…"},blankChar:a(" , ,\t,\r,\n,\f"),boolAttrs:a("allowfullscreen,autoplay,autostart,controls,ignore,loop,muted"),blockTags:a("address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section"),ignoreTags:a("area,base,canvas,frame,iframe,input,link,map,meta,param,script,source,style,svg,textarea,title,track,wbr"),richOnlyTags:a("a,colgroup,fieldset,legend"),selfClosingTags:a("area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),trustTags:a("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"),userAgentStyles:{address:"font-style:italic",big:"display:inline;font-size:1.2em",blockquote:"background-color:#f6f6f6;border-left:3px solid #dbdbdb;color:#6c6c6c;padding:5px 0 5px 10px",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",mark:"background-color:yellow",pre:"font-family:monospace;white-space:pre;overflow:scroll",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",u:"text-decoration:underline"}};function a(t){for(var e=Object.create(null),i=t.split(","),a=i.length;a--;)e[i[a]]=!0;return e}t.exports=i},ca96:function(t,e,i){"use strict";i.r(e);var a=i("5f9f"),n=i("3832");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("fa4f");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"6d8b3c66",null,!1,a["a"],void 0);e["default"]=r.exports},cb81:function(t,e,i){"use strict";i.r(e);var a=i("4e09"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},cbde:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-list .goods-double[data-v-c7e3d2d2]{flex-wrap:wrap;padding:0 %?20?%;align-items:stretch}.goods-list .goods-double .item[data-v-c7e3d2d2]{width:%?347?%;border-radius:%?10?%}.goods-list .goods-double .item .goods-info[data-v-c7e3d2d2]{padding:%?10?%}.goods-list .goods-hot.goods-home-hot .item[data-v-c7e3d2d2]{padding:0}.goods-list .goods-hot.goods-home-hot .item .paixu[data-v-c7e3d2d2],\n.goods-list .goods-hot.goods-home-hot .item .number[data-v-c7e3d2d2]{left:%?10?%}.goods-list .goods-hot .item[data-v-c7e3d2d2]{position:relative;padding:%?30?% %?20?%;border-radius:%?10?%}.goods-list .goods-hot .item .goods-info[data-v-c7e3d2d2]{width:%?450?%}.goods-list .goods-hot .item .goods-info .sale[data-v-c7e3d2d2]{padding:%?4?% %?18?%;color:#f79c0c;background-color:rgba(247,156,12,.1)}.goods-list .goods-hot .item .paixu[data-v-c7e3d2d2],\n.goods-list .goods-hot .item .number[data-v-c7e3d2d2]{position:absolute;top:0;left:%?27?%;width:%?50?%;height:%?54?%;line-height:%?60?%;text-align:center;color:#621e09}.goods-list .goods-one .item[data-v-c7e3d2d2]{padding:%?20?%}.goods-list .goods-one .item[data-v-c7e3d2d2]:not(:last-of-type){margin-bottom:%?20?%}.goods-list .goods-new .item[data-v-c7e3d2d2]{box-shadow:0 0 10px rgba(0,0,0,.16);border-radius:%?10?%}',""]),t.exports=e},cc19:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uPopup:i("c990").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{},[i("u-popup",{staticClass:"share-popup",attrs:{mode:"bottom","border-radius":"14",closeable:!0,"safe-area-inset-bottom":!0,"mask-close-able":!1},model:{value:t.showshare,callback:function(e){t.showshare=e},expression:"showshare"}},[i("v-uni-view",{staticClass:"row row-center md bold mt30 mb30"},[t._v("分享至")]),i("v-uni-view",{staticClass:"row row-around share-tab"},[i("v-uni-view",{staticClass:"column column-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getPoster.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"share-icon",attrs:{mode:"widthFix",src:"/static/images/icon_generate_poster.png"}}),i("v-uni-view",{staticStyle:{margin:"15rpx 0"}},[t._v("生成海报")])],1),i("v-uni-view",{attrs:{oclass:"column column-center"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.shareWx.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"share-icon",attrs:{src:"/static/images/icon_wechat.png"}}),i("v-uni-view",{},[t._v("微信好友")])],1)],1),i("v-uni-view",{staticClass:"row row-center bg-body cancel xl",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showshare=!1}}},[t._v("取消")])],1),i("u-popup",{staticClass:"share-poster",attrs:{mode:"center",closeable:!0,"safe-area-inset-bottom":!0},model:{value:t.showPoster,callback:function(e){t.showPoster=e},expression:"showPoster"}},[i("img",{staticStyle:{width:"640rpx"},attrs:{src:t.poster}}),i("v-uni-button",{staticClass:"row row-center save-btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.savePoster.apply(void 0,arguments)}}},[t._v("长按保存图片到相册")])],1),i("u-popup",{staticClass:"share-tips",attrs:{"custom-style":{background:"none"},mode:"top"},model:{value:t.showTips,callback:function(e){t.showTips=e},expression:"showTips"}},[i("v-uni-view",{staticStyle:{overflow:"hidden"}},[i("v-uni-image",{staticClass:"share-arrow",attrs:{src:"/static/images/share_arrow.png"}}),i("v-uni-view",{staticClass:"white",staticStyle:{"text-align":"center","margin-top":"280rpx"}},[i("v-uni-view",{staticClass:"bold lg"},[t._v("立即分享给好友吧")]),i("v-uni-view",{staticClass:"sm m-t-10"},[t._v("点击屏幕右上角将本页面分享给好友")])],1)],1)],1),t.enablePoster?i("poster",{attrs:{type:t.type,"share-id":t.shareId,config:t.config,qrcode:t.mnpQrcode,link:t.getLink,"b-share-title":t.bargainShare.share_title,"b-share-intro":t.bargainShare.share_intro},on:{success:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSuccess.apply(void 0,arguments)},fail:function(e){arguments[0]=e=t.$handleEvent(e),t.handleFail.apply(void 0,arguments)}}}):t._e()],1)},s=[]},cf70:function(t,e,i){"use strict";var a=i("bcfd"),n=i.n(a);n.a},cf9d:function(t,e,i){var a=i("be9d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("151439ee",a,!0,{sourceMap:!1,shadowMode:!1})},d00a:function(t,e,i){var a=i("7e19");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("f9da873c",a,!0,{sourceMap:!1,shadowMode:!1})},d5d9:function(t,e,i){"use strict";i.r(e);var a=i("878e"),n=i("84e0");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("048c");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"22ac6d8d",null,!1,a["a"],void 0);e["default"]=r.exports},d6ce:function(t,e,i){var a=i("441d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("3597828f",a,!0,{sourceMap:!1,shadowMode:!1})},d7a8:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uIcon:i("5b98").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-back-top",class:["u-back-top--mode--"+t.mode],style:[{bottom:t.bottom+"rpx",right:t.right+"rpx",borderRadius:"circle"==t.mode?"10000rpx":"8rpx",zIndex:t.uZIndex,opacity:t.opacity},t.customStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.backToTop.apply(void 0,arguments)}}},[t.$slots.default||t.$slots.$default?t._t("default"):i("v-uni-view",{staticClass:"u-back-top__content"},[i("u-icon",{attrs:{name:t.icon,"custom-style":t.iconStyle},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.backToTop.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"u-back-top__content__tips"},[t._v(t._s(t.tips))])],1)],2)},s=[]},d81a:function(t,e,i){"use strict";var a=i("aa83"),n=i.n(a);n.a},d985:function(t,e,i){"use strict";(function(t){i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("fd3c"),i("3efd");getApp();var a={data:function(){return{currentSwiper:0,urls:[],showPlay:!0,showControls:!1,autoplay:!0,start:0}},components:{},props:{imgUrls:{type:Array,default:function(){return[]}},circular:{type:Boolean,default:!0},interval:{type:Number,default:3e3},duration:{type:Number,default:500},video:{type:String},isShow:{type:Boolean,default:!1}},watch:{imgUrls:{handler:function(t){var e=this;this.urls=t.map((function(t){return{url:t.uri,type:"image"}})),this.video&&(this.urls.unshift({url:this.video,type:"video"}),this.autoplay=!1,this.$nextTick((function(){e.videoContext=uni.createVideoContext("myVideo",e),e.videoContexts=uni.createVideoContext("videos",e)})))},immediate:!0},isShow:{handler:function(t){1!=t||this.showPlay?0==t&&0==this.showPlay&&this.videoContext.play():this.videoContext.stop()}}},methods:{swiperChange:function(t){if(this.currentSwiper=t.detail.current,0!==t.detail.current&&""!=this.video)try{this.showPlay=!0,this.videoContext.stop(),this.videoContexts.stop()}catch(e){}},videoErrorCallback:function(e){t.log("err==>",e)},previewImage:function(t){var e=t;if(0==t&&this.video)return!1;this.video&&(e=t-1),"video"==this.urls[t].type?this.videoContext.requestFullScreen():uni.previewImage({indicator:"default",index:e,urls:this.imgUrls.map((function(t){return t.uri}))})},play:function(){0==this.start?(this.start=1,this.showPlay=!1,this.videoContext.play()):(this.start=0,this.showPlay=!0,this.videoContext.stop())},playEnd:function(){this.start=0,this.showPlay=!0},fullscreenchange:function(t){var e=t.detail.fullScreen;!e&&this.videoContext.stop(),this.showControls=!!e,this.showPlay=!e}}};e.default=a}).call(this,i("ba7c")["default"])},dd40:function(t,e,i){"use strict";(function(t){i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("5c47"),i("0506"),i("5ef2"),i("c9b5"),i("bf0f"),i("ab80");var a={name:"u-number-box",props:{value:{type:Number,default:1},bgColor:{type:String,default:"#F2F3F5"},min:{type:Number,default:0},max:{type:Number,default:99999},step:{type:Number,default:1},disabled:{type:Boolean,default:!1},size:{type:[Number,String],default:26},color:{type:String,default:"#323233"},inputWidth:{type:[Number,String],default:80},inputHeight:{type:[Number,String],default:50},index:{type:[Number,String],default:""},disabledInput:{type:Boolean,default:!1},cursorSpacing:{type:[Number,String],default:100},longPress:{type:Boolean,default:!0},pressTime:{type:[Number,String],default:250},positiveInteger:{type:Boolean,default:!0}},watch:{value:function(e,i){if(t.log(e,i),!this.changeFromInner){if(this.inputVal==e)return;this.inputVal=e,this.$nextTick((function(){this.changeFromInner=!1}))}},inputVal:function(t,e){var i=this;if(""!=t){var a=0,n=this.$u.test.number(t);a=n&&t>=this.min&&t<=this.max?t:e,this.positiveInteger&&(t<0||-1!==String(t).indexOf("."))&&(a=e,this.$nextTick((function(){i.inputVal=e}))),this.isFistVal||this.handleChange(a,"change")}}},data:function(){return{inputVal:1,timer:null,changeFromInner:!1,innerChangeTimer:null,isFistVal:!0}},created:function(){this.inputVal=Number(this.value),this.isFistVal=!1},computed:{getCursorSpacing:function(){return Number(uni.upx2px(this.cursorSpacing))}},methods:{btnTouchStart:function(t){var e=this;this[t](),this.longPress&&(clearInterval(this.timer),this.timer=null,this.timer=setInterval((function(){e[t]()}),this.pressTime))},clearTimer:function(){var t=this;this.$nextTick((function(){clearInterval(t.timer),t.timer=null}))},minus:function(){this.computeVal("minus")},plus:function(){this.computeVal("plus")},calcPlus:function(t,e){var i,a,n;try{a=t.toString().split(".")[1].length}catch(o){a=0}try{n=e.toString().split(".")[1].length}catch(o){n=0}i=Math.pow(10,Math.max(a,n));var s=a>=n?a:n;return((t*i+e*i)/i).toFixed(s)},calcMinus:function(t,e){var i,a,n;try{a=t.toString().split(".")[1].length}catch(o){a=0}try{n=e.toString().split(".")[1].length}catch(o){n=0}i=Math.pow(10,Math.max(a,n));var s=a>=n?a:n;return((t*i-e*i)/i).toFixed(s)},computeVal:function(t){if(uni.hideKeyboard(),!this.disabled){var e=0;"minus"===t?e=this.calcMinus(this.inputVal,this.step):"plus"===t&&(e=this.calcPlus(this.inputVal,this.step)),e<this.min||e>this.max||(this.inputVal=e,this.handleChange(e,t))}},onBlur:function(t){var e=this,i=0,a=t.detail.value;/(^\d+$)/.test(a)&&0!=a[0]||(i=this.min),i=+a,i>this.max?i=this.max:i<this.min&&(i=this.min),this.$nextTick((function(){e.inputVal=i})),this.handleChange(i,"blur")},onFocus:function(){this.$emit("focus")},handleChange:function(t,e){var i=this;this.disabled||(this.innerChangeTimer&&(clearTimeout(this.innerChangeTimer),this.innerChangeTimer=null),this.changeFromInner=!0,this.innerChangeTimer=setTimeout((function(){i.changeFromInner=!1}),150),this.$emit("input",Number(t)),this.$emit(e,{value:Number(t),index:this.index}))}}};e.default=a}).call(this,i("ba7c")["default"])},df1d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={customImage:i("33fd").default,priceFormat:i("8718").default,cuProgress:i("6728").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-like"},[i("v-uni-view",{staticClass:"title row-center"},[i("v-uni-text",{staticClass:"line"}),i("v-uni-view",{staticClass:"row"},[i("v-uni-image",{staticClass:"mr20",attrs:{src:"/static/images/icon_like.png"}}),i("v-uni-text",{staticClass:"bold xxl"},[t._v("猜你喜欢")])],1),i("v-uni-text",{staticClass:"line"})],1),i("v-uni-view",{staticClass:"goods"},[i("v-uni-scroll-view",{staticStyle:{height:"340rpx","white-space":"nowrap"},attrs:{"scroll-x":"true","scroll-with-animation":"true"},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.scrollBarChange.apply(void 0,arguments)}}},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"goods-item",attrs:{"hover-class":"none",url:"/pages/goods_details/goods_details?id="+e.id}},[i("custom-image",{attrs:{width:"240rpx",height:"240rpx",radius:"10rpx","lazy-load":!0,src:e.image}}),i("v-uni-view",{staticClass:"goods-name line1 mt10"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"price"},[i("price-format",{attrs:{color:"#FF2C3C","subscript-size":22,"second-size":22,price:e.price,weight:500}}),i("price-format",{staticClass:"ml10",attrs:{"show-subscript":!0,color:"#999","line-through":!0,"subscript-size":22,"first-size":22,"second-size":22,price:e.market_price}})],1)],1)})),1),t.list.length>3?i("v-uni-view",{staticClass:"row-center mb10 mt20"},[i("cu-progress",{attrs:{progressBarColor:"#FF2C3C",left:t.progressPer}})],1):t._e()],1)],1)},s=[]},e11a:function(t,e,i){"use strict";var a=i("f5bb"),n=i.n(a);n.a},e60f:function(t,e,i){"use strict";i.r(e);var a=i("f371"),n=i("3529");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("1651");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"d04c0de2",null,!1,a["a"],void 0);e["default"]=r.exports},e6e2:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-back-top[data-v-b8cea202]{width:%?80?%;height:%?80?%;position:fixed;z-index:9;display:flex;flex-direction:row;flex-direction:column;justify-content:center;background-color:#e1e1e1;color:#606266;align-items:center;transition:opacity .4s}.u-back-top__content[data-v-b8cea202]{display:flex;flex-direction:row;flex-direction:column;align-items:center}.u-back-top__content__tips[data-v-b8cea202]{font-size:%?24?%;-webkit-transform:scale(.8);transform:scale(.8);line-height:1}',""]),t.exports=e},e7b8:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"bubble-tips-container",style:{top:t.top,left:t.left}},t._l(t.currentList,(function(e){return i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showBubble,expression:"showBubble"}],key:e.id,staticClass:"bubble-content row"},[i("v-uni-image",{staticClass:"bubble-img",attrs:{src:e.user.avatar}}),i("v-uni-view",{staticClass:"xs"},[t._v(t._s(e.template))])],1)})),1)},n=[]},ea01:function(t,e,i){"use strict";var a=i("cf9d"),n=i.n(a);n.a},ea82:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uNavbar:i("3558").default,uIcon:i("5b98").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"navbar",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.showFloat=!1}}},[i("u-navbar",{attrs:{background:t.background,title:t.title,"title-color":t.titleColor,"border-bottom":t.borderBottom,immersive:t.immersive,"title-bold":!0,"is-back":!1}},[i("v-uni-view",{staticClass:"navbar-left",attrs:{slot:"left"},slot:"left"},[i("u-icon",{attrs:{name:t.backIcon,size:36},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"line"}),i("v-uni-view",{staticClass:"navbar-lists",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.showFloat=!t.showFloat}}},[i("u-icon",{attrs:{name:t.baseUrl+"/image/icon_list.png",size:32}}),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showFloat,expression:"showFloat"}],staticClass:"navbar-float"},t._l(t.navLists,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"float-item",attrs:{url:e.url,"open-type":e.type,"hover-class":"none"}},[i("u-icon",{attrs:{name:e.icon,size:44}}),i("v-uni-text",{staticClass:"ml20"},[t._v(t._s(e.name))])],1)})),1)],1)],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showFloat,expression:"showFloat"}],staticClass:"mask",on:{touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.showFloat=!1}}})],1)},s=[]},edac:function(t,e,i){var a=i("3497");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("41bacf4e",a,!0,{sourceMap:!1,shadowMode:!1})},edf0:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.download-nav-container[data-v-22ac6d8d]{height:%?80?%;background-color:#333;padding:0 %?30?%;position:fixed;width:100%;z-index:871;top:0}.download-nav-container .primary-btn[data-v-22ac6d8d]{background-color:#ff2c3c;height:%?54?%;width:%?154?%;margin-right:%?30?%}',""]),t.exports=e},f159:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={customImage:i("33fd").default,priceFormat:i("8718").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-list"},["double"===t.type?i("v-uni-view",{staticClass:"goods-double row-between"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+(t.isBargain?e.goods_id:e.id)}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"347rpx",height:"347rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"347rpx",height:"347rpx",radius:"10rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"goods-name line2"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price||e.activity_price}})],1)],1)],1)})),1):t._e(),"model2"===t.type?i("v-uni-view",{staticClass:"goods-hot"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/model2_details?id="+e.id+"&order_type=5"}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"180rpx",height:"180rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"180rpx",height:"180rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-text",{staticClass:"sale br60 xxs"},[t._v("已有"+t._s(e.sales_sum)+"人购买")]),i("v-uni-view",{staticClass:"row-between  mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1),i("v-uni-image",{staticClass:"icon-md",attrs:{src:"/static/images/icon_go_red.png"}})],1)],1)],1)})),1):t._e(),"hot"===t.type?i("v-uni-view",{staticClass:"goods-hot"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"180rpx",height:"180rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"180rpx",height:"180rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-text",{staticClass:"sale br60 xxs"},[t._v("已有"+t._s(e.sales_sum)+"人购买")]),i("v-uni-view",{staticClass:"row-between  mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1),i("v-uni-image",{staticClass:"icon-md",attrs:{src:"/static/images/icon_go_red.png"}})],1)],1),i("v-uni-image",{staticClass:"paixu",attrs:{src:t.baseUrl+"/image/No."+(a<3?a:3)+".png"}}),i("v-uni-view",{staticClass:"number xxs"},[t._v(t._s(a+1))])],1)})),1):t._e(),"home-hot"===t.type?i("v-uni-view",{staticClass:"goods-home-hot goods-hot"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mb20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img"},[i("custom-image",{attrs:{"lazy-load":!0,width:"240rpx",height:"240rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20 mr20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-text",{staticClass:"sale br60 xxs"},[t._v("已有"+t._s(e.sales_sum)+"人购买")]),i("v-uni-view",{staticClass:"row-between  mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1),i("v-uni-button",{staticClass:"br60",attrs:{type:"primary",size:"xs"}},[t._v("去购买")])],1)],1),i("v-uni-image",{staticClass:"paixu",attrs:{src:t.baseUrl+"/image/No."+(a<3?a:3)+".png"}}),i("v-uni-view",{staticClass:"number"},[t._v(t._s(a+1))])],1)})),1):t._e(),"new"===t.type?i("v-uni-view",{staticClass:"goods-new"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img"},[i("custom-image",{attrs:{"lazy-load":!0,width:"240rpx",height:"240rpx",radius:"10rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20 mr20 flex1"},[i("v-uni-view",{staticClass:"goods-name line2 mb20"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"row-between muted xxs "},[i("v-uni-view",{staticClass:"line-through"},[i("v-uni-text",[t._v("原价")]),i("price-format",{attrs:{"second-size":22,"first-size":22,"subscript-size":22,price:e.market_price}})],1),i("v-uni-view",[t._v(t._s(e.sales_sum)+"人购买")])],1),i("v-uni-view",{staticClass:"row-between  mt10"},[i("price-format",{attrs:{color:"#FF2C3C","first-size":38,"subscript-size":26,"second-size":26,price:e.price,weight:500}}),i("v-uni-button",{staticClass:"br60",attrs:{type:"primary",size:"xs"}},[t._v("立即抢购")])],1)],1)],1)})),1):t._e(),"one"===t.type?i("v-uni-view",{staticClass:"goods-one mt20"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"200rpx",height:"200rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"200rpx",height:"200rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"row-between mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1)],1)],1)],1)})),1):t._e()],1)},s=[]},f17b:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={priceFormat:i("8718").default,uIcon:i("5b98").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"coupon-list"},t._l(t.list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"mb20"},[i("v-uni-view",{class:"coupon-item row "+(1==t.btnType||2==t.btnType?"gray":"")},[i("v-uni-view",{staticClass:"price white column-center"},[i("v-uni-view",{staticClass:"xl"},[i("price-format",{attrs:{"first-size":60,"second-size":50,"subscript-size":34,price:e.money,weight:500}})],1),i("v-uni-view",{staticClass:"sm",staticStyle:{"text-align":"center"}},[t._v(t._s(e.use_condition))])],1),i("v-uni-view",{staticClass:"info ml20"},[i("v-uni-view",{staticClass:"bold lg mb10"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"xs lighter mb20"},[t._v(t._s(e.use_time_tips))]),i("v-uni-view",{staticClass:"xs lighter "},[t._v(t._s(e.coupon_type))])],1),i("v-uni-button",{class:"btn br60 white xs "+(3!=t.btnType?"plain":""),attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onHandle(e.id)}}},[t._v(t._s(t.getBtn))]),e.is_get?i("v-uni-image",{staticClass:"receive",attrs:{src:"/static/images/coupon_receive.png"}}):t._e()],1),e.tips?i("v-uni-view",{staticClass:"bg-white",staticStyle:{padding:"14rpx 20rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onShowTips(a)}}},[i("v-uni-view",{staticClass:"row-between"},[i("v-uni-view",{staticClass:"xs"},[t._v("使用说明")]),i("u-icon",{class:t.showTips[a]?"rotate":"",attrs:{name:"arrow-down"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showTips[a],expression:"showTips[index]"}],staticClass:"mt10 xs"},[t._v(t._s(e.tips))])],1):t._e()],1)})),1)},s=[]},f222:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.checkAllow=function(){return n.default.get("Market/isAllowJoin")},e.detail=function(t){return n.default.post("Model1/detail",t)},e.flList=function(t){return n.default.post("Model1/rebate",t)},e.getModel1Group=function(t){return n.default.get("user/myModel1",{params:t})},e.getModelInfo=function(t){return n.default.get("model1/getModel1info",{data:t})},e.getModelList=function(t){return n.default.get("model1/model1GoodsList",{params:t})},e.getTeamInfo=function(t){return n.default.get("model1/model1Info",{params:t})},e.teamBuy=function(t){return n.default.post("model1/buy",t)},e.teamCheck=function(t){return n.default.post("model1/check",t)};var n=a(i("35af"));i("73e7")},f371:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uPopup:i("c990").default,customImage:i("33fd").default,priceFormat:i("8718").default,uNumberBox:i("8609").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{mode:"bottom","border-radius":"14",closeable:!0,"safe-area-inset-bottom":!0},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.onClose.apply(void 0,arguments)}},model:{value:t.showPop,callback:function(e){t.showPop=e},expression:"showPop"}},[i("v-uni-view",{staticClass:"bg-white spec-contain"},[i("v-uni-view",{staticClass:"spec-header row"},[i("custom-image",{staticClass:"goods-img mr20",attrs:{radius:"10rpx",src:t.checkedGoods.image},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewImage(t.checkedGoods.image)}}}),t.isBargain?i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"xs"},[t._v("最低可砍至"),i("v-uni-text",{staticClass:"sm",staticStyle:{color:"#f95f2f"}},[t._v("¥"+t._s(t.checkedGoods.activity_price))])],1),i("v-uni-view",{staticClass:"muted xs mt10"},[t._v("原价 ¥"+t._s(t.checkedGoods.price))]),i("v-uni-view",{staticClass:"sm lighter mt20"},[i("v-uni-text",[t._v(t._s(t.specValueText))])],1)],1):i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"primary row"},[t.goods.score?i("v-uni-view",{staticStyle:{"font-size":"46rpx"}},[t._v(t._s(t.goods.score)+"贡献值")]):i("price-format",{attrs:{"first-size":46,"second-size":32,"subscript-size":32,price:t.group?t.checkedGoods.team_price:t.checkedGoods.price,weight:500}}),t.group||t.isSeckill||!t.checkedGoods.member_price||t.score?t._e():i("v-uni-view",{staticClass:"vip-price row"},[i("v-uni-view",{staticClass:"price-name xxs"},[t._v("会员价")]),i("v-uni-view",{staticStyle:{padding:"0 11rpx"}},[i("price-format",{attrs:{price:t.checkedGoods.member_price,"first-size":22,"second-size":22,"subscript-size":22,weight:500,color:"#7B3200"}})],1)],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showStock,expression:"showStock"}],staticClass:"sm"},[t._v("库存："+t._s(t.checkedGoods.stock)+"件")]),i("v-uni-view",{staticClass:"sm"},[i("v-uni-text",[t._v(t._s(t.specValueText))])],1)],1)],1),i("v-uni-view",{staticClass:"spec-main"},[i("v-uni-scroll-view",{staticStyle:{"max-height":"600rpx"},attrs:{"scroll-y":"true"}},[i("v-uni-view",{staticClass:"spec-list"},t._l(t.specList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"spec"},[i("v-uni-view",{staticClass:"name mb20"},[t._v(t._s(e.name)),i("v-uni-text",{staticClass:"primary xs ml20"},[t._v(t._s(""==t.checkedGoods.spec_value_ids_arr[a]?"请选择"+e.name:""))])],1),i("v-uni-view",{staticClass:"row wrap"},t._l(e.spec_value,(function(e,n){return i("v-uni-view",{key:n,class:"spec-item sm "+(t.checkedGoods.spec_value_ids_arr[a]==e.id?"checked":"")+(t.isDisable(e.id)?"disabled":""),on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.choseSpecItem(a,n)}}},[t._v(t._s(e.value))])})),1)],1)})),1)],1),i("v-uni-view",{staticClass:"good-num row-between ml20 mr20"},[i("v-uni-view",{staticClass:"label"},[t._v("数量")]),i("u-number-box",{attrs:{min:1,max:t.checkedGoods.stock,disabled:t.disabledNumberBox},model:{value:t.goodsNum,callback:function(e){t.goodsNum=e},expression:"goodsNum"}})],1)],1),i("v-uni-view",{staticClass:"btns row-between bg-white",class:-1!=t.specValueText.indexOf("请选择")||0==t.checkedGoods.stock?"disabled":""},[t.showAdd?i("v-uni-button",{staticClass:"add-cart br60 white btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("addcart")}}},[t._v(t._s(t.yellowBtnText))]):t._e(),t.showBuy?i("v-uni-button",{staticClass:"bg-primary br60 white btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("buynow")}}},[t._v(t._s(t.redBtnText))]):t._e(),t.showConfirm?i("v-uni-button",{staticClass:"bg-primary br60 white btn",attrs:{size:"lg"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("confirm")}}},[t._v("确定")]):t._e()],1)],1)],1)},s=[]},f3f7:function(t,e,i){"use strict";i("53f7")},f5bb:function(t,e,i){var a=i("a8d4");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("a0987a40",a,!0,{sourceMap:!1,shadowMode:!1})},fa4f:function(t,e,i){"use strict";var a=i("4555"),n=i.n(a);n.a},fa7aa:function(t,e,i){"use strict";i.r(e);var a=i("210e"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},fb92:function(t,e,i){"use strict";var a=i("0fec"),n=i.n(a);n.a},ff79a:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.show?i("v-uni-view",{staticClass:"u-badge",class:[t.isDot?"u-badge-dot":"","mini"==t.size?"u-badge-mini":"",t.type?"u-badge--bg--"+t.type:""],style:[{top:t.offset[0]+"rpx",right:t.offset[1]+"rpx",fontSize:t.fontSize+"rpx",position:t.absolute?"absolute":"static",color:t.color,backgroundColor:t.bgColor},t.boxStyle]},[t._v(t._s(t.showText))]):t._e()},n=[]}}]);