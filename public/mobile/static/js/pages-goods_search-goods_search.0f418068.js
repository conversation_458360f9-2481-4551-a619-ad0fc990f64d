(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-goods_search-goods_search"],{"0784":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{type:{type:String,default:"double"},list:{type:Array,default:function(){return[]}},isBargain:{type:Boolean,default:!1}},data:function(){return{baseUrl:this.baseUrl}}};e.default=a},"0c36":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-search[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center;flex:1;padding:%?15?% %?20?%}.u-content[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center;padding:0 %?18?%;flex:1}.u-clear-icon[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center}.u-input[data-v-3c66e606]{flex:1;font-size:%?28?%;line-height:1;margin:0 %?10?%;color:#909399}.u-close-wrap[data-v-3c66e606]{width:%?40?%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;border-radius:50%}.u-placeholder-class[data-v-3c66e606]{color:#909399}.u-action[data-v-3c66e606]{font-size:%?28?%;color:#303133;width:0;overflow:hidden;transition:all .3s;white-space:nowrap;text-align:center}.u-action-active[data-v-3c66e606]{width:%?80?%;margin-left:%?10?%}',""]),t.exports=e},"0eea":function(t,e,i){"use strict";var a=i("ad48"),o=i.n(a);o.a},"18ab":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={data:function(){return{}},components:{},props:{color:{type:String,default:""},direction:{type:String},size:{type:String},opacity:{type:String,default:"0.8"}},methods:{}};e.default=a},1976:function(t,e,i){"use strict";i.r(e);var a=i("749b"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},"1afe":function(t,e,i){"use strict";i.r(e);var a=i("6c39"),o=i("fc92");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("4b0d");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"3573b013",null,!1,a["a"],void 0);e["default"]=r.exports},"1f01":function(t,e,i){var a=i("c73d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("0f94b586",a,!0,{sourceMap:!1,shadowMode:!1})},"25f4":function(t,e,i){"use strict";i.r(e);var a=i("dc8e"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},"2a9e":function(t,e,i){"use strict";i.r(e);var a=i("f159"),o=i("bff5");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("3c79");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"c7e3d2d2",null,!1,a["a"],void 0);e["default"]=r.exports},"2b31":function(t,e,i){"use strict";i.r(e);var a=i("e54d"),o=i("e128");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("0eea");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"3c66e606",null,!1,a["a"],void 0);e["default"]=r.exports},"2cab":function(t,e,i){"use strict";var a=i("885c"),o=i.n(a);o.a},"33d9":function(t,e,i){"use strict";i.r(e);var a=i("a8ad"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},"33fd":function(t,e,i){"use strict";i.r(e);var a=i("5530"),o=i("25f4");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("a84e");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"07bfc4b2",null,!1,a["a"],void 0);e["default"]=r.exports},3435:function(t,e,i){var a=i("cbde");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("b2b3afce",a,!0,{sourceMap:!1,shadowMode:!1})},3480:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"u-search",props:{shape:{type:String,default:"round"},bgColor:{type:String,default:"#f2f2f2"},placeholder:{type:String,default:"请输入关键字"},clearabled:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},showAction:{type:Boolean,default:!0},actionStyle:{type:Object,default:function(){return{}}},actionText:{type:String,default:"搜索"},inputAlign:{type:String,default:"left"},disabled:{type:Boolean,default:!1},animation:{type:Boolean,default:!1},borderColor:{type:String,default:"none"},value:{type:String,default:""},height:{type:[Number,String],default:64},inputStyle:{type:Object,default:function(){return{}}},maxlength:{type:[Number,String],default:"-1"},searchIconColor:{type:String,default:""},color:{type:String,default:"#606266"},placeholderColor:{type:String,default:"#909399"},margin:{type:String},searchIcon:{type:String,default:"search"},wrapBgColor:{type:String,default:"#fff"},hideRight:{type:Boolean,default:!1}},data:function(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword:function(t){this.$emit("input",t),this.$emit("change",t)},value:{immediate:!0,handler:function(t){this.keyword=t}}},computed:{showActionBtn:function(){return!(this.animation||!this.showAction)},borderStyle:function(){return this.borderColor?"1px solid ".concat(this.borderColor):"none"}},methods:{inputChange:function(t){this.keyword=t.detail.value},clear:function(){var t=this;this.keyword="",this.$nextTick((function(){t.$emit("clear")}))},search:function(t){this.$emit("search",t.detail.value);try{uni.hideKeyboard()}catch(t){}},custom:function(){this.$emit("custom",this.keyword);try{uni.hideKeyboard()}catch(t){}},getFocus:function(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur:function(){var t=this;setTimeout((function(){t.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler:function(){this.disabled&&this.$emit("click")}}};e.default=a},"34d8":function(t,e,i){"use strict";i.r(e);var a=i("e3fa"),o=i("46f8");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("2cab");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"13a5beb4",null,!1,a["a"],void 0);e["default"]=r.exports},"3c79":function(t,e,i){"use strict";var a=i("3435"),o=i.n(a);o.a},"3e3a":function(t,e,i){"use strict";i.r(e);var a=i("a995"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},4126:function(t,e,i){"use strict";i.r(e);var a=i("58e7"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},"42c0":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".trigonometry[data-v-3573b013]{border-color:transparent transparent currentcolor currentcolor;border-style:solid;border-width:3px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);opacity:.8;margin:-3px %?10?% 0}.up[data-v-3573b013]{margin-top:%?1?%;-webkit-transform:rotate(135deg);transform:rotate(135deg)}.small[data-v-3573b013]{border-width:2px;margin-top:-2px}.small.up[data-v-3573b013]{margin-top:2px}",""]),t.exports=e},"46f8":function(t,e,i){"use strict";i.r(e);var a=i("c73c"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},"4b0d":function(t,e,i){"use strict";var a=i("f047"),o=i.n(a);o.a},5530:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uIcon:i("5b98").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{class:{"custom-image":!0,"image-round":t.round},style:[t.viewStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.error?t._e():i("v-uni-image",{staticClass:"image",attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoaded.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrored.apply(void 0,arguments)}}}),t.loading&&t.showLoading?i("v-uni-view",{staticClass:"loading-wrap image"},[t.useLoadingSlot?t._t("loading"):i("u-icon",{attrs:{color:"#aaa",name:"photo-fill",size:"45"}})],2):t._e(),t.error&&t.showError?i("v-uni-view",{staticClass:"error-wrap image"},[t.useErrorSlot?t._t("error"):i("u-icon",{attrs:{color:"#aaa",name:"error-circle-fill",size:"45"}}),i("v-uni-text",{staticClass:"sm"},[t._v("加载失败")])],2):t._e()],1)},s=[]},"565a":function(t,e,i){"use strict";i.r(e);var a=i("ec50"),o=i("3e3a");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("76d2");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"6dade8d3",null,!1,a["a"],void 0);e["default"]=r.exports},"580c":function(t,e,i){var a=i("8cc4");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("79a8583f",a,!0,{sourceMap:!1,shadowMode:!1})},"58e7":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(i("2634")),s=a(i("2fdc")),n=i("695b"),r=i("73e7"),c=i("1782"),d={data:function(){return{baseUrl:this.baseUrl,keyword:"",status:c.loadingType.LOADING,page:1,goodsType:"double",goodsList:[],priceSort:"",saleSort:"",showHistory:!1,hotList:[],historyList:[]}},components:{},props:{},watch:{keyword:function(t,e){t||this.id||(this.showHistory=!0)},showHistory:function(t){t&&this.getSearchpageFun()}},computed:{comprehensive:function(){var t=this.priceSort,e=this.saleSort;return""==t&&""==e}},onLoad:function(t){this.onNormal=(0,r.trottle)(this.onNormal,500,this),this.onPriceSort=(0,r.trottle)(this.onPriceSort,500,this),this.onSaleSort=(0,r.trottle)(this.onSaleSort,500,this),this.onSearch=(0,r.trottle)(this.onSearch,500,this),this.init(t)},onReachBottom:function(){this.getGoodsSearchFun()},methods:{onChange:function(t){this.keyword=t.value},changeType:function(){this.goodsType="one"===this.goodsType?"double":"one"},clearSearchFun:function(){var t=this;(0,n.clearSearch)().then((function(e){1==e.code&&t.getSearchpageFun()}))},onNormal:function(){this.priceSort="",this.saleSort="",this.onRefresh()},onPriceSort:function(){var t=this.priceSort;this.saleSort="",this.priceSort="asc"==t?"desc":"asc",this.onRefresh()},onSaleSort:function(){var t=this.saleSort;this.priceSort="",this.saleSort="desc"==t?"asc":"desc",this.onSearch()},init:function(t){var e=t.id,i=t.name,a=t.type;this.type=a,e?(uni.setNavigationBarTitle({title:i}),this.id=e,this.getGoodsSearchFun()):(uni.setNavigationBarTitle({title:"搜索"}),this.showHistory=!0)},getSearchpageFun:function(){var t=this;(0,n.getSearchpage)().then((function(e){if(1==e.code){var i=e.data,a=i.history_lists,o=i.hot_lists;t.hotList=o,t.historyList=a}}))},onClear:function(){this.id&&this.onSearch()},onSearch:function(){this.onRefresh()},onRefresh:function(){var t=this;this.showHistory=!1,this.page=1,this.goodsList=[],this.status=c.loadingType.LOADING,this.$nextTick((function(){t.getGoodsSearchFun()}))},onChangeKeyword:function(t){this.keyword=t,this.showHistory=!1,this.onRefresh()},getGoodsSearchFun:function(){var t=this;return(0,s.default)((0,o.default)().mark((function e(){var i,a,s,d,l,u,f,p;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=t.page,a=t.goodsList,s=t.keyword,d=t.priceSort,l=t.saleSort,u=t.status,u!=c.loadingType.FINISHED){e.next=3;break}return e.abrupt("return");case 3:return f={category_id:1==t.type?t.id:"",brand_id:0==t.type?t.id:"",page_no:i,keyword:s,price:d,sales_sum:l},e.next=6,(0,r.loadingFun)(n.getGoodsSearch,i,a,u,f);case 6:if(p=e.sent,p){e.next=9;break}return e.abrupt("return");case 9:t.page=p.page,t.goodsList=p.dataList,t.status=p.status;case 12:case"end":return e.stop()}}),e)})))()}}};e.default=d},"59dc":function(t,e,i){"use strict";var a=i("697b"),o=i.n(a);o.a},"697b":function(t,e,i){var a=i("6b27");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("6ba7ab65",a,!0,{sourceMap:!1,shadowMode:!1})},"6b27":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".price-format[data-v-60f6159f]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=e},"6b39":function(t,e,i){"use strict";var a=i("1f01"),o=i.n(a);o.a},"6c39":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:"trigonometry "+("up"===this.direction?"up":"")+" "+("small"===this.size?"small":""),style:"color:"+this.color+";opacity: "+this.opacity})},o=[]},"749b":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={data:function(){return{}},components:{},props:{status:{type:String,default:"loading"},errorText:{type:String,default:"加载失败，点击重新加载"},loadingText:{type:String,default:"加载中..."},finishedText:{type:String,default:"我可是有底线的～"},slotEmpty:{type:Boolean,default:!1},color:{type:String,default:"#666"}},methods:{onRefresh:function(){this.$emit("refresh")}}};e.default=a},"76d2":function(t,e,i){"use strict";var a=i("580c"),o=i.n(a);o.a},"7ed6":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?i("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),i("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?i("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},o=[]},8718:function(t,e,i){"use strict";i.r(e);var a=i("7ed6"),o=i("33d9");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("59dc");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"60f6159f",null,!1,a["a"],void 0);e["default"]=r.exports},"881e":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'[data-v-13a5beb4]:host{font-size:0;line-height:1}.loading[data-v-13a5beb4]{display:inline-flex;align-items:center;justify-content:center;color:#c8c9cc}.loading__spinner[data-v-13a5beb4]{position:relative;box-sizing:border-box;width:%?45?%;max-width:100%;max-height:100%;height:%?45?%;-webkit-animation:rotate-data-v-13a5beb4 .8s linear infinite;animation:rotate-data-v-13a5beb4 .8s linear infinite}.loading__spinner--spinner[data-v-13a5beb4]{-webkit-animation-timing-function:steps(12);animation-timing-function:steps(12)}.loading__spinner--circular[data-v-13a5beb4]{border:%?2?% solid transparent;border-top-color:initial;border-radius:100%}.loading__text[data-v-13a5beb4]{margin-left:%?16?%;color:#969799;font-size:%?28?%;line-height:%?40?%}.loading__text[data-v-13a5beb4]:empty{display:none}.loading--vertical[data-v-13a5beb4]{-webkit-flex-direction:column;flex-direction:column}.loading--vertical .loading__text[data-v-13a5beb4]{margin:%?16?% 0 0}.loading__dot[data-v-13a5beb4]{position:absolute;top:0;left:0;width:100%;height:100%}.loading__dot[data-v-13a5beb4]:before{display:block;width:%?4?%;height:25%;margin:0 auto;background-color:currentColor;border-radius:40%;content:" "}.loading__dot[data-v-13a5beb4]:first-of-type{-webkit-transform:rotate(30deg);transform:rotate(30deg);opacity:1}.loading__dot[data-v-13a5beb4]:nth-of-type(2){-webkit-transform:rotate(60deg);transform:rotate(60deg);opacity:.9375}.loading__dot[data-v-13a5beb4]:nth-of-type(3){-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:.875}.loading__dot[data-v-13a5beb4]:nth-of-type(4){-webkit-transform:rotate(120deg);transform:rotate(120deg);opacity:.8125}.loading__dot[data-v-13a5beb4]:nth-of-type(5){-webkit-transform:rotate(150deg);transform:rotate(150deg);opacity:.75}.loading__dot[data-v-13a5beb4]:nth-of-type(6){-webkit-transform:rotate(180deg);transform:rotate(180deg);opacity:.6875}.loading__dot[data-v-13a5beb4]:nth-of-type(7){-webkit-transform:rotate(210deg);transform:rotate(210deg);opacity:.625}.loading__dot[data-v-13a5beb4]:nth-of-type(8){-webkit-transform:rotate(240deg);transform:rotate(240deg);opacity:.5625}.loading__dot[data-v-13a5beb4]:nth-of-type(9){-webkit-transform:rotate(270deg);transform:rotate(270deg);opacity:.5}.loading__dot[data-v-13a5beb4]:nth-of-type(10){-webkit-transform:rotate(300deg);transform:rotate(300deg);opacity:.4375}.loading__dot[data-v-13a5beb4]:nth-of-type(11){-webkit-transform:rotate(330deg);transform:rotate(330deg);opacity:.375}.loading__dot[data-v-13a5beb4]:nth-of-type(12){-webkit-transform:rotate(1turn);transform:rotate(1turn);opacity:.3125}@-webkit-keyframes rotate-data-v-13a5beb4{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes rotate-data-v-13a5beb4{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},"885c":function(t,e,i){var a=i("881e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("262c9b88",a,!0,{sourceMap:!1,shadowMode:!1})},"8cc4":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-sticky[data-v-6dade8d3]{z-index:9999999999}',""]),t.exports=e},"95ba":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uSticky:i("565a").default,uSearch:i("2b31").default,trigonometry:i("1afe").default,goodsList:i("2a9e").default,loadingFooter:i("d77c").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-search"},[i("v-uni-view",{staticClass:"header-wrap"},[i("u-sticky",{attrs:{"offset-top":"0","h5-nav-height":"0"}},[i("v-uni-view",{staticClass:"search"},[i("u-search",{attrs:{focus:t.showHistory,"bg-color":"#F4F4F4"},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.showHistory=!0},search:function(e){arguments[0]=e=t.$handleEvent(e),t.onSearch.apply(void 0,arguments)}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!t.showHistory,expression:"!showHistory"}],staticClass:"header row bg-white"},[i("v-uni-view",{class:"tag row-center "+(t.comprehensive?"primary":""),on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onNormal.apply(void 0,arguments)}}},[t._v("综合")]),i("v-uni-view",{staticClass:"tag row-center",attrs:{"data-type":"priceSort"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onPriceSort.apply(void 0,arguments)}}},[i("v-uni-text",{class:t.priceSort?"primary":""},[t._v("价格")]),i("v-uni-view",[i("trigonometry",{attrs:{direction:"up",size:"small",color:"desc"==t.priceSort?"#FF5058":"#333"}}),i("trigonometry",{attrs:{size:"small",color:"asc"==t.priceSort?"#FF5058":"#333"}})],1)],1),i("v-uni-view",{staticClass:"tag row-center",attrs:{"data-type":"saleSort"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSaleSort.apply(void 0,arguments)}}},[i("v-uni-text",{class:t.saleSort?"primary":""},[t._v("销量")]),i("v-uni-view",[i("trigonometry",{attrs:{direction:"up",size:"small",color:"desc"==t.saleSort?"#FF5058":"#333"}}),i("trigonometry",{attrs:{size:"small",color:"asc"==t.saleSort?"#FF5058":"#333"}})],1)],1),i("v-uni-view",{staticClass:"tag row-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeType.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"icon-sm",attrs:{src:"one"===t.goodsType?t.baseUrl+"/image/icon_double.png":t.baseUrl+"/image/icon_one.png"}})],1)],1)],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showHistory,expression:"showHistory"}],staticClass:"content bg-white"},[t.hotList.length?i("v-uni-view",{staticClass:"search-words"},[i("v-uni-view",{staticClass:"title"},[t._v("热门搜索")]),i("v-uni-view",{staticClass:"words row wrap"},t._l(t.hotList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"item br60  mr20 mb20 lighter sm line1",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onChangeKeyword(e)}}},[t._v(t._s(e))])})),1)],1):t._e(),t.historyList.length?i("v-uni-view",{staticClass:"search-words"},[i("v-uni-view",{staticClass:"title row-between"},[i("v-uni-view",[t._v("历史搜索")]),i("v-uni-view",{staticClass:"xs muted mr20",staticStyle:{padding:"10rpx 20rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clearSearchFun.apply(void 0,arguments)}}},[t._v("清空")])],1),i("v-uni-view",{staticClass:"words row wrap"},t._l(t.historyList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"item br60  mr20 mb20 lighter sm line1",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onChangeKeyword(e)}}},[t._v(t._s(e))])})),1)],1):t._e()],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!t.showHistory,expression:"!showHistory"}],staticClass:"content"},[i("v-uni-view",{staticClass:"goods-list"},[i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"double"==t.goodsType,expression:"goodsType == 'double'"}],staticClass:"double"},[i("goods-list",{attrs:{type:"double",list:t.goodsList}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"one"==t.goodsType,expression:"goodsType == 'one'"}],staticClass:"one"},[i("goods-list",{attrs:{list:t.goodsList,type:"one"}})],1)],1),i("loading-footer",{attrs:{status:t.status,"slot-empty":!0}},[i("v-uni-view",{staticClass:"column-center",staticStyle:{"padding-top":"200rpx"},attrs:{slot:"empty"},slot:"empty"},[i("v-uni-image",{staticClass:"img-null",attrs:{src:t.baseUrl+"/image/goods_null.png"}}),i("v-uni-text",{staticClass:"lighter"},[t._v("暂无商品")])],1)],1)],1)],1)},s=[]},"9fd1":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={loading:i("34d8").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"loading-footer row-center",style:"color: "+t.color},["loading"===t.status?i("v-uni-view",{staticClass:"loading row"},[i("loading",{staticClass:"mr20",attrs:{color:t.color}}),i("v-uni-text",{style:"color: "+t.color},[t._v(t._s(t.loadingText))])],1):t._e(),"finished"===t.status?i("v-uni-view",{staticClass:"finished"},[t._v(t._s(t.finishedText))]):t._e(),"error"===t.status?i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onRefresh.apply(void 0,arguments)}}},[t._v(t._s(t.errorText))]):t._e(),"empty"===t.status?i("v-uni-view",{staticClass:"empty"},[t.slotEmpty?t._t("empty"):i("v-uni-text",[t._v("暂无数据")])],2):t._e()],1)},s=[]},a84e:function(t,e,i){"use strict";var a=i("fbc3"),o=i.n(a);o.a},a8ad:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("e838");var a={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&""!==t&&void 0!==t&&(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e)}}};e.default=a},a995:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("9c4e");var a={name:"u-sticky",props:{offsetTop:{type:[Number,String],default:0},index:{type:[Number,String],default:""},enable:{type:Boolean,default:!0},h5NavHeight:{type:[Number,String],default:44},bgColor:{type:String,default:"#ffffff"},zIndex:{type:[Number,String],default:""}},data:function(){return{fixed:!1,height:"auto",stickyTop:0,elClass:this.$u.guid(),left:0,width:"auto"}},watch:{offsetTop:function(t){this.initObserver()},enable:function(t){0==t?(this.fixed=!1,this.disconnectObserver("contentObserver")):this.initObserver()}},computed:{uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.sticky}},mounted:function(){this.initObserver()},methods:{initObserver:function(){var t=this;this.enable&&(this.stickyTop=0!=this.offsetTop?uni.upx2px(this.offsetTop)+this.h5NavHeight:this.h5NavHeight,this.disconnectObserver("contentObserver"),this.$uGetRect("."+this.elClass).then((function(e){t.height=e.height,t.left=e.left,t.width=e.width,t.$nextTick((function(){t.observeContent()}))})))},observeContent:function(){var t=this;this.disconnectObserver("contentObserver");var e=this.createIntersectionObserver({thresholds:[.95,.98,1]});e.relativeToViewport({top:-this.stickyTop}),e.observe("."+this.elClass,(function(e){t.enable&&t.setFixed(e.boundingClientRect.top)})),this.contentObserver=e},setFixed:function(t){var e=t<this.stickyTop;e?this.$emit("fixed",this.index):this.fixed&&this.$emit("unfixed",this.index),this.fixed=e},disconnectObserver:function(t){var e=this[t];e&&e.disconnect()}},beforeDestroy:function(){this.disconnectObserver("contentObserver")}};e.default=a},ad48:function(t,e,i){var a=i("0c36");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("be9187ac",a,!0,{sourceMap:!1,shadowMode:!1})},b747:function(t,e,i){var a=i("c099");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("1dfb52f0",a,!0,{sourceMap:!1,shadowMode:!1})},bed8:function(t,e,i){"use strict";i.r(e);var a=i("95ba"),o=i("4126");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("e93a");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"b92c7bca",null,!1,a["a"],void 0);e["default"]=r.exports},bff5:function(t,e,i){"use strict";i.r(e);var a=i("0784"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},c099:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-search .header-wrap .search[data-v-b92c7bca]{box-shadow:0 3px 6px rgba(0,0,0,.03);position:relative;z-index:1}.goods-search .header-wrap .header[data-v-b92c7bca]{height:%?80?%}.goods-search .header-wrap .header .tag[data-v-b92c7bca]{height:100%;flex:1}.goods-search .content .search-words[data-v-b92c7bca]{padding-left:%?24?%;padding-bottom:%?20?%}.goods-search .content .search-words .title[data-v-b92c7bca]{padding:%?26?% 0}.goods-search .content .search-words .words .item[data-v-b92c7bca]{line-height:%?52?%;height:%?52?%;padding:0 %?24?%;background-color:#f5f5f5}.goods-search .content .goods-list[data-v-b92c7bca]{overflow:hidden}',""]),t.exports=e},c73c:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("08eb"),i("18f7");var a={props:{color:String,vertical:Boolean,type:{type:String,default:"spinner"},size:{type:Number,default:40},textSize:String},data:function(){return{array12:Array.from({length:12})}}};e.default=a},c73d:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".loading-footer[data-v-6dc30303]{padding:%?30?% 0;color:#666}",""]),t.exports=e},cbde:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-list .goods-double[data-v-c7e3d2d2]{flex-wrap:wrap;padding:0 %?20?%;align-items:stretch}.goods-list .goods-double .item[data-v-c7e3d2d2]{width:%?347?%;border-radius:%?10?%}.goods-list .goods-double .item .goods-info[data-v-c7e3d2d2]{padding:%?10?%}.goods-list .goods-hot.goods-home-hot .item[data-v-c7e3d2d2]{padding:0}.goods-list .goods-hot.goods-home-hot .item .paixu[data-v-c7e3d2d2],\n.goods-list .goods-hot.goods-home-hot .item .number[data-v-c7e3d2d2]{left:%?10?%}.goods-list .goods-hot .item[data-v-c7e3d2d2]{position:relative;padding:%?30?% %?20?%;border-radius:%?10?%}.goods-list .goods-hot .item .goods-info[data-v-c7e3d2d2]{width:%?450?%}.goods-list .goods-hot .item .goods-info .sale[data-v-c7e3d2d2]{padding:%?4?% %?18?%;color:#f79c0c;background-color:rgba(247,156,12,.1)}.goods-list .goods-hot .item .paixu[data-v-c7e3d2d2],\n.goods-list .goods-hot .item .number[data-v-c7e3d2d2]{position:absolute;top:0;left:%?27?%;width:%?50?%;height:%?54?%;line-height:%?60?%;text-align:center;color:#621e09}.goods-list .goods-one .item[data-v-c7e3d2d2]{padding:%?20?%}.goods-list .goods-one .item[data-v-c7e3d2d2]:not(:last-of-type){margin-bottom:%?20?%}.goods-list .goods-new .item[data-v-c7e3d2d2]{box-shadow:0 0 10px rgba(0,0,0,.16);border-radius:%?10?%}',""]),t.exports=e},d77c:function(t,e,i){"use strict";i.r(e);var a=i("9fd1"),o=i("1976");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("6b39");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"6dc30303",null,!1,a["a"],void 0);e["default"]=r.exports},dc8e:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{src:{type:String},round:Boolean,width:{type:null},height:{type:null},radius:null,lazyLoad:{type:Boolean,default:!0},useErrorSlot:Boolean,useLoadingSlot:Boolean,showMenuByLongpress:Boolean,mode:{type:String,default:"scaleToFill"},showError:{type:Boolean,default:!0},showLoading:{type:Boolean,default:!0},customStyle:{type:Object,default:function(){}}},data:function(){return{error:!1,loading:!0,viewStyle:{}}},created:function(){this.setStyle()},methods:{setStyle:function(){var t=this.width,e=this.height,i=this.radius,a={};t&&(a.width=t),e&&(a.height=e),i&&(a["overflow"]="hidden",a["border-radius"]=i),this.viewStyle=a,this.customStyle&&(this.viewStyle=Object.assign(this.viewStyle,this.customStyle))},onLoaded:function(t){this.loading=!1,this.$emit("load",t.detail)},onErrored:function(t){this.error=!1,this.loading=!0,this.$emit("error",t.detail)},onClick:function(t){this.$emit("click",t.detail)}},watch:{src:function(){this.error=!1,this.loading=!0},width:function(){this.setStyle()},height:function(){this.setStyle()}}};e.default=a},e128:function(t,e,i){"use strict";i.r(e);var a=i("3480"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},e3fa:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{class:"loading "+(t.vertical?"loading--vertical":"")},[i("v-uni-view",{class:"loading__spinner loading__spinner--"+t.type,style:{color:t.color,width:t.size+"rpx",height:t.size+"rpx"}},t._l(t.array12,(function(e,a){return"spinner"===t.type?i("v-uni-view",{key:a,staticClass:"loading__dot"}):t._e()})),1),i("v-uni-view",{staticClass:"loading__text",style:{"font-size":t.textSize+"rpx",color:t.color}},[t._t("default")],2)],1)},o=[]},e54d:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uIcon:i("5b98").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-search",style:{margin:t.margin,backgroundColor:t.wrapBgColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-content",style:{backgroundColor:t.bgColor,borderRadius:"round"==t.shape?"100rpx":"10rpx",border:t.borderStyle,height:t.height+"rpx"}},[i("v-uni-view",{staticClass:"u-icon-wrap"},[i("u-icon",{staticClass:"u-clear-icon",attrs:{size:30,name:t.searchIcon,color:t.searchIconColor?t.searchIconColor:t.color}})],1),i("v-uni-input",{staticClass:"u-input",style:[{textAlign:t.inputAlign,color:t.color,backgroundColor:t.bgColor},t.inputStyle],attrs:{"confirm-type":"search",value:t.value,disabled:t.disabled,focus:t.focus,maxlength:t.maxlength,"placeholder-class":"u-placeholder-class",placeholder:t.placeholder,"placeholder-style":"color: "+t.placeholderColor,type:"text"},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.blur.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.inputChange.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.getFocus.apply(void 0,arguments)}}}),t.keyword&&t.clearabled&&t.focused?i("v-uni-view",{staticClass:"u-close-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[i("u-icon",{staticClass:"u-clear-icon",attrs:{name:"close-circle-fill",size:"34",color:"#c0c4cc"}})],1):t._e()],1),t.hideRight?i("v-uni-view",{staticClass:"u-action",class:[t.showActionBtn||t.show?"u-action-active":""],style:[t.actionStyle],on:{click:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.custom.apply(void 0,arguments)}}},[t._v(t._s(t.actionText))]):t._e()],1)},s=[]},e93a:function(t,e,i){"use strict";var a=i("b747"),o=i.n(a);o.a},ec50:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{},[i("v-uni-view",{staticClass:"u-sticky-wrap",class:[t.elClass],style:{height:t.fixed?t.height+"px":"auto",backgroundColor:t.bgColor}},[i("v-uni-view",{staticClass:"u-sticky",style:{position:t.fixed?"fixed":"static",top:t.stickyTop+"px",left:t.left+"px",width:"auto"==t.width?"auto":t.width+"px",zIndex:t.uZIndex}},[t._t("default")],2)],1)],1)},o=[]},f047:function(t,e,i){var a=i("42c0");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("3bd44978",a,!0,{sourceMap:!1,shadowMode:!1})},f159:function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={customImage:i("33fd").default,priceFormat:i("8718").default},o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-list"},["double"===t.type?i("v-uni-view",{staticClass:"goods-double row-between"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+(t.isBargain?e.goods_id:e.id)}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"347rpx",height:"347rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"347rpx",height:"347rpx",radius:"10rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"goods-name line2"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price||e.activity_price}})],1)],1)],1)})),1):t._e(),"model2"===t.type?i("v-uni-view",{staticClass:"goods-hot"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/model2_details?id="+e.id+"&order_type=5"}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"180rpx",height:"180rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"180rpx",height:"180rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-text",{staticClass:"sale br60 xxs"},[t._v("已有"+t._s(e.sales_sum)+"人购买")]),i("v-uni-view",{staticClass:"row-between  mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1),i("v-uni-image",{staticClass:"icon-md",attrs:{src:"/static/images/icon_go_red.png"}})],1)],1)],1)})),1):t._e(),"hot"===t.type?i("v-uni-view",{staticClass:"goods-hot"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"180rpx",height:"180rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"180rpx",height:"180rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-text",{staticClass:"sale br60 xxs"},[t._v("已有"+t._s(e.sales_sum)+"人购买")]),i("v-uni-view",{staticClass:"row-between  mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1),i("v-uni-image",{staticClass:"icon-md",attrs:{src:"/static/images/icon_go_red.png"}})],1)],1),i("v-uni-image",{staticClass:"paixu",attrs:{src:t.baseUrl+"/image/No."+(a<3?a:3)+".png"}}),i("v-uni-view",{staticClass:"number xxs"},[t._v(t._s(a+1))])],1)})),1):t._e(),"home-hot"===t.type?i("v-uni-view",{staticClass:"goods-home-hot goods-hot"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mb20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img"},[i("custom-image",{attrs:{"lazy-load":!0,width:"240rpx",height:"240rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20 mr20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-text",{staticClass:"sale br60 xxs"},[t._v("已有"+t._s(e.sales_sum)+"人购买")]),i("v-uni-view",{staticClass:"row-between  mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1),i("v-uni-button",{staticClass:"br60",attrs:{type:"primary",size:"xs"}},[t._v("去购买")])],1)],1),i("v-uni-image",{staticClass:"paixu",attrs:{src:t.baseUrl+"/image/No."+(a<3?a:3)+".png"}}),i("v-uni-view",{staticClass:"number"},[t._v(t._s(a+1))])],1)})),1):t._e(),"new"===t.type?i("v-uni-view",{staticClass:"goods-new"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img"},[i("custom-image",{attrs:{"lazy-load":!0,width:"240rpx",height:"240rpx",radius:"10rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20 mr20 flex1"},[i("v-uni-view",{staticClass:"goods-name line2 mb20"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"row-between muted xxs "},[i("v-uni-view",{staticClass:"line-through"},[i("v-uni-text",[t._v("原价")]),i("price-format",{attrs:{"second-size":22,"first-size":22,"subscript-size":22,price:e.market_price}})],1),i("v-uni-view",[t._v(t._s(e.sales_sum)+"人购买")])],1),i("v-uni-view",{staticClass:"row-between  mt10"},[i("price-format",{attrs:{color:"#FF2C3C","first-size":38,"subscript-size":26,"second-size":26,price:e.price,weight:500}}),i("v-uni-button",{staticClass:"br60",attrs:{type:"primary",size:"xs"}},[t._v("立即抢购")])],1)],1)],1)})),1):t._e(),"one"===t.type?i("v-uni-view",{staticClass:"goods-one mt20"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"200rpx",height:"200rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"200rpx",height:"200rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"row-between mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1)],1)],1)],1)})),1):t._e()],1)},s=[]},faf8:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.custom-image[data-v-07bfc4b2]{position:relative;display:block;width:100%;height:100%}.custom-image.image-round[data-v-07bfc4b2]{overflow:hidden;border-radius:50%}.custom-image .image[data-v-07bfc4b2]{display:block;width:100%;height:100%}.custom-image .loading-wrap[data-v-07bfc4b2],\n.custom-image .error-wrap[data-v-07bfc4b2]{position:absolute;top:0;left:0;display:flex;flex-direction:column;align-items:center;justify-content:center;color:#969799;font-size:%?28?%;background-color:#f7f8fa}',""]),t.exports=e},fbc3:function(t,e,i){var a=i("faf8");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("f8b03a56",a,!0,{sourceMap:!1,shadowMode:!1})},fc92:function(t,e,i){"use strict";i.r(e);var a=i("18ab"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a}}]);