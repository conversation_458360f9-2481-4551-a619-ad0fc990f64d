(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-sort-sort"],{"0784":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{type:{type:String,default:"double"},list:{type:Array,default:function(){return[]}},isBargain:{type:Boolean,default:!1}},data:function(){return{baseUrl:this.baseUrl}}};e.default=a},"0a12":function(t,e,i){var a=i("866a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("78c5edca",a,!0,{sourceMap:!1,shadowMode:!1})},"0c36":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.u-search[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center;flex:1;padding:%?15?% %?20?%}.u-content[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center;padding:0 %?18?%;flex:1}.u-clear-icon[data-v-3c66e606]{display:flex;flex-direction:row;align-items:center}.u-input[data-v-3c66e606]{flex:1;font-size:%?28?%;line-height:1;margin:0 %?10?%;color:#909399}.u-close-wrap[data-v-3c66e606]{width:%?40?%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;border-radius:50%}.u-placeholder-class[data-v-3c66e606]{color:#909399}.u-action[data-v-3c66e606]{font-size:%?28?%;color:#303133;width:0;overflow:hidden;transition:all .3s;white-space:nowrap;text-align:center}.u-action-active[data-v-3c66e606]{width:%?80?%;margin-left:%?10?%}',""]),t.exports=e},"0eea":function(t,e,i){"use strict";var a=i("ad48"),n=i.n(a);n.a},"122b":function(t,e,i){"use strict";i.r(e);var a=i("3a4e"),n=i("3d01");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("3c06");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"52202d9d",null,!1,a["a"],void 0);e["default"]=r.exports},1569:function(t,e,i){"use strict";i.r(e);var a=i("e62a"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"165a":function(t,e,i){"use strict";i.r(e);var a=i("5196"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"18ab":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={data:function(){return{}},components:{},props:{color:{type:String,default:""},direction:{type:String},size:{type:String},opacity:{type:String,default:"0.8"}},methods:{}};e.default=a},"1afe":function(t,e,i){"use strict";i.r(e);var a=i("6c39"),n=i("fc92");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("4b0d");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"3573b013",null,!1,a["a"],void 0);e["default"]=r.exports},"1fd4":function(t,e,i){var a=i("78df");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("906232cc",a,!0,{sourceMap:!1,shadowMode:!1})},"22c8":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={swipers:i("d3b3").default,uIcon:i("5b98").default,uImage:i("4c0e").default,trigonometry:i("1afe").default,priceFormat:i("8718").default,loadingFooter:i("d77c").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"cate-four row"},[i("v-uni-view",{staticClass:"aside"},[i("v-uni-scroll-view",{staticStyle:{height:"100%"},attrs:{"scroll-y":"true","scroll-with-animation":"true"}},[t._l(t.cateList,(function(e,a){return[i("v-uni-view",{key:a+"_0",class:"one-item sm "+(a==t.selectIndex?"active":""),on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeActive(a)}}},[i("v-uni-text",{staticClass:"name"},[t._v(t._s(e.name))]),a==t.selectIndex?i("v-uni-view",{staticClass:"active-line bg-primary"}):t._e()],1)]}))],2)],1),i("v-uni-view",{staticClass:"main bg-body"},[i("v-uni-scroll-view",{staticStyle:{height:"100%"},attrs:{"scroll-y":"true","scroll-with-animation":"true"},on:{scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsSearchFun.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"main-wrap"},[i("v-uni-view",{staticStyle:{"border-radius":"10rpx"}},[i("swipers",{attrs:{pid:4,height:"200rpx","previous-margin":"0",padding:"20rpx 0 0",radius:"10rpx"}})],1),t.currentType?i("v-uni-view",{staticClass:"cate-two mt20"},t._l(t.cateTwoList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"two-item bg-white mb20"},[1==e.type?i("v-uni-navigator",{staticClass:"title row-between",attrs:{"hover-class":"none",url:"/pages/goods_search/goods_search?id="+e.id+"&name="+e.name+"&type="+e.type}},[i("v-uni-text",{staticClass:"name bold sm"},[t._v(t._s(e.name))]),i("u-icon",{attrs:{name:"arrow-right"}})],1):i("v-uni-view",{staticClass:"title row-between"},[i("v-uni-text",{staticClass:"name bold sm"},[t._v(t._s(e.name))])],1),i("v-uni-view",{staticClass:"three-list row wrap"},t._l(e.sons,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"three-item column-center mb20",attrs:{"hover-class":"none",url:"/pages/goods_search/goods_search?id="+e.id+"&name="+e.name+"&type="+e.type}},[i("u-image",{attrs:{mode:"aspectFit",width:"150rpx",height:"150rpx",src:e.image}}),i("v-uni-view",{staticClass:"text mt20 xs"},[t._v(t._s(e.name))])],1)})),1)],1)})),1):i("v-uni-view",{staticClass:"goods"},[i("v-uni-view",{staticClass:"row condition bg-white mt20"},[i("v-uni-view",{class:"tag row-center "+(t.comprehensive?"active":""),on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onNormal.apply(void 0,arguments)}}},[t._v("综合")]),i("v-uni-view",{staticClass:"tag row-center",class:t.priceSort?"active":"",attrs:{"data-type":"priceSort"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onPriceSort.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("价格")]),i("v-uni-view",[i("trigonometry",{attrs:{direction:"up",size:"small",color:"desc"==t.priceSort?"#FF5058":"#333"}}),i("trigonometry",{attrs:{size:"small",color:"asc"==t.priceSort?"#f79c0c":"#333"}})],1)],1),i("v-uni-view",{staticClass:"tag row-center",class:t.saleSort?"active":"",attrs:{"data-type":"saleSort"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSaleSort.apply(void 0,arguments)}}},[i("v-uni-text",[t._v("销量")]),i("v-uni-view",[i("trigonometry",{attrs:{direction:"up",size:"small",color:"desc"==t.saleSort?"#FF5058":"#333"}}),i("trigonometry",{attrs:{size:"small",color:"asc"==t.saleSort?"#f79c0c":"#333"}})],1)],1)],1),i("v-uni-view",{staticClass:"goods-list"},t._l(t.goodsList,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"row item bg-white mt20",attrs:{"hover-class":"none",url:"/pages/goods_details/goods_details?id="+e.id}},[i("u-image",{attrs:{width:"200rpx",height:"200rpx","border-radius":"14rpx",src:e.image}}),i("v-uni-view",{staticClass:"flex1 ml20 mr10"},[i("v-uni-view",{staticClass:"line2"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"muted"},[i("v-uni-text",{staticClass:"xxs"},[t._v("原价")]),i("price-format",{attrs:{subscriptSize:22,firstSize:22,secondSize:22,price:e.market_price}})],1),i("v-uni-view",{staticClass:"primary mt10"},[i("price-format",{attrs:{price:e.price,subscriptSize:22,firstSize:34,secondSize:26}})],1)],1)],1)})),1),i("loading-footer",{attrs:{status:t.status,"slot-empty":!0}},[i("v-uni-view",{staticClass:"column-center",staticStyle:{padding:"200rpx 0 0"},attrs:{slot:"empty"},slot:"empty"},[i("v-uni-image",{staticClass:"img-null",attrs:{src:"/static/images/goods_null.png"}}),i("v-uni-text",{staticClass:"lighter sm"},[t._v("暂无商品")])],1)],1)],1)],1)],1)],1)],1)},s=[]},"25f4":function(t,e,i){"use strict";i.r(e);var a=i("dc8e"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"29dd":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bd06");var n=a(i("2634")),s=a(i("2fdc")),o=i("73e7"),r=i("695b"),c=i("1782"),d={name:"cate-four",props:{list:{type:Array,default:function(){return[]}}},data:function(){return{page:1,status:c.loadingType.LOADING,selectIndex:0,cateList:[],cateTwoList:[],goodsList:[],priceSort:"",saleSort:""}},created:function(){this.onNormal=(0,o.trottle)(this.onNormal,500,this),this.onPriceSort=(0,o.trottle)(this.onPriceSort,500,this),this.onSaleSort=(0,o.trottle)(this.onSaleSort,500,this)},methods:{changeActive:function(t){var e=this.cateList;this.selectIndex=t,this.cateTwoList=e[this.selectIndex].sons||[],this.onRefresh()},onRefresh:function(){var t=this;this.page=1,this.goodsList=[],this.status=c.loadingType.LOADING,this.$nextTick((function(){t.getGoodsSearchFun()}))},onNormal:function(){this.priceSort="",this.saleSort="",this.onRefresh()},onPriceSort:function(){var t=this.priceSort;this.saleSort="",this.priceSort="asc"==t?"desc":"asc",this.onRefresh()},onSaleSort:function(){var t=this.saleSort;this.priceSort="",this.saleSort="desc"==t?"asc":"desc",this.onRefresh()},getGoodsSearchFun:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i,a,s,d,l,u,f,v,p,h;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=t.page,a=t.goodsList,s=t.priceSort,d=t.saleSort,l=t.status,u=t.cateList,f=t.selectIndex,v=u[f],0!=v.type){e.next=4;break}return e.abrupt("return");case 4:if(l!=c.loadingType.FINISHED){e.next=6;break}return e.abrupt("return");case 6:return p={category_id:v.id,page_no:i,price:s,sales_sum:d},e.next=9,(0,o.loadingFun)(r.getGoodsSearch,i,a,l,p);case 9:if(h=e.sent,h){e.next=12;break}return e.abrupt("return");case 12:t.page=h.page,t.goodsList=h.dataList,t.status=h.status;case 15:case"end":return e.stop()}}),e)})))()}},watch:{list:{handler:function(t){if(t.length){var e=t.findIndex((function(t){return 1==t.type}));this.selectIndex=-1==e?0:e,this.cateList=t,this.cateTwoList=t[this.selectIndex]?t[this.selectIndex].sons:[],this.getGoodsSearchFun()}}}},computed:{comprehensive:function(){var t=this.priceSort,e=this.saleSort;return""==t&&""==e},currentType:function(){var t=this.cateList,e=this.selectIndex;return!t[e]||0==t[e].type}}};e.default=d},"2a9e":function(t,e,i){"use strict";i.r(e);var a=i("f159"),n=i("bff5");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("3c79");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"c7e3d2d2",null,!1,a["a"],void 0);e["default"]=r.exports},"2b31":function(t,e,i){"use strict";i.r(e);var a=i("e54d"),n=i("e128");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("0eea");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"3c66e606",null,!1,a["a"],void 0);e["default"]=r.exports},"2b87":function(t,e,i){var a=i("6da3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("06873d3d",a,!0,{sourceMap:!1,shadowMode:!1})},"2cab":function(t,e,i){"use strict";var a=i("885c"),n=i.n(a);n.a},"2e3f":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uSearch:i("2b31").default,cateOne:i("6e67").default,cateTwo:i("783c").default,cateThree:i("122b").default,cateFour:i("92df").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"sort"},[i("v-uni-navigator",{staticClass:"header",attrs:{"hover-class":"none",url:"/pages/goods_search/goods_search"}},[i("u-search",{attrs:{"bg-color":"#F4F4F4",disabled:!0}})],1),i("v-uni-view",{staticClass:"content"},[2==t.appConfig.cate_style?i("cate-one",{attrs:{list:t.cateList}}):t._e(),3==t.appConfig.cate_style?i("cate-two",{attrs:{list:t.cateList}}):t._e(),4==t.appConfig.cate_style?i("cate-three",{attrs:{list:t.cateList}}):t._e(),1==t.appConfig.cate_style?i("cate-four",{attrs:{list:t.cateList}}):t._e()],1)],1)},s=[]},"303a":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */._tab-box[data-v-6cfd186a]{width:100%;font-size:%?26?%;position:relative;z-index:10}._tab-box .scroll-view-h[data-v-6cfd186a]{height:%?80?%;line-height:%?80?%;white-space:nowrap;width:100%;box-sizing:border-box}._tab-box .scroll-view-h ._scroll-content[data-v-6cfd186a]{width:100%;height:100%;position:relative;display:inline-block}._tab-box .scroll-view-h ._scroll-content ._tab-item-box[data-v-6cfd186a]{height:100%;display:inline-block}._tab-box .scroll-view-h ._scroll-content ._tab-item-box._flex[data-v-6cfd186a]{display:flex}._tab-box .scroll-view-h ._scroll-content ._tab-item-box._flex ._item[data-v-6cfd186a]{flex:1;padding:0 %?20?%}._tab-box .scroll-view-h ._scroll-content ._tab-item-box._clamp ._item[data-v-6cfd186a]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}._tab-box .scroll-view-h ._scroll-content ._tab-item-box ._item[data-v-6cfd186a]{height:100%;display:inline-block;text-align:center;position:relative;text-align:center;color:#333}._tab-box .scroll-view-h ._scroll-content ._tab-item-box ._item._active[data-v-6cfd186a]{color:#e54d42}._tab-box .scroll-view-h ._scroll-content ._underline[data-v-6cfd186a]{height:%?4?%;background-color:#e54d42;border-radius:%?6?%;transition:-webkit-transform .5s;transition:transform .5s;transition:transform .5s,-webkit-transform .5s;position:absolute;bottom:0}',""]),t.exports=e},"33d9":function(t,e,i){"use strict";i.r(e);var a=i("a8ad"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"33fd":function(t,e,i){"use strict";i.r(e);var a=i("5530"),n=i("25f4");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("a84e");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"07bfc4b2",null,!1,a["a"],void 0);e["default"]=r.exports},3435:function(t,e,i){var a=i("cbde");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("b2b3afce",a,!0,{sourceMap:!1,shadowMode:!1})},3480:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a={name:"u-search",props:{shape:{type:String,default:"round"},bgColor:{type:String,default:"#f2f2f2"},placeholder:{type:String,default:"请输入关键字"},clearabled:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},showAction:{type:Boolean,default:!0},actionStyle:{type:Object,default:function(){return{}}},actionText:{type:String,default:"搜索"},inputAlign:{type:String,default:"left"},disabled:{type:Boolean,default:!1},animation:{type:Boolean,default:!1},borderColor:{type:String,default:"none"},value:{type:String,default:""},height:{type:[Number,String],default:64},inputStyle:{type:Object,default:function(){return{}}},maxlength:{type:[Number,String],default:"-1"},searchIconColor:{type:String,default:""},color:{type:String,default:"#606266"},placeholderColor:{type:String,default:"#909399"},margin:{type:String},searchIcon:{type:String,default:"search"},wrapBgColor:{type:String,default:"#fff"},hideRight:{type:Boolean,default:!1}},data:function(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword:function(t){this.$emit("input",t),this.$emit("change",t)},value:{immediate:!0,handler:function(t){this.keyword=t}}},computed:{showActionBtn:function(){return!(this.animation||!this.showAction)},borderStyle:function(){return this.borderColor?"1px solid ".concat(this.borderColor):"none"}},methods:{inputChange:function(t){this.keyword=t.detail.value},clear:function(){var t=this;this.keyword="",this.$nextTick((function(){t.$emit("clear")}))},search:function(t){this.$emit("search",t.detail.value);try{uni.hideKeyboard()}catch(t){}},custom:function(){this.$emit("custom",this.keyword);try{uni.hideKeyboard()}catch(t){}},getFocus:function(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur:function(){var t=this;setTimeout((function(){t.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler:function(){this.disabled&&this.$emit("click")}}};e.default=a},"34d8":function(t,e,i){"use strict";i.r(e);var a=i("e3fa"),n=i("46f8");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("2cab");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"13a5beb4",null,!1,a["a"],void 0);e["default"]=r.exports},"3a4e":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={swipers:i("d3b3").default,uIcon:i("5b98").default,uImage:i("4c0e").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"cate-three row"},[i("v-uni-view",{staticClass:"aside"},[i("v-uni-scroll-view",{staticStyle:{height:"100%"},attrs:{"scroll-y":"true","scroll-with-animation":"true"}},[t._l(t.cateList,(function(e,a){return[i("v-uni-view",{key:a+"_0",class:"one-item sm "+(a==t.selectIndex?"active":""),on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeActive(a)}}},[i("v-uni-text",{staticClass:"name"},[t._v(t._s(e.name))]),a==t.selectIndex?i("v-uni-view",{staticClass:"active-line bg-primary"}):t._e()],1)]}))],2)],1),i("v-uni-view",{staticClass:"main bg-body"},[i("v-uni-scroll-view",{staticStyle:{height:"100%"},attrs:{"scroll-y":"true","scroll-with-animation":"true"}},[i("v-uni-view",{staticClass:"main-wrap"},[i("v-uni-view",{staticStyle:{"border-radius":"10rpx"}},[i("swipers",{attrs:{pid:4,height:"200rpx","previous-margin":"0",padding:"20rpx 0 0",radius:"10rpx"}})],1),i("v-uni-view",{staticClass:"cate-two mt20"},t._l(t.cateTwoList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"two-item bg-white mb20"},[1==e.type?i("v-uni-navigator",{staticClass:"title row-between",attrs:{"hover-class":"none",url:"/pages/goods_search/goods_search?id="+e.id+"&name="+e.name+"&type="+e.type}},[i("v-uni-text",{staticClass:"name bold sm"},[t._v(t._s(e.name))]),i("u-icon",{attrs:{name:"arrow-right"}})],1):i("v-uni-view",{staticClass:"title row-between"},[i("v-uni-text",{staticClass:"name bold sm"},[t._v(t._s(e.name))])],1),i("v-uni-view",{staticClass:"three-list row wrap"},t._l(e.sons,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"three-item column-center mb20",attrs:{"hover-class":"none",url:"/pages/goods_search/goods_search?id="+e.id+"&name="+e.name+"&type="+e.type}},[i("u-image",{attrs:{mode:"aspectFit",width:"150rpx",height:"150rpx",src:e.image}}),i("v-uni-view",{staticClass:"text mt20 xs"},[t._v(t._s(e.name))])],1)})),1)],1)})),1)],1)],1)],1)],1)},s=[]},"3c06":function(t,e,i){"use strict";var a=i("0a12"),n=i.n(a);n.a},"3c79":function(t,e,i){"use strict";var a=i("3435"),n=i.n(a);n.a},"3d01":function(t,e,i){"use strict";i.r(e);var a=i("9952"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"42b6":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={tabs:i("cfa3").default,tab:i("74fc").default,cateList:i("84fa").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"cate-one"},[i("tabs",{attrs:{active:t.selectIndex,config:{itemWidth:150}},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeShow.apply(void 0,arguments)}}},t._l(t.cateList,(function(e,a){return i("tab",{key:a,attrs:{title:e.name}},[e.isShow?i("cate-list",{attrs:{cate:e}}):t._e()],1)})),1)],1)},s=[]},"42c0":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".trigonometry[data-v-3573b013]{border-color:transparent transparent currentcolor currentcolor;border-style:solid;border-width:3px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);opacity:.8;margin:-3px %?10?% 0}.up[data-v-3573b013]{margin-top:%?1?%;-webkit-transform:rotate(135deg);transform:rotate(135deg)}.small[data-v-3573b013]{border-width:2px;margin-top:-2px}.small.up[data-v-3573b013]{margin-top:2px}",""]),t.exports=e},"46f8":function(t,e,i){"use strict";i.r(e);var a=i("c73c"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"4b0d":function(t,e,i){"use strict";var a=i("f047"),n=i.n(a);n.a},"500a":function(t,e,i){"use strict";var a=i("9991"),n=i.n(a);n.a},5196:function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("9b1b")),s=i("695b"),o=i("73e7"),r=i("8f59"),c=a(i("5bad")),d={data:function(){return{cateList:[]}},components:{},onLoad:function(t){(0,o.setTabbar)(),this.getCatrgoryFun()},onShow:function(){this.getCartNum()},onShareAppMessage:function(){var t=c.default.get("shareInfo");return{title:t.mnp_share_title,path:"pages/index/index?invite_code="+this.inviteCode,imageUrl:t.mnp_share_image}},methods:(0,n.default)((0,n.default)({},(0,r.mapActions)(["getCartNum"])),{},{getCatrgoryFun:function(){var t=this;(0,s.getCatrgory)().then((function(e){1==e.code&&(t.cateList=e.data)}))}}),computed:(0,n.default)({},(0,r.mapGetters)(["cartNum","inviteCode","appConfig"]))};e.default=d},5296:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.cate-scroll[data-v-259340ed]{height:calc(100vh - %?174?% - var(--window-bottom))}.cate-list .cate-nav[data-v-259340ed]{position:relative;border-radius:%?20?%;margin:%?20?% %?20?% 0}.cate-list .cate-nav .nav-item[data-v-259340ed]{width:20%;margin-top:%?30?%}.cate-list .cate-nav .nav-item .icon-wrap[data-v-259340ed]{border:1px solid transparent;border-radius:50%}.cate-list .cate-nav .nav-item .icon-wrap.active[data-v-259340ed]{border-color:#ff2c3c}.cate-list .cate-nav .nav-item .name[data-v-259340ed]{margin-top:%?14?%}.cate-list .cate-nav .dots[data-v-259340ed]{position:absolute;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);bottom:%?20?%;display:flex}.cate-list .cate-nav .dots .dot[data-v-259340ed]{width:%?10?%;height:%?6?%;border-radius:%?6?%;margin-right:%?10?%;background-color:rgba(255,44,60,.4)}.cate-list .cate-nav .dots .dot.active[data-v-259340ed]{width:%?20?%;background-color:#ff2c3c}.cate-list .condition[data-v-259340ed]{height:%?80?%;margin:%?20?% %?20?% 0;border-radius:%?10?%}.cate-list .condition .tag[data-v-259340ed]{flex:1}',""]),t.exports=e},5530:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uIcon:i("5b98").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{class:{"custom-image":!0,"image-round":t.round},style:[t.viewStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.error?t._e():i("v-uni-image",{staticClass:"image",attrs:{src:t.src,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoaded.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrored.apply(void 0,arguments)}}}),t.loading&&t.showLoading?i("v-uni-view",{staticClass:"loading-wrap image"},[t.useLoadingSlot?t._t("loading"):i("u-icon",{attrs:{color:"#aaa",name:"photo-fill",size:"45"}})],2):t._e(),t.error&&t.showError?i("v-uni-view",{staticClass:"error-wrap image"},[t.useErrorSlot?t._t("error"):i("u-icon",{attrs:{color:"#aaa",name:"error-circle-fill",size:"45"}}),i("v-uni-text",{staticClass:"sm"},[t._v("加载失败")])],2):t._e()],1)},s=[]},"55a2":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uImage:i("4c0e").default,swipers:i("d3b3").default,trigonometry:i("1afe").default,goodsList:i("2a9e").default,loadingFooter:i("d77c").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-scroll-view",{staticClass:"cate-scroll",attrs:{"scroll-y":"true"},on:{scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsSearchFun.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"cate-list"},[t.navList.length&&2==t.type||0==t.cate.type?i("v-uni-view",{staticClass:"cate-nav bg-white"},[i("v-uni-swiper",{style:"height:"+t.navSwiperH+"rpx;",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.swiperChange.apply(void 0,arguments)}}},t._l(t.navList,(function(e,a){return i("v-uni-swiper-item",{key:a},[i("v-uni-view",{staticClass:"nav-list row wrap"},t._l(e,(function(e,a){return i("v-uni-view",{key:a,staticClass:"nav-item column-center",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.changeCate(e)}}},[i("v-uni-view",{staticClass:"icon-wrap",class:{active:t.id==e.id}},[i("u-image",{attrs:{width:"82rpx",height:"82rpx","border-radius":"50%",src:e.image}})],1),i("v-uni-view",{staticClass:"name xs line1 u-text-center",class:{primary:t.id==e.id},staticStyle:{width:"90%"}},[t._v(t._s(e.name))])],1)})),1)],1)})),1),t.navList.length>1?i("v-uni-view",{staticClass:"dots"},t._l(t.navList,(function(e,a){return i("v-uni-view",{key:a,class:"dot "+(a==t.currentSwiper?"active":"")})})),1):t._e()],1):t._e(),i("swipers",{attrs:{pid:4,height:"267rpx","previous-margin":"0",padding:"20rpx 20rpx 0",radius:"10rpx"}}),0!==t.cate.type?i("v-uni-view",[i("v-uni-view",{staticClass:"row condition bg-white mt20"},[i("v-uni-view",{class:"tag row-center "+(t.comprehensive?"primary":""),on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onNormal.apply(void 0,arguments)}}},[t._v("综合")]),i("v-uni-view",{staticClass:"tag row-center",attrs:{"data-type":"priceSort"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onPriceSort.apply(void 0,arguments)}}},[i("v-uni-text",{class:t.priceSort?"primary":""},[t._v("价格")]),i("v-uni-view",[i("trigonometry",{attrs:{direction:"up",size:"small",color:"desc"==t.priceSort?"#FF5058":"#333"}}),i("trigonometry",{attrs:{size:"small",color:"asc"==t.priceSort?"#FF5058":"#333"}})],1)],1),i("v-uni-view",{staticClass:"tag row-center",attrs:{"data-type":"saleSort"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSaleSort.apply(void 0,arguments)}}},[i("v-uni-text",{class:t.saleSort?"primary":""},[t._v("销量")]),i("v-uni-view",[i("trigonometry",{attrs:{direction:"up",size:"small",color:"desc"==t.saleSort?"#FF5058":"#333"}}),i("trigonometry",{attrs:{size:"small",color:"asc"==t.saleSort?"#FF5058":"#333"}})],1)],1),i("v-uni-view",{staticClass:"tag row-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeType.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"icon-sm",attrs:{src:"one"===t.goodsType?"/static/images/icon_double.png":"/static/images/icon_one.png"}})],1)],1),i("v-uni-view",{staticClass:"goods"},[i("v-uni-view",{staticClass:"goods-list"},[i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"double"==t.goodsType,expression:"goodsType == 'double'"}],staticClass:"double"},[i("goods-list",{attrs:{type:"double",list:t.goodsList}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"one"==t.goodsType,expression:"goodsType == 'one'"}],staticClass:"one",staticStyle:{padding:"0 20rpx"}},[i("goods-list",{attrs:{list:t.goodsList,type:"one"}})],1)],1),i("loading-footer",{attrs:{status:t.status,"slot-empty":!0}},[i("v-uni-view",{staticClass:"column-center",staticStyle:{padding:"200rpx 0"},attrs:{slot:"empty"},slot:"empty"},[i("v-uni-image",{staticClass:"img-null",attrs:{src:"/static/images/goods_null.png"}}),i("v-uni-text",{staticClass:"lighter"},[t._v("暂无商品")])],1)],1)],1)],1):t._e()],1)],1)},s=[]},"59dc":function(t,e,i){"use strict";var a=i("697b"),n=i.n(a);n.a},"5db9":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bd06"),i("bf0f"),i("2797");var a={name:"cate-one",props:{list:{type:Array,default:function(){return[]}}},data:function(){return{selectIndex:0,cateList:[]}},methods:{changeShow:function(t){this.selectIndex=t,this.cateList[this.selectIndex].isShow=!0}},watch:{list:{immediate:!0,handler:function(t){var e=this,i=t.findIndex((function(t){return 1==t.type}));this.selectIndex=-1==i?0:i,t.forEach((function(t,i){return t.isShow=e.selectIndex==i})),this.cateList=t}}}};e.default=a},"601d":function(t,e,i){"use strict";var a=i("cbd6"),n=i.n(a);n.a},"697b":function(t,e,i){var a=i("6b27");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("6ba7ab65",a,!0,{sourceMap:!1,shadowMode:!1})},"6a0e":function(t,e,i){"use strict";i.r(e);var a=i("9335"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"6b27":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".price-format[data-v-60f6159f]{font-family:Avenir,SourceHanSansCN,PingFang SC,Arial,Hiragino Sans GB,Microsoft YaHei,sans-serif}",""]),t.exports=e},"6c39":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:"trigonometry "+("up"===this.direction?"up":"")+" "+("small"===this.size?"small":""),style:"color:"+this.color+";opacity: "+this.opacity})},n=[]},"6da3":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */uni-page-body .sort .header[data-v-8bbdb9d4]{box-sizing:border-box;height:%?94?%;border-bottom:1px solid #e5e5e5}uni-page-body .sort .header .search[data-v-8bbdb9d4]{flex:1;height:%?60?%}uni-page-body .sort .header .search uni-input[data-v-8bbdb9d4]{flex:1;height:100%}uni-page-body .sort .content[data-v-8bbdb9d4]{height:calc(100vh - %?94?% - var(--window-top) - var(--window-bottom))}',""]),t.exports=e},"6e67":function(t,e,i){"use strict";i.r(e);var a=i("42b6"),n=i("c1b1");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"ea85ed3a",null,!1,a["a"],void 0);e["default"]=r.exports},7062:function(t,e,i){"use strict";var a=i("b4aa"),n=i.n(a);n.a},"74fc":function(t,e,i){"use strict";i.r(e);var a=i("9f4a"),n=i("8ed1");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("fd7e");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"469015b5",null,!1,a["a"],void 0);e["default"]=r.exports},"783c":function(t,e,i){"use strict";i.r(e);var a=i("ac9f"),n=i("6a0e");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"34799224",null,!1,a["a"],void 0);e["default"]=r.exports},7865:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.cate-four[data-v-013bfebc]{height:calc(100vh - %?94?% - var(--window-top) - var(--window-bottom));background-color:#fff}.cate-four .aside[data-v-013bfebc]{width:%?180?%;flex:none;height:100%}.cate-four .aside .one-item[data-v-013bfebc]{position:relative;text-align:center;padding:%?26?% %?10?%}.cate-four .aside .one-item.active[data-v-013bfebc]{color:#f79c0c;font-size:%?26?%;font-weight:700}.cate-four .aside .one-item .active-line[data-v-013bfebc]{background-color:#f79c0c}.cate-four .aside .one-item .active-line[data-v-013bfebc]{position:absolute;width:%?6?%;height:%?30?%;left:%?4?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.cate-four .main[data-v-013bfebc]{height:100%;flex:1}.cate-four .main .main-wrap[data-v-013bfebc]{position:relative;padding:0 %?20?%}.cate-four .main .main-wrap .goods .item[data-v-013bfebc]{border-radius:%?14?%;overflow:hidden}.cate-four .main .main-wrap .goods .item .add[data-v-013bfebc]{width:%?46?%;height:%?46?%;line-height:%?46?%;text-align:center}.cate-four .main .main-wrap .two-item[data-v-013bfebc]{border-radius:%?10?%}.cate-four .main .main-wrap .two-item .title[data-v-013bfebc]{height:%?90?%;padding:0 %?20?%}.cate-four .main .main-wrap .two-item .title .line[data-v-013bfebc]{width:%?40?%;height:1px;background-color:#bbb}.cate-four .main .main-wrap .two-item .three-list[data-v-013bfebc]{align-items:flex-start;padding:0 %?10?%}.cate-four .main .main-wrap .two-item .three-list .three-item[data-v-013bfebc]{width:33%}.cate-four .main .main-wrap .two-item .three-list .three-item .text[data-v-013bfebc]{text-align:center}.cate-four .condition[data-v-013bfebc]{height:%?80?%;border-radius:%?10?%}.cate-four .condition .tag[data-v-013bfebc]{flex:1}.cate-four .condition .tag.active[data-v-013bfebc]{color:#f79c0c}',""]),t.exports=e},"78df":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".tab.active[data-v-469015b5]{height:auto}.tab.inactive[data-v-469015b5]{height:0;overflow:visible}",""]),t.exports=e},"7ed6":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{class:(t.lineThrough?"line-through":"")+" price-format",style:{color:t.color,"font-weight":t.weight}},[t.showSubscript?i("v-uni-text",{style:{"font-size":t.subscriptSize+"rpx","margin-right":"2rpx"}},[t._v("¥")]):t._e(),i("v-uni-text",{style:{"font-size":t.firstSize+"rpx","margin-right":"1rpx"}},[t._v(t._s(t.priceSlice.first))]),t.priceSlice.second?i("v-uni-text",{style:{"font-size":t.secondSize+"rpx"}},[t._v("."+t._s(t.priceSlice.second))]):t._e()],1)},n=[]},"84fa":function(t,e,i){"use strict";i.r(e);var a=i("55a2"),n=i("a18e");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("601d");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"259340ed",null,!1,a["a"],void 0);e["default"]=r.exports},"85e9":function(t,e,i){"use strict";var a=i("2b87"),n=i.n(a);n.a},"866a":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.cate-three[data-v-52202d9d]{height:calc(100vh - %?94?% - var(--window-top) - var(--window-bottom));background-color:#fff}.cate-three .aside[data-v-52202d9d]{width:%?180?%;flex:none;height:100%}.cate-three .aside .one-item[data-v-52202d9d]{position:relative;text-align:center;padding:%?26?% %?10?%}.cate-three .aside .one-item.active[data-v-52202d9d]{color:#ff2c3c;font-size:%?26?%;font-weight:700}.cate-three .aside .one-item .active-line[data-v-52202d9d]{position:absolute;width:%?6?%;height:%?30?%;left:%?4?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.cate-three .main[data-v-52202d9d]{height:100%;flex:1}.cate-three .main .main-wrap[data-v-52202d9d]{position:relative;padding:0 %?20?%}.cate-three .main .main-wrap .two-item[data-v-52202d9d]{border-radius:%?10?%}.cate-three .main .main-wrap .two-item .title[data-v-52202d9d]{height:%?90?%;padding:0 %?20?%}.cate-three .main .main-wrap .two-item .title .line[data-v-52202d9d]{width:%?40?%;height:1px;background-color:#bbb}.cate-three .main .main-wrap .two-item .three-list[data-v-52202d9d]{align-items:flex-start;padding:0 %?10?%}.cate-three .main .main-wrap .two-item .three-list .three-item[data-v-52202d9d]{width:33%}.cate-three .main .main-wrap .two-item .three-list .three-item .text[data-v-52202d9d]{text-align:center}',""]),t.exports=e},8718:function(t,e,i){"use strict";i.r(e);var a=i("7ed6"),n=i("33d9");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("59dc");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"60f6159f",null,!1,a["a"],void 0);e["default"]=r.exports},"881e":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'[data-v-13a5beb4]:host{font-size:0;line-height:1}.loading[data-v-13a5beb4]{display:inline-flex;align-items:center;justify-content:center;color:#c8c9cc}.loading__spinner[data-v-13a5beb4]{position:relative;box-sizing:border-box;width:%?45?%;max-width:100%;max-height:100%;height:%?45?%;-webkit-animation:rotate-data-v-13a5beb4 .8s linear infinite;animation:rotate-data-v-13a5beb4 .8s linear infinite}.loading__spinner--spinner[data-v-13a5beb4]{-webkit-animation-timing-function:steps(12);animation-timing-function:steps(12)}.loading__spinner--circular[data-v-13a5beb4]{border:%?2?% solid transparent;border-top-color:initial;border-radius:100%}.loading__text[data-v-13a5beb4]{margin-left:%?16?%;color:#969799;font-size:%?28?%;line-height:%?40?%}.loading__text[data-v-13a5beb4]:empty{display:none}.loading--vertical[data-v-13a5beb4]{-webkit-flex-direction:column;flex-direction:column}.loading--vertical .loading__text[data-v-13a5beb4]{margin:%?16?% 0 0}.loading__dot[data-v-13a5beb4]{position:absolute;top:0;left:0;width:100%;height:100%}.loading__dot[data-v-13a5beb4]:before{display:block;width:%?4?%;height:25%;margin:0 auto;background-color:currentColor;border-radius:40%;content:" "}.loading__dot[data-v-13a5beb4]:first-of-type{-webkit-transform:rotate(30deg);transform:rotate(30deg);opacity:1}.loading__dot[data-v-13a5beb4]:nth-of-type(2){-webkit-transform:rotate(60deg);transform:rotate(60deg);opacity:.9375}.loading__dot[data-v-13a5beb4]:nth-of-type(3){-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:.875}.loading__dot[data-v-13a5beb4]:nth-of-type(4){-webkit-transform:rotate(120deg);transform:rotate(120deg);opacity:.8125}.loading__dot[data-v-13a5beb4]:nth-of-type(5){-webkit-transform:rotate(150deg);transform:rotate(150deg);opacity:.75}.loading__dot[data-v-13a5beb4]:nth-of-type(6){-webkit-transform:rotate(180deg);transform:rotate(180deg);opacity:.6875}.loading__dot[data-v-13a5beb4]:nth-of-type(7){-webkit-transform:rotate(210deg);transform:rotate(210deg);opacity:.625}.loading__dot[data-v-13a5beb4]:nth-of-type(8){-webkit-transform:rotate(240deg);transform:rotate(240deg);opacity:.5625}.loading__dot[data-v-13a5beb4]:nth-of-type(9){-webkit-transform:rotate(270deg);transform:rotate(270deg);opacity:.5}.loading__dot[data-v-13a5beb4]:nth-of-type(10){-webkit-transform:rotate(300deg);transform:rotate(300deg);opacity:.4375}.loading__dot[data-v-13a5beb4]:nth-of-type(11){-webkit-transform:rotate(330deg);transform:rotate(330deg);opacity:.375}.loading__dot[data-v-13a5beb4]:nth-of-type(12){-webkit-transform:rotate(1turn);transform:rotate(1turn);opacity:.3125}@-webkit-keyframes rotate-data-v-13a5beb4{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes rotate-data-v-13a5beb4{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),t.exports=e},"885c":function(t,e,i){var a=i("881e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("262c9b88",a,!0,{sourceMap:!1,shadowMode:!1})},"8ed1":function(t,e,i){"use strict";i.r(e);var a=i("e064"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"92df":function(t,e,i){"use strict";i.r(e);var a=i("22c8"),n=i("a13b");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("7062");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"013bfebc",null,!1,a["a"],void 0);e["default"]=r.exports},9335:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bd06"),i("bf0f"),i("2797");var a={name:"cate-two",props:{list:{type:Array,default:function(){return[]}}},data:function(){return{selectIndex:0,cateList:[]}},methods:{changeShow:function(t){this.selectIndex=t,this.cateList[this.selectIndex].isShow=!0}},watch:{list:{immediate:!0,handler:function(t){var e=this,i=t.findIndex((function(t){return 1==t.type}));this.selectIndex=-1==i?0:i,t.forEach((function(t,i){return t.isShow=e.selectIndex==i})),this.cateList=t}}}};e.default=a},9952:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("bd06");var a={name:"cate-three",props:{list:{type:Array,default:function(){return[]}}},data:function(){return{selectIndex:0,cateList:[],cateTwoList:[]}},methods:{changeActive:function(t){var e=this.cateList;this.selectIndex=t,this.cateTwoList=e[this.selectIndex].sons}},watch:{list:{immediate:!0,handler:function(t){var e=t.findIndex((function(t){return 1==t.type}));this.selectIndex=-1==e?0:e,this.cateList=t,this.cateTwoList=t[this.selectIndex]?t[this.selectIndex].sons:[]}}}};e.default=a},9991:function(t,e,i){var a=i("303a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("adbff8ba",a,!0,{sourceMap:!1,shadowMode:!1})},"9f4a":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{class:{active:this.active,inactive:!this.active,tab:!0},style:this.shouldShow?"":"display: none;"},[this.shouldRender?this._t("default"):this._e()],2)},n=[]},a13b:function(t,e,i){"use strict";i.r(e);var a=i("29dd"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},a18e:function(t,e,i){"use strict";i.r(e);var a=i("cc42"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},a84e:function(t,e,i){"use strict";var a=i("fbc3"),n=i.n(a);n.a},a8ad:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("e838");var a={data:function(){return{priceSlice:{}}},components:{},props:{firstSize:{type:[String,Number],default:28},secondSize:{type:[String,Number],default:28},color:{type:String},weight:{type:[String,Number],default:400},price:{type:[String,Number],default:""},showSubscript:{type:Boolean,default:!0},subscriptSize:{type:[String,Number],default:28},lineThrough:{type:Boolean,default:!1}},created:function(){this.priceFormat()},watch:{price:function(t){this.priceFormat()}},methods:{priceFormat:function(){var t=this.price,e={};null!==t&&""!==t&&void 0!==t&&(t=parseFloat(t),t=String(t).split("."),e.first=t[0],e.second=t[1],this.priceSlice=e)}}};e.default=a},ac9f:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={tabs:i("cfa3").default,tab:i("74fc").default,cateList:i("84fa").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"cate-two"},[i("tabs",{attrs:{active:t.selectIndex,config:{itemWidth:150}},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeShow.apply(void 0,arguments)}}},t._l(t.cateList,(function(e,a){return i("tab",{key:a,attrs:{title:e.name}},[e.isShow?i("cate-list",{attrs:{type:2,cate:e}}):t._e()],1)})),1)],1)},s=[]},ad48:function(t,e,i){var a=i("0c36");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("be9187ac",a,!0,{sourceMap:!1,shadowMode:!1})},b4aa:function(t,e,i){var a=i("7865");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("1d7f1682",a,!0,{sourceMap:!1,shadowMode:!1})},bff5:function(t,e,i){"use strict";i.r(e);var a=i("0784"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},c1b1:function(t,e,i){"use strict";i.r(e);var a=i("5db9"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},c73c:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("08eb"),i("18f7");var a={props:{color:String,vertical:Boolean,type:{type:String,default:"spinner"},size:{type:Number,default:40},textSize:String},data:function(){return{array12:Array.from({length:12})}}};e.default=a},cbd6:function(t,e,i){var a=i("5296");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("6f7deb42",a,!0,{sourceMap:!1,shadowMode:!1})},cbde:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.goods-list .goods-double[data-v-c7e3d2d2]{flex-wrap:wrap;padding:0 %?20?%;align-items:stretch}.goods-list .goods-double .item[data-v-c7e3d2d2]{width:%?347?%;border-radius:%?10?%}.goods-list .goods-double .item .goods-info[data-v-c7e3d2d2]{padding:%?10?%}.goods-list .goods-hot.goods-home-hot .item[data-v-c7e3d2d2]{padding:0}.goods-list .goods-hot.goods-home-hot .item .paixu[data-v-c7e3d2d2],\n.goods-list .goods-hot.goods-home-hot .item .number[data-v-c7e3d2d2]{left:%?10?%}.goods-list .goods-hot .item[data-v-c7e3d2d2]{position:relative;padding:%?30?% %?20?%;border-radius:%?10?%}.goods-list .goods-hot .item .goods-info[data-v-c7e3d2d2]{width:%?450?%}.goods-list .goods-hot .item .goods-info .sale[data-v-c7e3d2d2]{padding:%?4?% %?18?%;color:#f79c0c;background-color:rgba(247,156,12,.1)}.goods-list .goods-hot .item .paixu[data-v-c7e3d2d2],\n.goods-list .goods-hot .item .number[data-v-c7e3d2d2]{position:absolute;top:0;left:%?27?%;width:%?50?%;height:%?54?%;line-height:%?60?%;text-align:center;color:#621e09}.goods-list .goods-one .item[data-v-c7e3d2d2]{padding:%?20?%}.goods-list .goods-one .item[data-v-c7e3d2d2]:not(:last-of-type){margin-bottom:%?20?%}.goods-list .goods-new .item[data-v-c7e3d2d2]{box-shadow:0 0 10px rgba(0,0,0,.16);border-radius:%?10?%}',""]),t.exports=e},cc42:function(t,e,i){"use strict";(function(t){i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("2634")),s=a(i("2fdc")),o=a(i("9b1b"));i("64aa"),i("c223");var r=i("8f59"),c=i("73e7"),d=i("695b"),l=i("1782"),u={props:{cate:{type:Object,default:function(){return{}}},type:{type:Number,default:1}},data:function(){return{navSwiperH:374,navList:[],currentSwiper:0,status:l.loadingType.LOADING,page:1,goodsType:"double",goodsList:[],priceSort:"",saleSort:"",id:""}},created:function(){this.onNormal=(0,c.trottle)(this.onNormal,500,this),this.onPriceSort=(0,c.trottle)(this.onPriceSort,500,this),this.onSaleSort=(0,c.trottle)(this.onSaleSort,500,this),this.getGoodsSearchFun()},watch:{cate:{immediate:!0,handler:function(e){var i=[];t.log(e),i=0===e.type?e.sons[0].sons||[]:e.sons||[],i.length<=5?this.navSwiperH=200:this.navSwiperH=374,this.id=e.id,this.navList=(0,c.arraySlice)(i)}}},computed:(0,o.default)((0,o.default)({},(0,r.mapGetters)(["appConfig"])),{},{comprehensive:function(){var t=this.priceSort,e=this.saleSort;return""==t&&""==e}}),methods:{changeCate:function(t){if(0!==this.cate.type){if(this.id==t.id)return this.id=this.cate.id,void this.onRefresh();this.id=t.id,this.onRefresh()}else uni.navigateTo({url:"/pages/goods_search/goods_search?id=".concat(t.id,"&name=").concat(t.name,"&type=").concat(t.type)})},swiperChange:function(t){this.currentSwiper=t.detail.current},changeType:function(){this.goodsType="one"===this.goodsType?"double":"one"},onNormal:function(){this.priceSort="",this.saleSort="",this.onRefresh()},onPriceSort:function(){var t=this.priceSort;this.saleSort="",this.priceSort="asc"==t?"desc":"asc",this.onRefresh()},onSaleSort:function(){var t=this.saleSort;this.priceSort="",this.saleSort="desc"==t?"asc":"desc",this.onRefresh()},onRefresh:function(){var t=this;this.showHistory=!1,this.page=1,this.goodsList=[],this.status=l.loadingType.LOADING,this.$nextTick((function(){t.getGoodsSearchFun()}))},getGoodsSearchFun:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i,a,s,o,r,u,f;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=t.page,a=t.goodsList,s=t.priceSort,o=t.saleSort,r=t.status,r!=l.loadingType.FINISHED){e.next=3;break}return e.abrupt("return");case 3:return u={category_id:t.id,page_no:i,price:s,sales_sum:o},e.next=6,(0,c.loadingFun)(d.getGoodsSearch,i,a,r,u);case 6:if(f=e.sent,f){e.next=9;break}return e.abrupt("return");case 9:t.page=f.page,t.goodsList=f.dataList,t.status=f.status;case 12:case"end":return e.stop()}}),e)})))()}}};e.default=u}).call(this,i("ba7c")["default"])},ce025:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"_tab-box",style:{fontSize:t.defaultConfig.fontSize+"rpx",color:t.defaultConfig.color}},[i("v-uni-scroll-view",{staticClass:"scroll-view-h",style:{backgroundColor:t.defaultConfig.bgColor},attrs:{id:"_scroll","scroll-x":!0,"scroll-with-animation":!0,"scroll-left":t.slider.scrollLeft}},[i("v-uni-view",{staticClass:"_scroll-content"},[i("v-uni-view",{staticClass:"_tab-item-box",class:[t.defaultConfig.itemWidth?"_clamp":"_flex"]},[t._l(t.tabList,(function(e,a){return[i("v-uni-view",{key:a+"_0",staticClass:"_item",class:{_active:t.tagIndex===a},style:{color:t.tagIndex==a?t.defaultConfig.activeColor:t.defaultConfig.color,width:t.defaultConfig.itemWidth?t.defaultConfig.itemWidth+"rpx":""},attrs:{id:"_tab_"+a},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.tabClick(a)}}},[t._v(t._s(e.title))])]}))],2),i("v-uni-view",{staticClass:"_underline",style:{transform:"translateX("+t.slider.left+"px)",width:t.slider.width+"px",height:t.defaultConfig.underLineHeight+"rpx",backgroundColor:t.defaultConfig.underLineColor}})],1)],1),i("v-uni-view",{staticClass:"tab-content"},[i("v-uni-view",[t._t("default")],2)],1)],1)},n=[]},cfa3:function(t,e,i){"use strict";i.r(e);var a=i("ce025"),n=i("1569");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("500a");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"6cfd186a",null,!1,a["a"],void 0);e["default"]=r.exports},d54a:function(t,e,i){"use strict";i.r(e);var a=i("2e3f"),n=i("165a");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("85e9");var o=i("828b"),r=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"8bbdb9d4",null,!1,a["a"],void 0);e["default"]=r.exports},dc8e:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{src:{type:String},round:Boolean,width:{type:null},height:{type:null},radius:null,lazyLoad:{type:Boolean,default:!0},useErrorSlot:Boolean,useLoadingSlot:Boolean,showMenuByLongpress:Boolean,mode:{type:String,default:"scaleToFill"},showError:{type:Boolean,default:!0},showLoading:{type:Boolean,default:!0},customStyle:{type:Object,default:function(){}}},data:function(){return{error:!1,loading:!0,viewStyle:{}}},created:function(){this.setStyle()},methods:{setStyle:function(){var t=this.width,e=this.height,i=this.radius,a={};t&&(a.width=t),e&&(a.height=e),i&&(a["overflow"]="hidden",a["border-radius"]=i),this.viewStyle=a,this.customStyle&&(this.viewStyle=Object.assign(this.viewStyle,this.customStyle))},onLoaded:function(t){this.loading=!1,this.$emit("load",t.detail)},onErrored:function(t){this.error=!1,this.loading=!0,this.$emit("error",t.detail)},onClick:function(t){this.$emit("click",t.detail)}},watch:{src:function(){this.error=!1,this.loading=!0},width:function(){this.setStyle()},height:function(){this.setStyle()}}};e.default=a},e064:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("aa9c");var a={props:{dot:{type:Boolean},info:{type:null},title:{type:String},titleStyle:{type:String},name:{type:[Number,String],value:""}},inject:["tabs"],data:function(){return{active:!1,shouldShow:!1,shouldRender:!1}},created:function(){this.tabs.childrens.push(this)},mounted:function(){this.update()},methods:{getComputedName:function(){return""!==this.data.name?this.data.name:this.index},updateRender:function(t,e){this.inited=this.inited||t,this.active=t,this.shouldRender=this.inited,this.shouldShow=t},update:function(){this.tabs&&this.tabs.updateTabs()}},computed:{changeData:function(){var t=this.dot,e=this.info,i=this.title,a=this.titleStyle;return{dot:t,info:e,title:i,titleStyle:a}}},watch:{changeData:function(t){this.update()}}};e.default=a},e128:function(t,e,i){"use strict";i.r(e);var a=i("3480"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},e3fa:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{class:"loading "+(t.vertical?"loading--vertical":"")},[i("v-uni-view",{class:"loading__spinner loading__spinner--"+t.type,style:{color:t.color,width:t.size+"rpx",height:t.size+"rpx"}},t._l(t.array12,(function(e,a){return"spinner"===t.type?i("v-uni-view",{key:a,staticClass:"loading__dot"}):t._e()})),1),i("v-uni-view",{staticClass:"loading__text",style:{"font-size":t.textSize+"rpx",color:t.color}},[t._t("default")],2)],1)},n=[]},e54d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={uIcon:i("5b98").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-search",style:{margin:t.margin,backgroundColor:t.wrapBgColor},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-content",style:{backgroundColor:t.bgColor,borderRadius:"round"==t.shape?"100rpx":"10rpx",border:t.borderStyle,height:t.height+"rpx"}},[i("v-uni-view",{staticClass:"u-icon-wrap"},[i("u-icon",{staticClass:"u-clear-icon",attrs:{size:30,name:t.searchIcon,color:t.searchIconColor?t.searchIconColor:t.color}})],1),i("v-uni-input",{staticClass:"u-input",style:[{textAlign:t.inputAlign,color:t.color,backgroundColor:t.bgColor},t.inputStyle],attrs:{"confirm-type":"search",value:t.value,disabled:t.disabled,focus:t.focus,maxlength:t.maxlength,"placeholder-class":"u-placeholder-class",placeholder:t.placeholder,"placeholder-style":"color: "+t.placeholderColor,type:"text"},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.blur.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.inputChange.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.getFocus.apply(void 0,arguments)}}}),t.keyword&&t.clearabled&&t.focused?i("v-uni-view",{staticClass:"u-close-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[i("u-icon",{staticClass:"u-clear-icon",attrs:{name:"close-circle-fill",size:"34",color:"#c0c4cc"}})],1):t._e()],1),t.hideRight?i("v-uni-view",{staticClass:"u-action",class:[t.showActionBtn||t.show?"u-action-active":""],style:[t.actionStyle],on:{click:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.custom.apply(void 0,arguments)}}},[t._v(t._s(t.actionText))]):t._e()],1)},s=[]},e62a:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa"),i("fd3c"),i("5c47"),i("bf0f"),i("2797");var a={name:"tabs",props:{active:{type:Number,default:0},config:{type:Object,default:function(){return{}}}},provide:function(){return{tabs:this}},data:function(){return{tabList:[],tagIndex:0,slider:{left:0,width:0,scrollLeft:0},scorll:{},defaultConfig:{bgColor:"#fff",fontSize:26,color:"#333",activeColor:"#FF2C3C",itemWidth:0,underLinePadding:10,underLineWidth:0,underLineHeight:4,underLineColor:"#FF2C3C"}}},watch:{},created:function(){this.childrens=[]},mounted:function(){this.updateTabs()},methods:{updateTabs:function(){var t=this;this.tabList=this.childrens.map((function(t){var e=t.title,i=t.info,a=t.name,n=t.dot,s=t.titleStyle,o=t.active,r=t.updateRender;return{title:e,info:i,name:a,dot:n,titleStyle:s,active:o,updateRender:r}})),this.updateConfig(),this.tagIndex=this.active,this.$nextTick((function(){t.calcScrollPosition()}))},updateConfig:function(){this.defaultConfig=Object.assign(this.defaultConfig,this.config)},calcScrollPosition:function(){var t=this,e=uni.createSelectorQuery().in(this);e.select("#_scroll").boundingClientRect((function(e){t.scorll=e,t.updateTabWidth()})).exec()},updateTabWidth:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,i=this.tabList;if(0==i.length)return!1;var a=uni.createSelectorQuery().in(this);a.select("#_tab_"+e).boundingClientRect((function(a){i[e]._slider={width:a.width,left:a.left,scrollLeft:a.left-(i[e-1]?i[e-1]._slider.width:0)},t.tagIndex==e&&t.tabToIndex(t.tagIndex),e++,i.length>e&&t.updateTabWidth(e)})).exec()},tabToIndex:function(t){var e=this,i=this.tabList[t]._slider,a=uni.upx2px(this.defaultConfig.underLineWidth);a||(a=this.defaultConfig.itemWidth?uni.upx2px(this.defaultConfig.itemWidth)/3:this.tabList[t]["title"].length*uni.upx2px(this.defaultConfig.fontSize),a+=2*uni.upx2px(this.defaultConfig.underLinePadding)),this.childrens.forEach((function(i,a){var n=a===t;n===i.active&&i.inited||i.updateRender(n,e)}));var n=this.scorll.left||0;this.slider={left:i.left-n+(i.width-a)/2,width:a,scrollLeft:i.scrollLeft-n}},tabClick:function(t){this.tagIndex=t,this.tabToIndex(t),this.$emit("change",t)}}};e.default=a},f047:function(t,e,i){var a=i("42c0");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("3bd44978",a,!0,{sourceMap:!1,shadowMode:!1})},f159:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={customImage:i("33fd").default,priceFormat:i("8718").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"goods-list"},["double"===t.type?i("v-uni-view",{staticClass:"goods-double row-between"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+(t.isBargain?e.goods_id:e.id)}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"347rpx",height:"347rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"347rpx",height:"347rpx",radius:"10rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"goods-name line2"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price||e.activity_price}})],1)],1)],1)})),1):t._e(),"model2"===t.type?i("v-uni-view",{staticClass:"goods-hot"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/model2_details?id="+e.id+"&order_type=5"}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"180rpx",height:"180rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"180rpx",height:"180rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-text",{staticClass:"sale br60 xxs"},[t._v("已有"+t._s(e.sales_sum)+"人购买")]),i("v-uni-view",{staticClass:"row-between  mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1),i("v-uni-image",{staticClass:"icon-md",attrs:{src:"/static/images/icon_go_red.png"}})],1)],1)],1)})),1):t._e(),"hot"===t.type?i("v-uni-view",{staticClass:"goods-hot"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"180rpx",height:"180rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"180rpx",height:"180rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-text",{staticClass:"sale br60 xxs"},[t._v("已有"+t._s(e.sales_sum)+"人购买")]),i("v-uni-view",{staticClass:"row-between  mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1),i("v-uni-image",{staticClass:"icon-md",attrs:{src:"/static/images/icon_go_red.png"}})],1)],1),i("v-uni-image",{staticClass:"paixu",attrs:{src:t.baseUrl+"/image/No."+(a<3?a:3)+".png"}}),i("v-uni-view",{staticClass:"number xxs"},[t._v(t._s(a+1))])],1)})),1):t._e(),"home-hot"===t.type?i("v-uni-view",{staticClass:"goods-home-hot goods-hot"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mb20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img"},[i("custom-image",{attrs:{"lazy-load":!0,width:"240rpx",height:"240rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20 mr20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-text",{staticClass:"sale br60 xxs"},[t._v("已有"+t._s(e.sales_sum)+"人购买")]),i("v-uni-view",{staticClass:"row-between  mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1),i("v-uni-button",{staticClass:"br60",attrs:{type:"primary",size:"xs"}},[t._v("去购买")])],1)],1),i("v-uni-image",{staticClass:"paixu",attrs:{src:t.baseUrl+"/image/No."+(a<3?a:3)+".png"}}),i("v-uni-view",{staticClass:"number"},[t._v(t._s(a+1))])],1)})),1):t._e(),"new"===t.type?i("v-uni-view",{staticClass:"goods-new"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white mt20 row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img"},[i("custom-image",{attrs:{"lazy-load":!0,width:"240rpx",height:"240rpx",radius:"10rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20 mr20 flex1"},[i("v-uni-view",{staticClass:"goods-name line2 mb20"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"row-between muted xxs "},[i("v-uni-view",{staticClass:"line-through"},[i("v-uni-text",[t._v("原价")]),i("price-format",{attrs:{"second-size":22,"first-size":22,"subscript-size":22,price:e.market_price}})],1),i("v-uni-view",[t._v(t._s(e.sales_sum)+"人购买")])],1),i("v-uni-view",{staticClass:"row-between  mt10"},[i("price-format",{attrs:{color:"#FF2C3C","first-size":38,"subscript-size":26,"second-size":26,price:e.price,weight:500}}),i("v-uni-button",{staticClass:"br60",attrs:{type:"primary",size:"xs"}},[t._v("立即抢购")])],1)],1)],1)})),1):t._e(),"one"===t.type?i("v-uni-view",{staticClass:"goods-one mt20"},t._l(t.list,(function(e,a){return i("v-uni-navigator",{key:a,staticClass:"item bg-white row",attrs:{"hover-class":"none","open-type":"navigate",url:"/pages/goods_details/goods_details?id="+e.id}},[i("v-uni-view",{staticClass:"goods-img",staticStyle:{width:"200rpx",height:"200rpx"}},[i("custom-image",{attrs:{"lazy-load":!0,width:"200rpx",height:"200rpx",radius:"6rpx","lazy-load":!0,src:e.image}})],1),i("v-uni-view",{staticClass:"goods-info ml20"},[i("v-uni-view",{staticClass:"goods-name line2 mb10"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"row-between mt10"},[i("v-uni-view",{staticClass:"price mt10 row"},[i("price-format",{staticClass:"mr10",attrs:{color:"#FF2C3C","first-size":34,"second-size":26,"subscript-size":26,price:e.price,weight:500}}),i("price-format",{staticClass:"muted",attrs:{firstSize:24,secondSize:24,"subscript-size":24,"line-through":!0,price:e.market_price}})],1)],1)],1)],1)})),1):t._e()],1)},s=[]},faf8:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */.custom-image[data-v-07bfc4b2]{position:relative;display:block;width:100%;height:100%}.custom-image.image-round[data-v-07bfc4b2]{overflow:hidden;border-radius:50%}.custom-image .image[data-v-07bfc4b2]{display:block;width:100%;height:100%}.custom-image .loading-wrap[data-v-07bfc4b2],\n.custom-image .error-wrap[data-v-07bfc4b2]{position:absolute;top:0;left:0;display:flex;flex-direction:column;align-items:center;justify-content:center;color:#969799;font-size:%?28?%;background-color:#f7f8fa}',""]),t.exports=e},fbc3:function(t,e,i){var a=i("faf8");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("967d").default;n("f8b03a56",a,!0,{sourceMap:!1,shadowMode:!1})},fc92:function(t,e,i){"use strict";i.r(e);var a=i("18ab"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},fd7e:function(t,e,i){"use strict";var a=i("1fd4"),n=i.n(a);n.a}}]);