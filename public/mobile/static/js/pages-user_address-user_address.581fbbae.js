(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user_address-user_address"],{2408:function(e,t,s){"use strict";s.d(t,"b",(function(){return a})),s.d(t,"c",(function(){return n})),s.d(t,"a",(function(){return i}));var i={uModal:s("db13").default},a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("v-uni-view",{staticClass:"user-address"},[e.hasAddress?s("v-uni-view",{staticClass:"address-list"},[s("v-uni-radio-group",{staticClass:"radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.radioChange.apply(void 0,arguments)}}},e._l(e.addressList,(function(t,i){return s("v-uni-view",{key:i,staticClass:"item bg-white mb20",attrs:{"data-id":t.id},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onSelect.apply(void 0,arguments)}}},[s("v-uni-view",{staticClass:"address"},[s("v-uni-view",{staticClass:"consignee md bold"},[e._v(e._s(t.contact)),s("v-uni-text",{staticClass:"phone ml10"},[e._v(e._s(t.telephone))])],1),s("v-uni-view",{staticClass:"lighter sm mt10"},[e._v(e._s(t.province)+" "+e._s(t.city)+" "+e._s(t.district)+"\n                        "+e._s(t.address))])],1),s("v-uni-view",{staticClass:"operation row-between"},[s("v-uni-view",[s("v-uni-radio",{staticClass:"radio row",attrs:{color:"#FF2C3C",value:t.id+"",checked:"1"==t.is_default}},[s("v-uni-text",[e._v("设为默认")])],1)],1),s("v-uni-view",{staticClass:"row-center"},[s("v-uni-view",{staticClass:"row mr20",on:{click:function(s){s.stopPropagation(),arguments[0]=s=e.$handleEvent(s),e.editAddress(t.id)}}},[s("v-uni-image",{staticClass:"icon-md mr10",attrs:{src:e.baseUrl+"/image/icon_edit.png"}}),e._v("编辑")],1),s("v-uni-view",{staticClass:"row ml20",attrs:{"data-id":t.id},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.showSurePop.apply(void 0,arguments)}}},[s("v-uni-image",{staticClass:"icon-md mr10",attrs:{src:e.baseUrl+"/image/icon_del_1.png"}}),e._v("删除")],1)],1)],1)],1)})),1)],1):s("v-uni-view",{staticClass:"no-address column-center"},[s("v-uni-image",{staticClass:"img-null mt20",attrs:{src:e.baseUrl+"/image/address_null.png"}}),s("v-uni-view",{staticClass:"sm muted"},[e._v("暂无添加地址，请添加~")])],1),s("u-modal",{attrs:{id:"delete-dialog",showCancelButton:!0,"confirm-text":"狠心删除","confirm-color":"#FF2C3C","show-title":!1},on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.delAddressFun.apply(void 0,arguments)},cancel:function(t){arguments[0]=t=e.$handleEvent(t),e.hidePop.apply(void 0,arguments)}},model:{value:e.deleteSure,callback:function(t){e.deleteSure=t},expression:"deleteSure"}},[s("v-uni-view",{staticClass:"column-center tips-dialog"},[s("v-uni-image",{staticClass:"icon-lg",attrs:{src:e.baseUrl+"/image/icon_warning.png"}}),s("v-uni-view",{staticStyle:{"margin-top":"30rpx"}},[e._v("确认删除该地址吗？")])],1)],1),s("v-uni-view",{staticClass:"footer row-between fixed bg-white"},[e.isWeixin?s("v-uni-view",{staticClass:"btn row-center bg-gray br60 mr20",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getWxAddressFun.apply(void 0,arguments)}}},[s("v-uni-image",{staticClass:"icon-lg mr10",attrs:{src:e.baseUrl+"/image/icon_wechat.png"}}),s("v-uni-text",{staticClass:"md"},[e._v("微信导入")])],1):e._e(),s("v-uni-view",{staticClass:"btn bg-primary white md row-center br60",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.addAddress.apply(void 0,arguments)}}},[e._v("新增收货地址")])],1)],1)},n=[]},"342a":function(e,t,s){"use strict";(function(e){s("6a54");var i=s("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,s("d4b5");var a=s("695c"),n=i(s("efe5f")),d=s("73e7"),r={data:function(){return{baseUrl:this.baseUrl,addressList:[],hasAddress:!0,deleteSure:!1,currentId:0,isWeixin:!0}},props:{},onLoad:function(e){this.type=e.type,this.isWeixin=(0,d.isWeixinClient)()},onShow:function(){this.getAddressListsFun()},methods:{onSelect:function(e){if(this.type){var t=e.currentTarget.dataset.id;uni.$emit("selectaddress",{id:t}),uni.navigateBack()}},addAddress:function(){uni.navigateTo({url:"/pages/address_edit/address_edit"})},editAddress:function(e){uni.navigateTo({url:"/pages/address_edit/address_edit?id=".concat(e)})},getAddressListsFun:function(){var e=this;(0,a.getAddressLists)().then((function(t){1==t.code&&t.data.length?(e.addressList=t.data,e.hasAddress=!0):e.hasAddress=!1}))},radioChange:function(t){var s=this,i=t.detail.value;e.log(t),(0,a.setDefaultAddress)(i).then((function(e){1==e.code&&s.getAddressListsFun()}))},onLoadFun:function(){this.getAddressListsFun()},delAddressFun:function(e){var t=this,s=this.currentId;(0,a.delAddress)(s).then((function(e){1==e.code&&(t.$toast({title:e.msg}),t.deleteSure=!1,t.getAddressListsFun())}))},getWxAddressFun:function(){n.default.getWxAddress().then((function(e){uni.setStorageSync("wxAddress",JSON.stringify(e)),setTimeout((function(){uni.navigateTo({url:"/pages/address_edit/address_edit"})}),200)}))},showSurePop:function(e){this.deleteSure=!0,this.currentId=e.currentTarget.dataset.id},hidePop:function(e){this.deleteSure=!1}}};t.default=r}).call(this,s("ba7c")["default"])},5087:function(e,t,s){var i=s("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* pages/user_address/user_address.wxss */.user-address[data-v-8d1007a6]{padding-bottom:calc(%?140?% + env(safe-area-inset-bottom))}.user-address .no-address[data-v-8d1007a6]{padding-top:%?300?%;text-align:center}.user-address .address-list[data-v-8d1007a6]{padding:%?10?% 0}.user-address .address-list .item[data-v-8d1007a6]{padding:0 %?30?%}.user-address .address-list .item .address[data-v-8d1007a6]{padding:%?20?% 0;border-bottom:1px solid #e5e5e5}.user-address .address-list .item .operation[data-v-8d1007a6]{height:%?80?%}.user-address .footer[data-v-8d1007a6]{position:fixed;left:0;right:0;bottom:0;height:%?118?%;padding:0 %?30?%;box-sizing:initial;padding-bottom:env(safe-area-inset-bottom)}.user-address .footer .btn[data-v-8d1007a6]{flex:1;height:%?80?%}.tips-dialog[data-v-8d1007a6]{height:%?230?%;width:100%}',""]),e.exports=t},"6c32":function(e,t,s){"use strict";s.r(t);var i=s("342a"),a=s.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return i[e]}))}(n);t["default"]=a.a},"9ab1":function(e,t,s){"use strict";var i=s("e755"),a=s.n(i);a.a},d7d3:function(e,t,s){"use strict";s.r(t);var i=s("2408"),a=s("6c32");for(var n in a)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return a[e]}))}(n);s("9ab1");var d=s("828b"),r=Object(d["a"])(a["default"],i["b"],i["c"],!1,null,"8d1007a6",null,!1,i["a"],void 0);t["default"]=r.exports},e755:function(e,t,s){var i=s("5087");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=s("967d").default;a("6e4bd862",i,!0,{sourceMap:!1,shadowMode:!1})}}]);